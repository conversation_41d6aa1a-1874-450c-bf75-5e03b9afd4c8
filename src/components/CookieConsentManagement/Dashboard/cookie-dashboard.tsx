import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, Cookie, Globe, Shield } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Badge } from '../../../@/components/ui/badge';
import { Switch } from '../../../@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../@/components/ui/tabs';
import httpClient from '../../../api/httpClientNew';
import { RootState } from '../../../redux/store';
import { ICookieDetails } from '../../../types/cookie-consent-management';
import DonutWithText from '../../common/Charts/donut-with-text';
import {
  convertDateToHumanView,
  generateDonutChartColors,
} from '../../common/CommonHelperFunctions';
import { ChartCardLoader } from '../../common/LoadingUI';
import { FETCH_COOKIES_BY_CATEGORY, GET_COOKIE_DICTIONARY } from '../../common/api';
import {
  cookie_additional_matrix,
  cookie_dashboard_card_data,
  get_geographic_distribution,
  get_monthly_status_count_data,
} from '../../common/services/cookie-consent-management';
interface CookieMatrixDataType {
  active_domains?: number;
  banner_views?: number;
  preference_updates?: number;
}

interface CookieDashBoardDataType {
  customer_id: number;
  total_cookies: number;
  third_party_cookies: number;
}
interface DashboardProp {
  selectedDomainId: number;
  selectedDomainName: string;
}

export function CookieDashboard({ selectedDomainId, selectedDomainName }: DashboardProp) {
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const { t } = useTranslation();

  const { data: cookieCategoryData, isLoading: isCookieCategoryDataLoading } = useQuery({
    queryKey: ['cookie_category_data', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await httpClient.get(
        `${FETCH_COOKIES_BY_CATEGORY}?customer_id=${customer_id}&domain_id=${selectedDomainId}`
      );
      return response?.data?.data?.result;
    },
    enabled: !!customer_id && selectedDomainId >= 0,
  });

  const colors = generateDonutChartColors(cookieCategoryData?.length);

  const cookieCategoryDataVales = cookieCategoryData?.map((item: any, index: number) => {
    return {
      key: item?.category_name,
      value: item?.cookie_count,
      fill: colors[index],
    };
  });

  const dynamicConfig =
    cookieCategoryDataVales?.reduce((config: any, item: any) => {
      const key = item.key?.toLowerCase().replace(/\s+/g, '_') || 'unknown';
      config[key] = {
        label: item.key,
        color: item.fill,
      };
      return config;
    }, {}) || {};

  const cookieCategoryDataToUse = {
    config: dynamicConfig,
    data: cookieCategoryDataVales || [],
  };

  const { data: cookieMatrixData, isLoading: loadingCookieMatrix } = useQuery({
    queryKey: ['cookieMatrix', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await cookie_additional_matrix(customer_id, selectedDomainId);
      return response?.result?.data;
    },
    enabled: !!customer_id,
  });

  const { data: cookieDashBoardData, isLoading: loadingCardData } = useQuery({
    queryKey: ['cookieDashboardCards', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await cookie_dashboard_card_data(customer_id, selectedDomainId);
      return response?.result?.data;
    },
    enabled: !!customer_id,
  });

  const { data: consentTrends, isLoading: loadingMonthlyInteraction } = useQuery({
    queryKey: ['consentTrendline', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await get_monthly_status_count_data(customer_id, selectedDomainId);
      return response?.result?.data;
    },
    enabled: !!customer_id,
  });

  // Compliance metrics
  const complianceMetrics = [
    { framework: 'GDPR', score: 94, status: 'Compliant' },
    { framework: 'CCPA', score: 89, status: 'Compliant' },
    { framework: 'LGPD', score: 86, status: 'Review Needed' },
    { framework: 'PIPEDA', score: 92, status: 'Compliant' },
  ];

  const { data: geographicData } = useQuery({
    queryKey: ['geographicData', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await get_geographic_distribution(customer_id, selectedDomainId);
      return response?.result?.data;
    },
    enabled: !!customer_id,
  });

  return (
    <div className="space-y-6 pb-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-2">
        {loadingCardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="mb-1 text-sm font-medium text-gray-600">Total Cookies</p>
                </div>
                <div className={`rounded-lg bg-blue-50 p-3`}>
                  <Cookie className={`h-6 w-6 text-blue-500`} />
                </div>
              </div>
              <div>
                <p className="mb-2 text-3xl font-bold">{cookieDashBoardData?.total_cookies}</p>
                <p className="text-xs text-gray-500">{`Across ${selectedDomainName ? selectedDomainName : 'all domains'}`}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {loadingCardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="mb-1 text-sm font-medium text-gray-600">Third-Party</p>
                </div>
                <div className={`rounded-lg bg-orange-50 p-3`}>
                  <Globe className={`h-6 w-6 text-orange-500`} />
                </div>
              </div>
              <div>
                <p className="mb-2 text-3xl font-bold">
                  {cookieDashBoardData?.third_party_cookies}
                </p>
                <p className="text-xs text-gray-500">External cookies</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Cookies by Category Chart */}
            {isCookieCategoryDataLoading ? (
              <div className="flex items-center justify-center">
                <ChartCardLoader
                  title="Loading..."
                  height="h-96"
                  chartType="bar"
                  showLegend={true}
                />
              </div>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Cookies by Category</CardTitle>
                  <CardDescription>
                    Distribution of cookies across different categories
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center justify-center">
                    {cookieCategoryDataToUse?.data?.length > 0 ? (
                      <>
                        <DonutWithText
                          chartConfig={cookieCategoryDataToUse?.config}
                          chartData={cookieCategoryDataToUse?.data}
                          chartSubtext="Total Cookies"
                          totalValue={cookieCategoryDataToUse.data.reduce(
                            (sum: number, item: any) => sum + item.value,
                            0
                          )}
                        />
                        <div className="flex flex-wrap justify-evenly gap-3">
                          {cookieCategoryData?.map((item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                className="flex max-w-full items-center gap-1.5 whitespace-nowrap text-sm"
                              >
                                <div
                                  className="size-2 shrink-0 rounded-[2px]"
                                  style={{ backgroundColor: colors[index] }}
                                />
                                <span className="max-w-[120px] truncate">
                                  {item?.category_name}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </>
                    ) : (
                      <></>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Consent Interaction Trends */}
            {loadingMonthlyInteraction ? (
              <div className="flex items-center justify-center">
                <ChartCardLoader
                  title="Loading..."
                  height="h-96"
                  chartType="bar"
                  showLegend={true}
                />
              </div>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Consent Interaction Trends</CardTitle>
                  <CardDescription>Monthly consent acceptance and decline trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={consentTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="grant"
                        stroke="#10b981"
                        strokeWidth={3}
                        name="Accepted"
                      />
                      <Line
                        type="monotone"
                        dataKey="declined"
                        stroke="#ef4444"
                        strokeWidth={3}
                        name="Declined"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Additional Metrics */}
          {loadingCookieMatrix ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-72" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Additional Metrics</CardTitle>
                <CardDescription>Key performance indicators and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mt-3 flex items-center justify-around">
                  {cookieMatrixData?.active_domains ? (
                    <div className="text-center">
                      <div className="mb-1 text-2xl font-bold">
                        {cookieMatrixData?.active_domains}
                      </div>
                      <div className="mb-1 text-sm text-gray-600">Active Domains</div>
                    </div>
                  ) : (
                    <></>
                  )}
                  <div className="text-center">
                    <div className="mb-1 text-2xl font-bold">{cookieMatrixData?.banner_views}</div>
                    <div className="mb-1 text-sm text-gray-600">Banner Views</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-1 text-2xl font-bold">
                      {cookieMatrixData?.preference_updates}
                    </div>
                    <div className="mb-1 text-sm text-gray-600">Preference Updates</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-1">
            <Card>
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>Consent rates by geographic region</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {geographicData?.map((region: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{region.continent}</div>
                        <div className="text-sm text-gray-600">{region.consents} consents</div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{region.percentage}%</div>
                        <div className="mt-1 h-2 w-24 rounded-full bg-gray-200">
                          <div
                            className="h-2 rounded-full bg-blue-500"
                            style={{ width: `${region.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Status</CardTitle>
              <CardDescription>Regulatory compliance across different frameworks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {complianceMetrics.map((compliance, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex items-center space-x-4">
                      <Shield className="h-8 w-8 text-blue-600" />
                      <div>
                        <h4 className="font-medium">{compliance.framework}</h4>
                        <p className="text-sm text-gray-600">
                          Compliance Score: {compliance.score}%
                        </p>
                      </div>
                    </div>
                    <Badge variant={compliance.status === 'Compliant' ? 'default' : 'secondary'}>
                      {compliance.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
