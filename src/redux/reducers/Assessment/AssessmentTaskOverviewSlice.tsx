import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { revertAll } from '../../ResetRedux/RevertAll';

// interface CommonObject {
//   id: number;
//   name: string;
// }

interface entitiesData {
  id: number;
  name: string;
  parent_id: number;
  customer_id: number;
  user_id: number | null;
  spoc_id: number;
  status: string;
  createdAt: Date | string | null;
  updatedAt: Date | string | null;
  deletedAt: Date | string | null;
}

interface User {
  id: number;
  name: string;
}

interface Process {
  id: number;
  name: string;
}

interface Assessment {
  id: number;
  type: string;
  assessment_name: string;
  key: string;
}

interface Department {
  id: number;
  name: string;
}

interface AssessmentTaskOverviewTable {
  id: number;
  assessment_id: number;
  risks: string | null;
  progress: number | null;
  start_date: string | null;
  end_date: string | null;
  tentative_date: string | null;
  status: string;
  Assessment: Assessment;
  Department: Department | null;
  Process?: Process | null;
  isCollaborator?: boolean;
  isAssigned?: boolean;
  AssignedTo: User;
  Approver: User;
  Owner: User;
  Group: Department;
}

interface Pagination {
  count: number;
  page: number;
  itemsPerPage: number;
}

interface TaskOverviewState {
  reloadTaskOverviewData: boolean;
  selectedNavItem: string;
  rowData: AssessmentTaskOverviewTable[];
  entities: entitiesData[];
  // selectedRowData: AssessmentTaskOverviewTable;
  pagination: Pagination;
  entity_name: string;
  loading: boolean;
  user_type: string;
  error: string | null;
  selectedEntityId: number;
}

// const dummyData = [
//   {
//       "id": 4,
//       "assessment_id": 1,
//       "risks": null,
//       "progress": 0,
//       "start_date": null,
//       "end_date": null,
//       "status": "Yet to Start",
//       "Assessment": {
//           "id": 1,
//           "type": "impact_assesment",
//           "assessment_name": "Privacy Impact Assessment",
//           "key": "pia"
//       },
//       "Department": {
//           "id": 38,
//           "name": "Business Development"
//       },
//       "Process": null,
//       "AssignedTo": {
//           "id": 681,
//           "name": "Swati Chandel"
//       },
//       "Approver": {
//           "id": 674,
//           "name": "Priya Rastogi"
//       },
//       "Owner": {
//           "id": 625,
//           "name": "Himanshu Gautam"
//       },
//       "Group": {
//           "id": 912,
//           "name": "AV-India"
//       }
//   },
//   {
//       "id": 5,
//       "assessment_id": 1,
//       "risks": null,
//       "progress": null,
//       "start_date": null,
//       "end_date": null,
//       "status": "Yet to Start",
//       "Assessment": {
//           "id": 1,
//           "type": "impact_assesment",
//           "assessment_name": "Privacy Impact Assessment",
//           "key": "pia"
//       },
//       "Department": {
//           "id": 38,
//           "name": "Business Development"
//       },
//       "Process": null,
//       "AssignedTo": {
//           "id": 681,
//           "name": "Swati Chandel"
//       },
//       "Approver": {
//           "id": 674,
//           "name": "Priya Rastogi"
//       },
//       "Owner": {
//           "id": 625,
//           "name": "Himanshu Gautam"
//       },
//       "Group": {
//           "id": 912,
//           "name": "AV-India"
//       }
//   }
// ];

const initialState: TaskOverviewState = {
  reloadTaskOverviewData: false,
  selectedNavItem: 'ACTIVITY',
  rowData: [],
  // selectedRowData: {
  // id: -1,
  // assessment_id: -1,
  // risks: null,
  // progress: null,
  // start_date: null,
  // end_date: null,
  // status: "",
  // Assessment: {id: -1, type: "", assessment_name: "", key: ""},
  // Department: null,
  // Process: null,
  // isCollaborator: false,
  // AssignedTo: {id: -1, name: ""},
  // Approver: {id: -1, name: ""},
  // Owner: {id: -1, name: ""},
  // Group: {id: -1, name: ""},
  // },
  pagination: {
    count: 0,
    page: 1,
    itemsPerPage: 10,
  },
  entities: [],
  selectedEntityId: -1,
  entity_name: '',
  loading: false,
  user_type: '',
  error: null,
};

const AssessmentTaskOverviewSlice = createSlice({
  name: 'assessmentTaskOverview',
  initialState,
  extraReducers: (builder) => builder.addCase(revertAll, () => initialState),
  reducers: {
    setReloadTaskOverviewData(state, action: PayloadAction<boolean>) {
      state.reloadTaskOverviewData = action.payload;
    },
    setSelectedNavItem(state, action: PayloadAction<string>) {
      state.selectedNavItem = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setUserType(state, action: PayloadAction<string>) {
      state.user_type = action.payload;
    },
    setEntities(state, action: PayloadAction<entitiesData[]>) {
      state.entities = action.payload;
    },
    setSelectedEntityId(state, action: PayloadAction<number>) {
      state.selectedEntityId = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    setAssessmentTaskOverviewTableData(
      state,
      action: PayloadAction<AssessmentTaskOverviewTable[]>
    ) {
      state.rowData = action.payload;
    },
    // setSelectedRowDetails(state, action: PayloadAction<AssessmentTaskOverviewTable>) {
    //   state.selectedRowData = action.payload;
    // },
    setSelectedEntityName(state, action: PayloadAction<string>) {
      state.entity_name = action.payload;
    },
    setAssessmentViewAuditLogPaginationCount(state, action: PayloadAction<number>) {
      state.pagination.count = action.payload;
    },
    setAssessmentViewAuditLogPaginationPage(state, action: PayloadAction<number>) {
      state.pagination.page = action.payload;
    },
    setAssessmentViewAuditLogPaginationItemsPerPage(state, action: PayloadAction<number>) {
      state.pagination.itemsPerPage = action.payload;
    },
  },
});

export const {
  setReloadTaskOverviewData,
  setSelectedNavItem,
  setAssessmentTaskOverviewTableData,
  // setSelectedRowDetails,
  setSelectedEntityName,
  setAssessmentViewAuditLogPaginationCount,
  setAssessmentViewAuditLogPaginationPage,
  setAssessmentViewAuditLogPaginationItemsPerPage,
  setLoading,
  setError,
  setUserType,
  setEntities,
  setSelectedEntityId,
} = AssessmentTaskOverviewSlice.actions;

export const AssessmentTaskOverviewActions = AssessmentTaskOverviewSlice.actions;
export default AssessmentTaskOverviewSlice;
