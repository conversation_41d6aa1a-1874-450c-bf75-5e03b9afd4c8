// import { base_api, go_trust_base_api } from "../../app/Constant";
// import { ucm_gotrust_basi_api } from "../../utils/helperData";
// import { base_api, go_trust_base_api } from "../../utils/helperData";

export const copilotKey = import.meta.env.VITE_APP_DPO_COPILOT_KEY;

export const automationURL = import.meta.env.VITE_APP_AI_AUTOMATION_BASE_URL;

export const base_api = import.meta.env.VITE_APP_GO_TRUST_BASE_API;
export const go_trust_base_api = import.meta.env.VITE_APP_GO_TRUST_BASE_API;
export const universal_gotrust_basi_api = import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API;
export const ucm_gotrust_basi_api = import.meta.env.VITE_APP_GO_TRUST_UCM_BASE_API;
export const sample_file_base_URL = import.meta.env.VITE_APP_SAMPLE_FILE_DOWNLOAD_URL;
export const clickhouse_endpoint = import.meta.env.VITE_APP_UNSTRUCTURED;
export const dd_file_classifier_endpoint = import.meta.env.VITE_APP_STRUCTURED;

export const DD_INGESTION_ENDPOINT = import.meta.env.VITE_APP_INGESTION_URL;
export const DD_WAREHOUSE_ENDPOINT = import.meta.env.VITE_APP_WAREHOUSE_URL;
export const DD_PROFILER_ENDPOINT = import.meta.env.VITE_APP_PROFILER_URL;

export const COMMON_ENDPOINT = `${go_trust_base_api}/api/v1`;
export const COMMON_ENDPOINT_V2 = `${go_trust_base_api}/api/v2`;
export const COMMON_ENDPOINT_V3 = `${go_trust_base_api}/api/v3`;
export const COMMON_ENDPOINT_CLICKHOUSE = `${clickhouse_endpoint}/`;
export const COMMON_DOWNLOAD_ENDPOINT = `${sample_file_base_URL}/api/v1/buckets/gt-sample/objects/download`;
// api/v1/buckets/gt-sample/objects/download

// sample download urls
export const DOWNLOAD_ROPA_SAMPLE_FILE = `${COMMON_DOWNLOAD_ENDPOINT}?prefix=ropa-control-sample.csv`;
export const DOWNLOAD_ASSESSMENT_SAMPLE_FILE = `${COMMON_DOWNLOAD_ENDPOINT}/?prefix=assement-control-sample.csv`;
export const DOWNLOAD_ROPA_AUTOFILL_FILE = `${COMMON_DOWNLOAD_ENDPOINT}/?prefix=ropa-filled-sample.csv`;
export const UPLOAD_ASSESSMENT_AI__ATTACHMENT = `${base_api}/api/v1/assessments/automate-assessment/`;
export const CATEGORY_CHANGE_ASSESSMENT_GLOBALLY = `${go_trust_base_api}/api/v1/assessments/change-category`;

//data visualization configurations
export const GET_ALL_DASHBOARDS_URL = `${COMMON_ENDPOINT}/grafana-dashboard-list`;
export const DASHBOARD_URL = `${COMMON_ENDPOINT}/grafana-dashboard`;
export const GET_DASHBOARD_URL_BY_NAME = `${COMMON_ENDPOINT}/grafana-details`;

export const AUTH_LOGOUT = `${base_api}/api/v1/auth/logout`;
export const GET_ROLE_LIST = `${base_api}/api/v1/role/list`;
export const GET_ROLE_RESOURCE_LIST = `${base_api}/api/v1/resources/user`;
export const GET_CUSTOMER_MANAGEMENT_LIST = `${base_api}/api/v1/customer/list`;
export const GET_CUSTOMER_DETAILS = `${base_api}/api/v1/customer`;
export const SEND_REGULATIONS_LIST = `${base_api}/api/v1/regulations/complai`;
export const SEND_CREATE_USER_DATA = `${base_api}/api/v1/user`;
export const GET_USER_ROLE_ACESS_LIST = `${base_api}/api/v1/role`;
export const FETCH_SERVICES_DATA = `${base_api}/api/v1/services`;
export const FETCH_MATURITY_DATA = `${base_api}/api/v1/ambitions`;
export const FETCH_RESULT_DATA = `${base_api}/api/v1/packages`;
export const SEND_ONBOARDING_DATA = `${base_api}/api/v1/onboarding/`;
export const FETCH_DEPT_POLICY = `${go_trust_base_api}/api/v1/departments`;
export const FETCH_INDUSTRY_VERTICAL = `${go_trust_base_api}/api/v1/industry-vertical`;
export const FETCH_QUESTIONS = `${go_trust_base_api}/api/v1/questionnaies?industry_vertical_id=`;
export const SEND_ANSWERS = `${go_trust_base_api}/api/v1/answers`;
export const FETCH_COUNTRIES = `${base_api}/api/v1/country`;
export const FETCH_STATE = `${base_api}/api/v1/state`;
export const SEND_COMPANY_STRUCTURE_DATA = `${base_api}/api/v1/groups`;
export const UPDATE_DEPARTMENT_DATA = `${go_trust_base_api}/api/v1/departments`;
export const UPDATE_PROCESS_DATA = `${go_trust_base_api}/api/v1/processes`;
export const FETCH_ROLE_MANAGEMENT_LIST = `${base_api}/api/v1/role/list`;
export const FETCH_GROUP_LISTING = `${base_api}/api/v1/groups/company-structure`;
export const FETCH_SIDEBAR = `${base_api}/api/v1/sidebar`;
export const SEND_ACCESS = `${base_api}/api/v1/resources`;
export const SEND_COMPANY_STRUCTURE_DEPARTMENT = `${go_trust_base_api}/api/v1/departments`;
export const SEND_COMPANY_STRUCTURE_BUSSINESS_UNIT = `${base_api}/api/v1/business-units`;
export const SEND_COMPANY_STRUCTURE_PROCESS = `${go_trust_base_api}/api/v1/processes`;
export const POLICY_ADD_COLLABORATOR = `${go_trust_base_api}/api/v1/policy/collaborator/`;
export const DELETE_DEPARTMENT = `${base_api}/api/v1/departments`;
export const DELETE_PROCESS = `${base_api}/api/v1/processes`;
export const DELETE_BUSINESS_UNIT = `${base_api}/api/v1/business-units`;
export const RESET_PASSWORD = `${base_api}/api/v1/resend-credentials`;
export const FETCH_USER_MANAGEMENT_AUDIT = `${base_api}/api/v1/account-setup/audit-log`;
export const FETCH_CUSTOMER_MANAGEMENT_AUDIT_LOG = `${base_api}/api/v1/audit-log`;

export const COMMON_ROPA_ENDPOINT = `${go_trust_base_api}/api/v1/ropa`;
export const FETCH_ROPA_CATEGORY_LIST = `${go_trust_base_api}/api/v1/ropa/categories/`;
export const COLLABORATOR_PROGRESS = `${go_trust_base_api}/api/v1/ropa/categories-progress/`;
export const FETCH_ROPA_CONTROL_LIST_BY_CATEGORY = `${go_trust_base_api}/api/v1/ropa/controls`;
export const FETCH_ROPA_CONTROL_FIELD = `${go_trust_base_api}/api/v1/ropa/controls/fields`;
export const ADD_ANSWER = `${go_trust_base_api}/api/v1/ropa/answer`;
export const DELETE_DOCUMENT_ROPA = `${go_trust_base_api}/api/v1/control/delete-document`;
export const DELETE_DOCUMENT_POLICY = `${go_trust_base_api}/api/v1/policy/delete-document`;
export const DOWNLOAD_DOCUMENT_POLICY = `${go_trust_base_api}/api/v1/policy/download-document`;
export const UPDATE_QUESTION = `${go_trust_base_api}/api/v1/ropa/controls/`;
export const UPDATE_ANWER_DOCUMENT = `${go_trust_base_api}/api/v1/ropa/answer/upload-document/`;
export const ROPA_PROGRESSBAR = `${go_trust_base_api}/api/v1/ropa/progress/`;
export const BULK_ROPA_UPLOAD = `${go_trust_base_api}/api/v1/ropa/department/bulk-ropa/upload`;
export const CATEGORY_CHANGE_GLOBALLY = `${go_trust_base_api}/api/v1/ropa/change-category`;

export const FETCH_POLICY_CATEGORY = `${go_trust_base_api}/api/v1/policy-category`;
export const FETCH_RELEVENT_LAW = `${go_trust_base_api}/api/v1/relevant-law`;
export const FETCH_REVIEWER_DATA = `${base_api}/api/v1/user/list`;
export const FETCH_LANGUAGES = `${base_api}/api/v1/language?page=1&size=200`;
export const FETCH_DEPARTMENTS = `${go_trust_base_api}/api/v1/departments/`;
export const FETCH_ENTITIES = `${base_api}/api/v1/entities/`;
export const ENABLE_ROPA = `${go_trust_base_api}/api/v1/ropa/restart/`;
export const SEND_NEW_POLICY_DATA = `${go_trust_base_api}/api/v1/policy`;
export const FETCH_POLICY_DASHBOARD_STATUS = `${go_trust_base_api}/api/v1/policy-dashboard/policy`;
export const FETCH_POLICY_AUTHOR = `${go_trust_base_api}/api/v1/policy-dashboard/author`;
export const FETCH_POLICY_APPROVER = `${go_trust_base_api}/api/v1/policy-dashboard/approver`;
export const FETCH_POLICY_REVIEWER = `${go_trust_base_api}/api/v1/policy-dashboard/reviewer`;
export const FETCH_POLICY_DEPARTMENT = `${go_trust_base_api}/api/v1/policy-dashboard/department`;
export const FETCH_POLICY_ENTITIES = `${go_trust_base_api}/api/v1/policy-dashboard/entity`;
export const VIEW_GROUP_DETAILS = `${base_api}/api/v1/user/list`;
// export const CHANGE_PASSWORD = `${base_api}/api/v1/change-password`;
export const CHANGE_PASSWORD = `${base_api}/api/v2/change-password`;
export const USER_PROFILE = `${base_api}/api/v1/profile`;
export const ABOUT_US = `${base_api}/api/v1/about-us/GoTrust`;
export const ROPA_LEVEL = `${go_trust_base_api}/api/v1/ropa-dashboard-level`;
export const FETCH_ROPA_DASHBOARD_DEPT = `${go_trust_base_api}/api/v1/ropa-dashboard`;
export const FETCH_ROPA_DASHBOARD_COMPLETION = `${go_trust_base_api}/api/v1/ropa-dashboard-department-completion`;
export const FETCH_ROPA_DASHBOARD_COLLABORATOR = `${go_trust_base_api}/api/v1/ropa-dashboard-assignee`;
export const FETCH_ROPA_DASHBOARD_LAW = `${go_trust_base_api}/api/v1/ropa-organization-role`;
export const FETCH_ROPA_DASHBOARD_THIRD_PARTY = `${go_trust_base_api}/api/v1/ropa/third-party`;
export const FETCH_ROPA_DASHBOARD_PERSONAL_DATA = `${go_trust_base_api}/api/v1/ropa/peronal-data`;
export const FETCH_ROPA_DASHBOARD_PROGRESS = `${go_trust_base_api}/api/v1/ropa-dashboard-progress`;
export const FETCH_ROPA_DASHBOARD_STATUS = `${go_trust_base_api}/api/v1/ropa-dashboard-status`;
export const FETCH_ROPA_DASHBOARD_DEPARTMENT = `${go_trust_base_api}/api/v1/ropa-dashboard-department`;
export const FETCH_ROPA_DASHBOARD_DATA_SYSTEM = `${go_trust_base_api}/api/v1/ropa/data-system`;
export const FETCH_ROPA_DASHBOARD_ORG_ROLE = `${go_trust_base_api}/api/v1/ropa/organisation-role`;
export const FETCH_ROPA_DASHBOARD_DPIA = `${go_trust_base_api}/api/v1/ropa/dpia-requirement`;
export const FETCH_ROPA_BASIC_INFO = `${go_trust_base_api}/api/v1/ropa/basic-info`;
export const UPLOAD_ROPA_AI__ATTACHMENT = `${base_api}/api/v2/ropa/automate-ropa/`;
export const SEND_ROPA_BASIC_INFO_ANSWERS = `${go_trust_base_api}/api/v1/ropa/basic-info/answer/`;
export const UPLOAD_PROFILE = `${base_api}/api/v1/upload-profile`;
export const ROPA_SUBMIT = `${go_trust_base_api}/api/v1/ropa/submit/`;
export const DELETE_QUESTION = `${go_trust_base_api}/api/v1/ropa/controls/`;
export const ROPA_ACTIVITY = `/privacy/ropa/`;
export const ROPA_ADD_COLLABORATOR = `${go_trust_base_api}/api/v1/ropa/collaborator`;
export const STORE_REVIEW = `${go_trust_base_api}/api/v1/ropa/review/`;
export const ALL_TICKETS = `${base_api}/api/v1/tickets`;
export const TICKET_DETAILS = `${base_api}/api/v1/ticket`;
export const ADD_COMMENT = `${base_api}/api/v1/ticket/comment`;
export const TICKET_PRIORITY = `${base_api}/api/v1/tickets-priority`;
export const UPLOAD_ATTACHMENT = `${base_api}/api/v1/create-ticket`;
export const SUPPORT_DASHBOARD = `${base_api}/api/v1/ticket-dashboard`;
export const SUPPORT_TICKET_HISTORY = `${base_api}/api/v1/ticket/history`;
export const NORMAL_USER_TODO = `${base_api}/api/v1/ticket/to-do`;
export const FETCH_POLICY_LIST = `${go_trust_base_api}/api/v1/ropa/policy-list`;
export const FETCH_ROPA_AUDIT_LOG = `${go_trust_base_api}/api/v1/ropa/audit-log`;
export const ROPA_REVIEW_SUBMIT = `${go_trust_base_api}/api/v1/ropa/review/submit/`;
export const ROPA_DOWNLOAD = `${go_trust_base_api}/api/v1/ropa/download-controls`;
export const ROPA_TASK_OVERVIEW_LIST = `${go_trust_base_api}/api/v2/ropa/list`;
export const UPLOAD_ROPA_FILE = `${go_trust_base_api}/api/v1/ropa/answer/upload-document/`;
export const ROPA_UPDATE = `${go_trust_base_api}/api/v1/ropa/update/`;
export const IMPORT_ROPA_FILE = `${go_trust_base_api}/api/v1/ropa/upload-controls`;
export const CHANGE_ROPA_TENTATIVE_DATE = `${go_trust_base_api}/api/v1/ropa/tentative-date/`;
export const CHANGE_ROPA_RECURRENCE_DATE = `${go_trust_base_api}/api/v1/ropa/recurrence-date/`;
export const CHANGE_ASSESSMENT_RECURRENCE_DATE = `${go_trust_base_api}/api/v1/assessments/recurrence-date/`;
export const CHANGE_VRM_RECURRENCE_DATE = `${go_trust_base_api}/api/v1/vrm/via/recurrence/`;
export const UPDATE_ROPA_ALREADY_PERFORMED = `${go_trust_base_api}/api/v1/ropa/upload-ropa`;
export const ROPA_START = `${go_trust_base_api}/api/v1/ropa/start`;
export const DOWNLOAD_ROPA_AUDIT_LOG = `${go_trust_base_api}/api/v1/ropa/download-audit-log`;

//Privacy and Data Protection - All Policies - View Policy
export const POLICY_DATA = `${go_trust_base_api}/api/v1/policy/`;
export const FETCH_POLICY_AUDIT_LOG = `${go_trust_base_api}/api/v1/policy/audit-policy`;
export const FETCH_ALL_POLICY_AUDIT_LOG = `${go_trust_base_api}/api/v1/policy/audit/logs`;
export const POLICY_REVIEW = `${go_trust_base_api}/api/v1/policy/review/`;
export const UPLOAD_POLICY_ATTACHMENT = `${go_trust_base_api}/api/v1/policy/upload-document/`;
export const ADD_QUESTION = `${go_trust_base_api}/api/v1/ropa/controls`;

//Assessment - Privacy
export const FETCH_ASSESSMENTS = `${COMMON_ENDPOINT}/assessments`;
export const COMMON_ASSESSMENT_ENDPOINT = `${go_trust_base_api}/api/v1/assessments`;
export const CREATE_NEW_ASSESSMENT = `${COMMON_ENDPOINT}/assessments/create`;
export const FETCH_PROCESS_BY_DEPTID = `${go_trust_base_api}/api/v1/processes/list`;
export const FETCH_DASHBOARD_RISK_COUNT = `${go_trust_base_api}/api/v1/assessments-dashboard/risks`;
export const FETCH_DASHBOARD_OWNER_COUNT = `${go_trust_base_api}/api/v1/assessments-dashboard/owner`;
export const FETCH_DASHBOARD_READINESS_COUNT = `${go_trust_base_api}/api/v1/assessments-dashboard/name`;
export const FETCH_DASHBOARD_ASSESSMENT_LIST = `${go_trust_base_api}/api/v1/assessments-dashboard/list`;
export const DELETE_DOCUMENT_ASSESSMENT = `${go_trust_base_api}/api/v1/assessments/delete-document`;
export const FETCH_DEPARTMENT_BY_GROUPID = `${go_trust_base_api}/api/v2/departments`;
export const FETCH_VRM_TEMPLATES = `${go_trust_base_api}/api/v1/vrm/vea/template`;
export const UPLOAD_VRM_ASSESSMENT_TEMPLATE = `${go_trust_base_api}/api/v1/vrm/vea/upload-controls`;
// export const FETCH_ASSESSMENTS_TASK_OVERVIEW = `${COMMON_ENDPOINT}/assessments/task-overview`;
export const FETCH_ASSESSMENT_POLICY_LIST = `${go_trust_base_api}/api/v1/ropa/policy-list`;
export const UPDATE_ASSESSMENT_TENTATIVE_DATE = `${COMMON_ASSESSMENT_ENDPOINT}/update`;

export const FETCH_ASSESSMENTS_LIST = `${go_trust_base_api}/api/v1/assessments/list`;
export const ADD_ASSESSMENT_UNIFIED = `${go_trust_base_api}/api/v1/assessments/assessment-type`;
export const UPLOAD_ASSESSMENT_TEMPLATE = `${go_trust_base_api}/api/v1/assessments/upload-controls`;
export const FETCH_ASSESSMENT_TEMPLATES = `${go_trust_base_api}/api/v1/assessments/list-template`;
export const FETCH_DASHBOARD_ASSESSMENT_TEMPLATES = `${go_trust_base_api}/api/v1/assessments-dashboard/templates`;
export const FETCH_ASSESSMENT_DASHBOARD_COLLABORATOR = `${go_trust_base_api}/api/v1/assessment-dashboard/assignee`;
export const CREATE_NEW_UNIFIED_ASSESSMENT = `${go_trust_base_api}/api/v1/assessments/create-assessment`;
export const FETCH_ASSESSMENTS_TASK_OVERVIEW = `${go_trust_base_api}/api/v1/assessments/tasks-overview`;
export const START_ASSESSMENT = `${go_trust_base_api}/api/v1/assessments/start`;
export const UPDATE_ANSWER = `${go_trust_base_api}/api/v1/assessments/answer`;
export const FETCH_CATEGORY_ASSESSMENTS_LIST = `${go_trust_base_api}/api/v1/assessments/category-types`;
export const FETCH_VIEW_TEMPLATE = `${go_trust_base_api}/api/v1/assessments/view-template`;
export const DOWNLOAD_ASSESSMENT_AUDIT_LOG = `${go_trust_base_api}/api/v1/assessments/download-audit-log`;
export const COLLABORATOR_PROGRESS_ASSESSMENT = `${go_trust_base_api}/api/v1/assessments/categories-progress/`;

//Blogs
export const BLOG_LIST = `${go_trust_base_api}/api/v1/blogs`;
export const BLOG_DETAILS = `${go_trust_base_api}/api/v1/blogs/`;
export const BLOG_CATEGORIES = `${go_trust_base_api}/api/v1/blogs/categories`;
//AI
export const FETCH_AI_CONTEN_POLICY = `${go_trust_base_api}/api/v1/policy-creation/`;
export const FETCH_TEMPLATE_SAMPLE = `${go_trust_base_api}/api/v1/policy-creation/templates`;
export const SAVE_AI_POLICY_TEMPLATE = `${go_trust_base_api}/api/v1/policy-creation/save`;
export const SUBMIT_AI_POLICY_TEMPLATE = `${go_trust_base_api}/api/v1/policy-creation/submit`;

//VRM
export const COMMON_VRM_ASSESSMENT_ENDPOINT = `${go_trust_base_api}/api/v1/vrm`;
export const FETCH_VRM_POLICY_LIST = `${go_trust_base_api}/api/v1/ropa/policy-list`;
export const DELETE_DOCUMENT_VRM = `${COMMON_ENDPOINT}/delete-document`;
export const SEND_VRM_ALERTS = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/alert`;
export const FETCH_ALL_VRM_TASK_OVERVIEW = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/vendor`;
export const FETCH_VRM_VENDER_DATA = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/vendor-data`;
export const FETCH_VRM_INTERNAL_ASSESSMENT_TASK_OVERVIEW = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/via`;
export const FETCH_VRM_EXTERNAL_ASSESSMENT_TASK_OVERVIEW = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/vea`;
export const FETCH_INTERNAL_ASSESSMENT_TASK_OVERVIEW = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/task-overview`;
export const ADD_NEW_VENDOR = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/create`;
export const FETCH_VENDOR_LIST = `${go_trust_base_api}/api/v1`;
export const FETCH_VRM_ASSESSMENT_AUDIT_LOG = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/api/v1`;
export const ADD_EXISTING_VENDOR = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/add`;
export const FETCH_VENDOR_TYPE = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/vendor/type`;
export const FETCH_VENDOR_DETAILS = `${COMMON_VRM_ASSESSMENT_ENDPOINT}/detail`;
export const FETCH_DASHBOARD_CRITICALITY_COUNT = `${go_trust_base_api}/api/v1/vrm-dashboard/key?key=`;
export const FETCH_DASHBOARD_CHART_DATA = `${go_trust_base_api}/api/v1/vrm-dashboard?key=`;
export const COLLABORATOR_PROGRESS_VRM_INTERNAL_ASSESSMENT = `${go_trust_base_api}/api/v1/vrm/via/categories-progress`;
export const COLLABORATOR_PROGRESS_VRM_VENDOR_ASSESSMENT = `${go_trust_base_api}/api/v1/vrm/vea/categories-progress`;
export const UPLOAD_VIA_AI__ATTACHMENT = `${base_api}/api/v1/vrm/via/automate/`;
export const UPLOAD_VEA_AI__ATTACHMENT = `${base_api}/api/v1/vrm/vea/automate/`;
export const CATEGORY_CHANGE_VIA_GLOBALLY = `${go_trust_base_api}/api/v1/vrm/via/change-category`;
export const CATEGORY_CHANGE_VEA_GLOBALLY = `${go_trust_base_api}/api/v1/vrm/vea/change-category`;

// Global workflow

export const COMMON_ADD_WORKFLOW = `${base_api}/api/v1/common-workflow`;
export const COMMON_FETCH_WORKFLOW_LIST = `${base_api}/api/v1/common-workflow`;
export const COMMON_EDIT_WORKFLOW = `${base_api}/api/v1/common-workflow`;
export const COMMON_FETCH_TASK_LIST = `${base_api}/api/v1/common-workflowsteps/alltask/`;
export const COMMON_ADD_TASK = `${base_api}/api/v1/common-workflowsteps/task`;
export const COMMON_UPDATE_TASK = `${base_api}/api/v1//common-workflowsteps/task`;
export const COMMON_UPDATE_WORKFLOW_TASK = `${base_api}/api/v1/common-workflowsteps/task/update/activepieces_id`;
export const COMMON_FETCH_WORKFLOW_LIST_BY_ID = `${base_api}/api/v1/common-workflow/`;
export const COMMON_DELETE_WORKFLOW_STEP = `${base_api}/api/v1/common-workflow/steps`;
export const COMMON_MOVEUP_WORKFLOW_STEP = `${base_api}/api/v1/common-workflow/steps/moveup`;
export const COMMON_MOVEDOWN_WORKFLOW_STEP = `${base_api}/api/v1/common-workflow/steps/movedown`;
export const COMMON_ADD_WORKFLOW_STEPS = `${base_api}/api/v1/common-workflow/steps`;
export const COMMON_SELECTED_WORKFLOW_AUTOMATION = `${base_api}/api/v1/workflow/steps`;

//DSR
export const ADD_WORKFLOW = `${base_api}/api/v1/workflow`;
export const FETCH_WORKFLOW_LIST = `${base_api}/api/v1/workflow`;
export const EDIT_WORKFLOW = `${base_api}/api/v1/workflow`;
export const FETCH_TASK_LIST = `${base_api}/api/v1/workflowsteps/alltask/`;
export const ADD_TASK = `${base_api}/api/v1/workflowsteps/task`;
export const UPDATE_TASK = `${base_api}/api/v1//workflowsteps/task`;
export const UPDATE_WORKFLOW_TASK = `${base_api}/api/v1/workflowsteps/task/update/activepieces_id`;
export const FETCH_WORKFLOW_LIST_BY_ID = `${base_api}/api/v1/workflow/`;
export const DELETE_WORKFLOW_STEP = `${base_api}/api/v1/workflow/steps`;
export const MOVEUP_WORKFLOW_STEP = `${base_api}/api/v1/workflow/steps/moveup`;
export const MOVEDOWN_WORKFLOW_STEP = `${base_api}/api/v1/workflow/steps/movedown`;
export const FETCH_REQUEST_LIST = `${base_api}/api/v1/dsr/request`;
export const ADD_WORKFLOW_STEPS = `${base_api}/api/v1/workflow/steps`;
export const FETCH_REQUEST_TYPE = `${base_api}/api/v1/workflow/published`;
export const CREATE_REQUEST_FORM = `${base_api}/api/v1/dsr/request`;
export const UPDATE_BUSINESS_Unit = `${base_api}/api/v1/dsr/request`;
export const GUEST_REQUESTS = `${base_api}/api/v1/dsr/guest-request`;
export const UPLOAD_GUEST_REQUESTS = `${base_api}/api/v1/dsr/guest-upload-documents`;
export const UPDATE_DSR_LOGO = `${base_api}/api/v1/dsr/form/upload-logo`;
export const ADD_TASK_TASKOVERVIEW = `${base_api}/api/v1/dsr/request/task`;
export const FETCH_REQUEST_ALL_DATA = `${base_api}/api/v1/dsr/request/allinfo`;
export const FETCH_PREVIEW_REQUEST_DATA = `${base_api}/api/v1/dsr/request/form-view`;
export const FETCH_PREVIEW_REQUEST_DATA_GUEST = `${base_api}/api/v1/dsr/guest-request/form-view`;
export const FETCH_GUEST_REQUEST_ALL_DATA = `${base_api}/api/v1/dsr/guest-request/allinfo`;
export const UPDATE_DSR_FORM_CONTENT = `${base_api}/api/v1/dsr/form/create`;
export const FETCH_DASHBOARD_REQUEST_COUNT = `${base_api}/api/v1/dsr-dashboard/count`;
export const FETCH_DASHBOARD_REQUEST_BY_RIGHTS = `${base_api}/api/v1/dsr-dashboard/types`;
export const FETCH_DASHBOARD_REQUEST_BY_RESIDENCY = `${base_api}/api/v1/dsr-dashboard/residency`;
export const FETCH_DASHBOARD_REQUEST_BY_PENDING = `${base_api}/api/v1/dsr-dashboard/pending`;
export const FETCH_DASHBOARD_REQUEST_BY_STAGE = `${base_api}/api/v1/dsr-dashboard/stage`;
export const FETCH_DASHBOARD_REQUEST_BY_OWNER = `${base_api}/api/v1/dsr-dashboard/owner`;
export const FETCH_DASHBOARD_REQUEST_BY_STATICS = `${base_api}/api/v1/dsr-dashboard/request`;
export const FETCH_ASSIGNEE_VIEW_DETAILS_LIST = `${base_api}/api/v1/dsr/request/approved`;
export const UPLOAD_REQUEST_IDENTIFICATION = `${base_api}/api/v1/dsr/uploadRequestIdentificationDocuments`;
export const UPLOAD_LETTER_OF_AUTHORITY = `${base_api}/api/v1/dsr/uploadLetterOfAuthorityDocuments`;
export const UPLOAD_ADD_TASK_ATTACHMENT = `${base_api}/api/v1/dsr/upload-documents`;
export const FETCH_FORM = `${base_api}/api/v1/dsr/form/template`;
export const DELETE_EMAIL_TEMPLATE = `${base_api}/api/v1/dsr/mail/template`;
export const UPDATE_EMAIL_TEMPLATE = `${base_api}/api/v1/dsr/mail/update`;
export const UPLOAD_dOCUMENT = `${base_api}/api/v1/dsr/upload-documents`;
export const DELETE_DOCUMENT_DSR = `${base_api}/api/v1/dsr/request/task/delete-documents`;
export const SEND_OTP_GUEST_LOGIN = `${base_api}/api/v1/guest/send-otp`;
export const VERIFY_OTP_GUEST_LOGIN = `${base_api}/api/v1/guest/verify-otp`;
export const ASSIGNED_TASK_LIST = `${base_api}/api/v1/dsr/tas-request`;
export const SELECTED_WORKFLOW_AUTOMATION = `${base_api}/api/v1/workflow/steps`;
//form builder
export const CREATE_DSR_FORM_BUILDER = `${base_api}/api/v1/dsr/request/create-form`;
export const DELETE_DSR_FORM_BUILDER = `${base_api}/api/v1/dsr/request/delete-form`;
export const FETCH_FORM_BUILDER_LIST = `${base_api}/api/v1/dsr/form/template-list`;
export const FETCH_DSR_FORM_BUILDER_CATEGORY = `${base_api}/api/v1/dsr/form/categories`;
export const FETCH_BASIC_INFO_CONTROL = `${base_api}/api/v1/dsr/form/controls`;
export const CREATE_CATEGORY_DSR_FORM_BUILDER = `${base_api}/api/v1/dsr/request/category`;
export const ADD_DSR_CONTROL = `${base_api}/api/v1/dsr/request/controls`;
export const UPDATE_DSR_CONTROLS = `${base_api}/api/v1/dsr/request/controls`;
export const PUBLISH_DSR_FORM = `${base_api}/api/v1/dsr/form/publish`;
export const GET_DSR_FORM_BUILDER_DATA = `${base_api}/api/v1/dsr/request-form`;
export const SUBMIT_FORM_BUILDER = `${base_api}/api/v1/dsr/request/create-request`;
export const SEND_OTP_FORM_BUILDER = `${base_api}/api/v1/dsr/request/resend-otp`;
export const VERIFY_OTP_FORM = `${base_api}/api/v1/dsr/request/verify-otp`;
export const UPDATE_FORM_BUILDER_CONTENT = `${base_api}/api/v1/dsr/request/form-content`;
export const CHECK_STATUS = `${base_api}/api/v1/dsr/request/check-verification`;
export const DELETE_DSR_FORM_BUILDER_QUESTION = `${base_api}/api/v1/dsr/request/controls`;
export const UPDATE_FORM_BUILDER_ORDER = `${base_api}/api/v1/dsr/request-form/updateFieldOrder`;

export const AUTOMATION_LIST = `${base_api}/api/v1/activepieces/folder-workflows`;

export const REQUEST_DETAILS = `${base_api}/api/v1/dsr/request`;
export const APPROVE_REJECTED_REQUEST = `${base_api}/api/v1/dsr/request/approved-reject`;
export const ARCHIVE_DSR_REQUEST = `${base_api}/api/v1/dsr/request/archive-unarchive`;
export const UNARCHIVE_DSR_REQUEST = `${base_api}/api/v1/dsr/request/archive-unarchive`;
export const REQUESTG_PER_OWNER = `${base_api}/api/v1/dsr-dashboard/owners`;
// task overview
export const TASK_OVERVIEW = `${base_api}/api/v1/dsr/request`;
export const UPDATE_TASK_PROGRESS = `${base_api}/api/v1/dsr/request/task`;
export const APPROVED_REQUEST_BY_ID = `/approved`;
export const FETCH_ASSIGNEE_DSR_LIST = `${base_api}/api/v1/dsr/request/assignee`;
export const FETCH_MAIL_ACTIVITY_LOG = `${base_api}/api/v1/dsr/request/mail-activity`;
export const COUNT_CHAT_ACTIVITY_LOG = `${base_api}/api/v1/dsr/request/mail-read`;
export const ASSIGN_DSR_REQUEST = `${base_api}/api/v1/dsr/request/assign`;
export const ACTIVITY_MAIL_TEMPLATES = `${base_api}/api/v1/dsr/request/send-assigneeside-mail`;
export const FETCH_DSR_ALL_AUDIT_LOG = `${base_api}/api/v1/dsr/request/audit`;
export const FETCH_STEP_WISE_AUDIT_LOG = `${base_api}/api/v1/dsr/request/step-audit`;
export const DOWNLOAD_AUDIT_LOG = `${base_api}/api/v1/dsr/request/download-audit`;
export const SET_AUTOMATION_BY_ID = `${base_api}/workflow/steps/`;
export const FETCH_LANGUAGES_LIST = `${ucm_gotrust_basi_api}/cp`;
export const CREATE_FORM_TRANSLATION = `${base_api}/api/v1/dsr/form/translations`;
export const FETCH_TRANSLATED_LANGUAGE = `${base_api}/api/v1/languages`;
export const DELETE_FORM_TRANSLATION = `${base_api}/api/v1/dsr/form/translations`;
export const FETCH_TRANSLATED_CONTENT = `${base_api}/api/v1/dsr/form/translations`;

//! UCM
export const COMMON_UCM_ENDPOINT = `${universal_gotrust_basi_api}/api/v1`;
export const COMMON_UCM_ENDPOINT_V2 = `${universal_gotrust_basi_api}/api/v2`;
export const COMMON_UCM_ENDPOINT_V3 = `${universal_gotrust_basi_api}/api/v3`;

//! v3

export const FETCH_UCM_PROCESS_LIST = `${COMMON_UCM_ENDPOINT_V3}/core/processes`;
export const FETCH_UCM_VENDOR_LIST = `${COMMON_UCM_ENDPOINT_V3}/core/vendors`;
export const FETCH_UCM_DEPARTMENT_LIST = `${COMMON_UCM_ENDPOINT_V3}/core/departments`;
export const FETCH_UCM_LEGAL_FRAMEWORK_LIST = `${COMMON_UCM_ENDPOINT_V3}/core/legal-frameworks`;
export const FETCH_PREFERENCE_CENTER_DATA = `${COMMON_UCM_ENDPOINT_V3}/pc/display`;
export const COLLECTION_TEMPLATE_DASHBOARD_DATA = `${COMMON_UCM_ENDPOINT_V3}/dashboard/kpis/`;
export const COLLECTION_TEMPLATE_DETAILED_DASHBOARD_DATA = `${COMMON_UCM_ENDPOINT_V3}/dashboard/kpis/templates/detailed`;

//! v2
export const UCM_COLLECTION_BUILDER_STEP_1 = `${COMMON_UCM_ENDPOINT_V2}/ct/records`;
export const CONSENT_PURPOSE_MAPPING = `${COMMON_UCM_ENDPOINT_V2}/ct/cp-pl-maps`;
export const GET_UCM_COLLECTION_BUILDER_STEP_2 = `${COMMON_UCM_ENDPOINT_V2}/ct/cp-pl-maps`;
export const FETCH_API_AS_SOURCES = `${COMMON_UCM_ENDPOINT_V2}/source/api`;
export const FETCH_COLLECTION_TEMPLATE = `${COMMON_UCM_ENDPOINT_V2}/ct/detailed`;
export const FETCH_UCM_FREQUENCY = `${COMMON_UCM_ENDPOINT_V2}/ct/frequency-list`;
export const DELETE_CONSENT_PURPOSE = `${COMMON_UCM_ENDPOINT_V2}/ct/cp-pl-maps/delete`;

export const FETCH_POLICY_DASHBOARD_DEPARTMENT = `${COMMON_ENDPOINT}/policy-dashboard/department`;
//DATA Retention
export const DATA_RETENTION_RULE = `${COMMON_UCM_ENDPOINT}/data-retention/core/data-rentention-rule/records?`;
export const GET_JURISDICTIONS = `${COMMON_UCM_ENDPOINT}/data-retention/core/legal-motif/records?display=detailed`;

export const SEND_RULES = `${COMMON_UCM_ENDPOINT}/data-retention/core/data-rentention-rule/records`;
export const SEND_RULES_MOTIF = `${COMMON_UCM_ENDPOINT}/data-retention/core/retention-rule-motif-pii-map/records`;
export const DELETE_RULES = `${COMMON_UCM_ENDPOINT}/data-retention/core/data-rentention-rule/delete`;
export const GET_RULE_DETAILS = `${COMMON_UCM_ENDPOINT}/data-retention/core/retention-rule-motif-pii-map/records?`;
//i/v1/pl/display-list?customer_id=715
export const FETCH_UCM_DASHBOARD_CONSENT_STATUS = `${COMMON_UCM_ENDPOINT}/dash/display-consent-status`;
export const FETCH_UCM_DASHBOARD_CARD_DATA = `${COMMON_UCM_ENDPOINT_V3}/dashboard/common/consent-metrics`;
export const FETCH_UCM_DASHBOARD_CONSENT_STATUS_GRAPH = `${COMMON_UCM_ENDPOINT}/dash/consent_status`;
export const FETCH_UCM_DASHBOARD_CONSENT_BY_PURPOSE = `${COMMON_UCM_ENDPOINT}/dash/consent_by_purpose`;
export const FETCH_UCM_DASHBOARD_CONSENT_BY_RESIDENCY = `${COMMON_UCM_ENDPOINT}/dash/consent_by_residency`;
export const FETCH_UCM_DASHBOARD_CONSENT_RECORD_TABLE = `${COMMON_UCM_ENDPOINT}/dash/display-consents-record`;
export const DOWNLOAD_UCM_DASHBOARD_CONSENT_RECORD_TABLE = `${COMMON_UCM_ENDPOINT}/dash/download-consent-record`;
export const FETCH_UCM_PROCESSING_PURPOSE_LIST = `${COMMON_UCM_ENDPOINT}/pp/display-records`;
export const FETCH_UCM_PROCESSING_CATEGORY_LIST = `${COMMON_UCM_ENDPOINT}/ppc/display-records`;
export const FETCH_UCM_CONSENT_PURPOSE_LIST = `${COMMON_UCM_ENDPOINT_V2}/cp/records`;

export const FETCH_UCM_PII_LABEL_LIST = `${COMMON_UCM_ENDPOINT_V2}/pl/records`;
export const FETCH_UCM_PREFERENCE_CENTER_LIST = `${COMMON_UCM_ENDPOINT}/pc/display-list`;
export const FETCH_UCM_COLLECTION_TEMPLATE_TABLE = `${COMMON_UCM_ENDPOINT}/ct/display-records`;
export const CRUD_DATA_RETENTION_DATA = `${COMMON_UCM_ENDPOINT}/data-retention/ct-cp-policy-maps`;
export const DELETE_POLICY_MAPPING = `${COMMON_UCM_ENDPOINT}/data-retention/ct-cp-policy-maps/delete`;
export const FETCH_LANG_DATA = `${ucm_gotrust_basi_api}/lang`;
export const FETCH_DEFAULT_WORKFLOW_DRORPDOWN_DATA = `${COMMON_UCM_ENDPOINT}/workflow-automation/implementation/workflow-types`;
export const FETCH_CUSTOM_WORKFLOW_DRORPDOWN_DATA = `${COMMON_UCM_ENDPOINT}/workflow-automation/implementation/common-workflows`;
export const UCM_WORKFLOW_TABLE_DATA = `${COMMON_UCM_ENDPOINT}/workflow-automation/implementation/ucm-workflows`;
export const ADD_WORKFLOW_UCM = `${COMMON_UCM_ENDPOINT}/workflow-automation/implementation/ucm-workflows`;
export const EDIT_WORKFLOW_UCM = `${COMMON_UCM_ENDPOINT}/workflow-automation/implementation/ucm-workflows`;
export const DELETE_WORKFLOW_UCM = `${COMMON_UCM_ENDPOINT}/workflow-automation/implementation/ucm-workflows/delete`;

export const FETCH_UCM_COLLECTION_TEMPLATE_TABLE_RECORDS = `${COMMON_UCM_ENDPOINT_V2}/ct/records`;
export const FETCH_UCM_SUBJECT_CONSENT_LIST = `${COMMON_UCM_ENDPOINT}/data-collector/display-ucd`;
export const FETCH_UCM_SUBJECT_CONSENT_LIST_RECORD = `${COMMON_UCM_ENDPOINT}/data-collector/display-unified-person`;
export const FETCH_UCM_SUBJECT_CONSENT_LIST_AUDIT_DATA = `${COMMON_UCM_ENDPOINT}/data-collector/audit-logs`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_AUDIT_DATA = `${COMMON_UCM_ENDPOINT_V2}/subject-consent-manager/audit-logs`;
export const FETCH_UCM_SUBJECT_CONSENT_TYPES_LIST = `${COMMON_UCM_ENDPOINT}/data-collector/display-ucd-per_ppc`;
export const FETCH_UCM_SUBJECT_CONSENT_TYPES_LIST_BY_CONSENT = `${COMMON_UCM_ENDPOINT}/data-collector/display-ucd-per-pp`;
export const CREATE_UCM_PROCESSING_CATEGORY = `${COMMON_UCM_ENDPOINT}/ppc/create_record`;
export const CREATE_UCM_PROCESSING_PURPOSE = `${COMMON_UCM_ENDPOINT}/pp/create_record`;
export const CREATE_UCM_CONSENT_PURPOSE = `${COMMON_UCM_ENDPOINT_V2}/cp/records`;
export const FETCH_UCM_PII_LIST = `${COMMON_UCM_ENDPOINT}/pl/display-list`;
export const CREATE_UCM_PII_LABEL = `${COMMON_UCM_ENDPOINT}/pl/insert`;
export const POC_SEND_MESSAGE = `${base_api}/api/v1/activepieces/send-whatsapp-message`;
export const POC_SEND_EMAIL = `${COMMON_UCM_ENDPOINT}/services/emails/`;
export const UPDATE_UCM_PROCESSING_CATEGORY = `${COMMON_UCM_ENDPOINT}/ppc/update-record`;
export const UPDATE_UCM_PROCESSING_PURPOSE = `${COMMON_UCM_ENDPOINT}/pp/update_record`;
export const UPDATE_UCM_CONSENT_PURPOSE = `${COMMON_UCM_ENDPOINT_V2}/cp/records`;
export const UPDATE_UCM_PII_LABEL = `${COMMON_UCM_ENDPOINT}/pl/update`;
export const FETCH_UCM_CONSENT_PURPOSE_RECORDS = `${COMMON_UCM_ENDPOINT_V2}/cp/records`;
export const UCM_REMOVE_COLLECTION_TEMPLATE = `${COMMON_UCM_ENDPOINT_V2}/ucm-lab/delete`;

export const FETCH_UCM_PROCESSING_PURPOSE_RECORDS = `${COMMON_UCM_ENDPOINT}/pp/display-records`;
export const FETCH_UNIFIED_PII_COLLECTION_TEMPLATE = `${COMMON_UCM_ENDPOINT}/ct/initiate`;
export const UPDATE_CONSENT_FORM = `${COMMON_UCM_ENDPOINT}/gtf/update-form`;
export const FETCH_SUPPORTED_LANGUAGES = `${COMMON_UCM_ENDPOINT_V2}/translator/language-codes`;
export const FETCH_DEFAULT_TRANSLATED_DATA = `${COMMON_UCM_ENDPOINT_V2}/translator/records`;
export const FETCH_SUBJECT_CONSENT_BAR_DATA = `${COMMON_UCM_ENDPOINT_V2}/analytics/pp-consent-counts`;
export const FETCH_SUBJECT_CONSENT_PIE_DATA = `${COMMON_UCM_ENDPOINT_V2}/analytics/consent-counts`;
export const FETCH_SEGREGATION_DATA = `${COMMON_UCM_ENDPOINT_V2}/analytics/source-seggregations`;
export const FETCH_UCM_SUBJECT_CONSENT_COLLECTION_LIST = `${COMMON_UCM_ENDPOINT_V2}/data-collector/ucd-templates`;
export const PRIVACY_NOTE_VERSION = `${COMMON_UCM_ENDPOINT_V2}/pn/create`;
// export const FETCH_PRIVACY_NOTE = `${COMMON_UCM_ENDPOINT_V2}/pn/display`;
export const FETCH_PRIVACY_NOTE = `${base_api}/api/v1/privacy-notice`;
export const CHANGE_COLLECTION_TEMPLATE_DETAILS = `${COMMON_UCM_ENDPOINT_V2}/ct/records`;
export const DELETE_UCM_LAB_COMPONENT = `${COMMON_UCM_ENDPOINT_V2}/ucm-lab/delete`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_CONSENT_DISTRIBUTION_DATA = `${COMMON_UCM_ENDPOINT_V2}/subject-consent-manager/consents-collection-template-distribution`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_CONSENT_SOURCE_DATA = `${COMMON_UCM_ENDPOINT_V2}/subject-consent-manager/consent-sources-distribution`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_PP_CONSENT_COUNTS = `${COMMON_UCM_ENDPOINT_V3}/analytics/v3-pp-consent-counts`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_CONSENT_COUNTS = `${COMMON_UCM_ENDPOINT_V3}/analytics/v3-consent-counts`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_CONSENT_STATUS_LIST = `${COMMON_UCM_ENDPOINT_V2}/subject-consent-manager/consent-status-list`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_CONSENT_SOURCE_LIST = `${COMMON_UCM_ENDPOINT_V2}/subject-consent-manager/consent-source-list`;
export const FETCH_UCM_SUBJECT_CONSENT_MANAGER_SUBJECT_CONSENT_HISTORY = `${COMMON_UCM_ENDPOINT_V2}/subject-consent-manager/customer-consent-history`;
export const FETCH_LIST_SANKY_GRAPH = `${COMMON_UCM_ENDPOINT_V3}/unified-person/graph`;
// Email-template
export const EMAIL_TEMPLATE = `${base_api}/api/v1/dsr/mail`;
export const FETCH_UCM_PROCESSING_PURPOSE_CATEGORY_RECORDS = `${COMMON_UCM_ENDPOINT}/ppc/display-list`;
export const CREATE_UCM_CT_CONSENT_PURPOSE = `${COMMON_UCM_ENDPOINT}/ct/ct-step2-cp`;
export const POST_UCM_COLLECTION_BUILDER_STEP_2 = `${COMMON_UCM_ENDPOINT}/ct/insert-ct-cp-pl-map`;
export const FETCH_SOURCES = `${COMMON_UCM_ENDPOINT}/ct/consent-sources`;
export const FETCH_COLLECTION_TEMPLATE_STEP_4_FORM = `${COMMON_UCM_ENDPOINT}/gtf/display-form`;
export const FETCH_PREFERENCE_LIST = `${COMMON_UCM_ENDPOINT}/pc/display-list`;
export const CREATE_UCM_CT_FORM = `${COMMON_UCM_ENDPOINT}/gtf/create-form`;
export const CREATE_UCM_CT_PREFERENCE_FORM = `${COMMON_UCM_ENDPOINT_V2}/pc/create`;

export const DOWNLOAD_UCM_TEMPLATE = `${COMMON_UCM_ENDPOINT_V2}/source/file`;
export const UPLOAD_FILE_TO_MINIO = `${COMMON_UCM_ENDPOINT_V2}/source/upload-csv`;
export const PROCESS_FILE_TO_UPLOAD = `${COMMON_UCM_ENDPOINT_V3}/source/file/process`;

// Cookie Consent Management
export const COMMON_COOKIE_ENDPOINT = `${ucm_gotrust_basi_api}`;
export const FETCH_COOKIE_SETUP = `${COMMON_COOKIE_ENDPOINT}/domain_cookie`;
export const POST_COOKIE_CONFIG_DATA = `${COMMON_COOKIE_ENDPOINT}/domain_route`;
export const FETCH_COOKIE_CONSENT_DOMAIN = `${COMMON_COOKIE_ENDPOINT}/domain_route/domain`;
export const FETCH_COOKIE_CONSENT_RATE = `${COMMON_COOKIE_ENDPOINT}/dashboard/display-rates`;
export const FETCH_COOKIE_SERVICES = `${COMMON_COOKIE_ENDPOINT}/cookie_classification/cookie-service`;
export const FETCH_COOKIE_MONTHLY_STATUS = `${COMMON_COOKIE_ENDPOINT}/dashboard/monthly-status-counts`;
export const FETCH_OBSERVATION_DATA = `${COMMON_COOKIE_ENDPOINT}/domain_cookie/create`;
export const DELETE_COOKIE_CONSENT_DOMAIN = `${COMMON_COOKIE_ENDPOINT}/domain_route/delete`;
export const FETCH_COOKIE_LEGAL_FRAMEWORKS = `${COMMON_COOKIE_ENDPOINT}/cp/regulations`;
export const FETCH_COOKIE_CONSENT_RECORDS = `${COMMON_COOKIE_ENDPOINT}/dashboard/display-consent-record`;
export const FETCH_COOKIE_CATEGORIES = `${COMMON_COOKIE_ENDPOINT}/cookie_consent_record/cookie-category`;
export const FETCH_COOKIE_CATEGORY_CONSENT = `${COMMON_COOKIE_ENDPOINT}/dashboard/category-consent-counts`;
export const FETCH_BANNER_COUNTS = `${COMMON_COOKIE_ENDPOINT}/dashboard/banner-counts`;
export const FETCH_COOKIES_BY_CATEGORY = `${COMMON_COOKIE_ENDPOINT}/dashboard/cookie-counts`;
export const FETCH_COOKIE_DOMAIN_LIST = `${COMMON_COOKIE_ENDPOINT}/dashboard/display-domains-list-per-customer`;
export const FETCH_COOKIE_CONSENT_BY_RESIDENCY = `${COMMON_COOKIE_ENDPOINT}/dashboard/continent-consent-counts`;
export const POST_AUTO_SCAN = `${COMMON_COOKIE_ENDPOINT}/auto_scan_scheduler/auto-cookie-scanner`;
export const GET_DOMAIN_ROUTE_DETAILS = `${COMMON_COOKIE_ENDPOINT}/domain_route/all-details?domain_id=`;
export const DOWNLOAD_COOKIE_REPORT = `${COMMON_COOKIE_ENDPOINT}/domain_cookie/all?download=true&domain_id=`;
export const GET_COOKIE_POLICY = `${COMMON_COOKIE_ENDPOINT}/cp/display`;
export const ADD_COOKIE_POLICY = `${COMMON_COOKIE_ENDPOINT}/cp/create`;
export const POST_COOKIE_POLICY_TRANSLATED_DATA = `${COMMON_COOKIE_ENDPOINT}/cp/lang-create`;
export const GET_ALL_COOKIE_LANGUAGES = `${COMMON_COOKIE_ENDPOINT}/cp`;
export const GET_COOKIE_DICTIONARY = `${COMMON_COOKIE_ENDPOINT}/cookie_dict/display?customer_id=`;
export const EDIT_COOKIE_DATA = `${COMMON_COOKIE_ENDPOINT}/cookie_dict/edit`;
export const CATEGORY_LISTING = `${COMMON_COOKIE_ENDPOINT}/cookie_consent_record/display`;
export const GET_BANNER_DETAILS_WITH_LANG = `${COMMON_COOKIE_ENDPOINT}/lang/display`;
export const TRANSLATE_COOKIE_CATEGORY = `${COMMON_COOKIE_ENDPOINT}/lang/translate-cookie-category`;
export const TRANSLATE_COOKIE_SERVICE = `${COMMON_COOKIE_ENDPOINT}/lang/cookie-service/translations`;
export const TRANSLATE_COOKIE_ITSELF = `${COMMON_COOKIE_ENDPOINT}/lang/cookie/translation`;
export const GET_COOKIE_RISK_SCORE = `${COMMON_COOKIE_ENDPOINT}/api/v2/cookie_risk/risk-score`;
export const COOKIE_ADDITIONAL_MATRIX = `${COMMON_COOKIE_ENDPOINT}/api/v3/dashboard/metrics`;
export const COOKIE_DASHBOARD_CARD_DATA = `${COMMON_COOKIE_ENDPOINT}/api/v3/dashboard/cookie-count`;
export const FETCH_GEOGRAPHIC_DISTIBUTION = `${COMMON_COOKIE_ENDPOINT}/api/v3/dashboard/continent-counts`;
export const FETCH_ANALYTICS_DATA = `${COMMON_COOKIE_ENDPOINT}/api/v3/analytics`;

//Privacy-Ops
export const ASSESSMENT_DATA = `${base_api}/api/v1/privacy-ops/repo/assessment`;

export const ASSESSMENT_COUNT = `${base_api}/api/v1/privacy-ops/repo/assessment/count`;
export const ROPA_DATA = `${base_api}/api/v1/privacy-ops/repo/ropa`;
export const ROPA_DETAILS = `${base_api}/api/v1/privacy-ops/repo/ropa/detail`;
export const VENDOR_DETAILS = `${base_api}/api/v1/privacy-ops/repo/vrm/detail`;
export const ASSESSMENT_DETAILS = `${base_api}/api/v1/privacy-ops/repo/assessment/detail`;
export const ROPA_COUNT = `${base_api}/api/v1/privacy-ops/repo/ropa/count`;
export const DOC_DATA = `${base_api}/api/v1/privacy-ops/repo/document`;
export const DOC_COUNT = `${base_api}/api/v1/privacy-ops/repo/document/count`;
export const REGULATIONS_LIST = `${base_api}/api/v2/regulations/list`;
export const FETCH_INDUSTRY = `${base_api}/api/v1/industry-vertical`;
export const RESOURCE_LIST = `${base_api}/api/v1/resources`;
export const FETCH_ORG_REGULATIONS = `${base_api}/api/v1/privacy-ops/regulations/list`;
export const REGULATIONS_CONTROLS = `${base_api}/api/v1/privacy-ops/regulations/controls`;
export const REGULATIONS_CONTROL_DETAILS = `${base_api}/api/v1/privacy-ops/regulations/busi-req`;
export const ACTIVITIES_ACTIONS_LIST = `${base_api}/api/v1/privacy-ops/activities/action-list`;
export const ADD_ACTION = `${base_api}/api/v1/privacy-ops/activities/action`;
export const ACTION_COUNT = `${base_api}/api/v1/privacy-ops/activities/action-count`;
export const UPDATE_ACTION = `${base_api}/api/v1/privacy-ops/activities/action`;
export const FETCH_DUTY_LIST = `${base_api}/api/v1/privacy-ops/activities/list-duty`;
export const EDIT_CONTROL_IN_REGULATION = `${base_api}/api/v1/privacy-ops/regulations/busi-req/`;
export const ACTIVITY_DOCUMENTS_UPLOAD = `${base_api}/api/v1/privacy-ops/activities/upload-documents`;
export const FETCH_REGULATION_CARDS_DATA = `${base_api}/api/v1/privacy-ops/regulations/compliance-status`;

export const ADD_DUTY = `${base_api}/api/v1/privacy-ops/activities/duty`;
export const UPDATE_DUTY = `${base_api}/api/v1/privacy-ops/activities/duty`;
export const DUTY_COUNT = `${base_api}/api/v1/privacy-ops/activities/duty-count`;
export const ADD_IMPROVEMENT = `${base_api}/api/v1/privacy-ops/activities/improvement`;
export const IMPROVEMENT_LIST = `${base_api}/api/v1/privacy-ops/activities/list-improvement`;
export const UPDATE_IMPROVEMENTS = `${base_api}/api/v1//privacy-ops/activities/improvement`;
export const IMPROVEMENT_COUNT = `${base_api}/api/v1//privacy-ops/activities/improvement-count`;

export const FETCH_ALL_RISKS = `${base_api}/api/v1/privacy-ops/risk-list`;
export const CREATE_RISK = `${base_api}/api/v1/privacy-ops/risk`;
export const FETCH_RISK_BY_ID = `${base_api}/api/v1/privacy-ops/risk`;
export const UPDATE_RISK_DETAILS = `${base_api}/api/v1/privacy-ops/risk`;
export const CATEGORY_LIST = `${base_api}/api/v1/privacy-ops/regulations/categories`;
export const CONTROLS_LIST = `${base_api}/api/v1/privacy-ops/regulations/controls`;
export const BUSINESS_REQUIREMENT = `${base_api}/api/v1/privacy-ops/regulations/control-busi-req`;
export const CUSTOM_REGULATIONS = `${base_api}/api/v1/privacy-ops/custom-regulations`;
export const CATEGORY_CONTROL_BUSINESS_REQUIREMENT = `${base_api}/api/v1/privacy-ops/regulations/category-control-busi-req

 `;
export const ADD_NEW_REGULATION = `${base_api}/api/v1/regulations`;

// data catalogue
export const FETCH_SERVICE_ENTRY_STRUCTURED = `${COMMON_ENDPOINT}/service-entity`;
export const FETCH_SCHEMA_ENTRY_STRUCTURED = `${COMMON_ENDPOINT}/schema-entity?fqnHash=`;
export const FETCH_TABLE_ENTRY_STRUCTURED = `${COMMON_ENDPOINT}/table-entity?fqnHash=`;
export const FETCH_PROFILE_ENTRY_STRUCTURED = `${COMMON_ENDPOINT}/profile-time-series?entityFQNHash=`;
export const FETCH__COLUMN_STRUCTURED = `${COMMON_ENDPOINT}/column-ner?table_id=`;
export const FETCH_PROFILER_META_DATA = `${COMMON_ENDPOINT}/profiler-meta`;
export const FETCH_STRUCTURED_META_DATA = `${DD_WAREHOUSE_ENDPOINT}/warehouse/structured_dashboard_metadata`;
export const FETCH_STRUCTURED_LIST = `${DD_WAREHOUSE_ENDPOINT}/warehouse/structured_dashboard_listing`;
export const FETCH_UNSTRUCTURED_META_DATA = `${DD_WAREHOUSE_ENDPOINT}/warehouse/unstructured_dashboard_metadata`;
export const FETCH_UNSTRUCTURED_LIST = `${DD_WAREHOUSE_ENDPOINT}/warehouse/unstructured_dashboard_listing`;
export const FETCH_SERVICE_ENTRY_UNSTRUCTURED = `${COMMON_ENDPOINT}/unstructured-catalogue`;
export const TEST_SERVICE_CREDS = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/credentials`;
export const FETCH_MICROSOFT_USER_LIST = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/list_all_users?credentials_id=`;
export const SCAN_SPECIFIC_USERS = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/scan_specific_user_service`;
export const FETCH_S3_BUCKET_LIST = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/list_s3_buckets?credentials_id=`;
export const SCAN_SPECIFIC_BUCKETS = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/scan_specific_s3_buckets`;
export const FETCH_GOOGlE_WORKSPACE_USER = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/list_google_workspace_users_auto?credentials_id=`;
export const SCAN_SPECIFIC_WORKSPACE_USERS = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/scan_google_workspace_users`;
export const SCAN_ALDS_SYSTEM = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/adls/scan`;
export const GET_CREDS_LIST = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/list_credentials?customer_id=`;
export const GET_INGESTION_LIST = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/ingestion-versions?cloud_credentials_id=`;
export const GET_MICROSOFT_SUBSERVICE = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/microsoft-365-subservices`;
export const GET_AWS_SUBSERVICE = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/aws-infrastructure-scanning`;
export const GET_GOOGLE_SUBSERVICE = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/google-workspace-subservices`;
export const GET_EC2_INSTANCES = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/list_ec2_instances?credentials_id=`;
export const GET_RDS_SUBSERVICE = `${COMMON_ENDPOINT_CLICKHOUSE}api/v1/Unstructured/list_rds_instances?credentials_id=`;
export const UPDATE_UNSTRUCTURED_PII = `${DD_WAREHOUSE_ENDPOINT}/warehouse/unstructured_dashboard_listing`;
export const UPDATE_STRUCTURED_PII = `${DD_WAREHOUSE_ENDPOINT}/warehouse/structured_dashboard_listing`;
export const PII_HANDBOOK = `${DD_WAREHOUSE_ENDPOINT}/warehouse/pii_details`;
export const ADD_PII_HANDBOOK = `${DD_WAREHOUSE_ENDPOINT}/warehouse/pii_detail`;
export const DELETE_PII_HANDBOOK = `${DD_WAREHOUSE_ENDPOINT}/warehouse/pii_tags_details`;

export const PII_DATA_ELEMENTS = `${base_api}/api/v1/data-element`;

//DD Services apis
export const SERVICE_CRUD = `${DD_INGESTION_ENDPOINT}/unstructure/service`;
export const GET_SERVICE_LIST = `${DD_INGESTION_ENDPOINT}/dd/services`;
export const INGESTION_CRUD = `${DD_INGESTION_ENDPOINT}/unstructure/ingestion`;
export const GET_INGESTION_LIST_BY_SERVICE = `${DD_INGESTION_ENDPOINT}/dd/ingestion`;
export const JOB_CRUD = `${DD_INGESTION_ENDPOINT}/unstructure/ingestion/{ingestion_id}/jobs`;
export const GET_JOB_LIST_BY_INGESTION_ID = `${DD_INGESTION_ENDPOINT}/dd/ingestion/{ingestion_id}/jobs`;
export const JOB_LOGS_CRUD = `${DD_INGESTION_ENDPOINT}/unstructure/ingestion_logs/{ingestion_id}/{job_id}`;

export const DD_STRUCTURED_SANKEY = `${DD_WAREHOUSE_ENDPOINT}/warehouse/structured_pii_flow`;
export const DD_UNSTRUCTURED_SANKEY = `${DD_WAREHOUSE_ENDPOINT}/warehouse/unstructured_pii_flow`;
export const DD_FILE_CLASSIFICATION = `${DD_PROFILER_ENDPOINT}/profile/upload-files?profile_type=pii`;

//DD Dashboard unstructured
export const GET_DATA_MANAGEMENT_INFO = `${COMMON_ENDPOINT}/unstructured-dashboard-data-management`;
export const GET_DASHBOARD_DOCUMENT = `${COMMON_ENDPOINT}/unstructured-dashboard-document`;
export const GET_DASHBOARD_FILE_FORMAT = `${COMMON_ENDPOINT}/unstructured-dashboard-file-format`;
export const GET_DASHBOARD_LOCATION = `${COMMON_ENDPOINT}/unstructured-dashboard-location`;
export const GET_SANKEY_GRAPH_DATA = `${COMMON_ENDPOINT}/unstructured-dashboard-sanky-graph`;
export const GET_DASHBOARD_SUMMARY = `${COMMON_ENDPOINT}/unstructured-dashboard-summary`;
export const GET_DASHBOARD_ELEMENTS = `${COMMON_ENDPOINT}/unstructured-dashboard-element`;
export const GET_DASHBOARD_VIEW = `${COMMON_ENDPOINT}/unstructured-dashboard-view`;
export const GET_DASHBOARD_SENSITIVITY = `${COMMON_ENDPOINT}/unstructured-dashboard-sensitivity`;

//DD Dashboard structured
export const GET_STRUCTURED_DASHBOARD_LOCATION_SUMMARY = `${COMMON_ENDPOINT}/structured-dashboard-view-location`;
export const GET_STRUCTURED_DASHBOARD_DATA_DETAILS = `${COMMON_ENDPOINT}/structured-dashboard-view-details`;
export const GET_STRUCTURED_DASHBOARD_VIEW_ELEMENT_CATEGORIES = `${COMMON_ENDPOINT}/structured-dashboard-element-categories`;
export const GET_STRUCTURED_DASHBOARD_DATA_MANAGEMENT = `${COMMON_ENDPOINT}/structured-dashboard-data-management`;
export const GET_STRUCTURED_DASHBOARD_ELEMENTS_TYPES = `${COMMON_ENDPOINT}/structured-dashboard-element-types`;
export const GET_SYSTEM_ELEMENT_TYPES = `${COMMON_ENDPOINT}/structured-unstructured-data-element`;
export const GET_SYSTEM_ELEMENTS = `${DD_WAREHOUSE_ENDPOINT}/warehouse/pii_type_listing`;
export const PII_ANALYSIS = `${DD_WAREHOUSE_ENDPOINT}/warehouse/pii_source_location_value`;
export const GET_STRUCTURED_DASHBOARD_LOCATION = `${COMMON_ENDPOINT}/structured-dashboard-location`;
export const GET_STRUCTURED_DASHBOARD_SANKEY_DATA = `${COMMON_ENDPOINT}/structured-dashboard-sanky-graph`;
export const GET_STRUCTURED_DASHBOARD_SENSITIVITY = `${COMMON_ENDPOINT}/structured-dashboard-sensitivity`;

//data mapping
export const UPLOAD_FILES_TO_SCAN = `${dd_file_classifier_endpoint}/instant-pii-scanner/process-files/`;

//incident and breach management

export const GET_INCIDENT_DETAILS = `${COMMON_ENDPOINT}/data-breach-management`;
export const CREATE_INCIDENT = `${COMMON_ENDPOINT}/data-breach-management/create-incident`;
export const BREACH_DASHBOARD_COUNT = `${COMMON_ENDPOINT}/data-breach-dashboard/count`;
export const BREACH_DASHBOARD_SEVERITY_DISTRIBUTION = `${COMMON_ENDPOINT}/data-breach-dashboard/severity-distribution`;
export const BREACH_DASHBOARD_STATUS_DISTRIBUTION = `${COMMON_ENDPOINT}/data-breach-dashboard/status-distribution`;
export const BREACH_MANAGEMENT_UPLOAD_DOCS = `${COMMON_ENDPOINT}/data-breach-management/upload-documents`;
export const BREACH_NOTIFICATIONS_TO_REGULATIONS = `${COMMON_ENDPOINT}/data-breach-management/add-data-breach-notification`;
