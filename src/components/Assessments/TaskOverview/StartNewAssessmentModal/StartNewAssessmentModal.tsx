import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from '../../../../@/components/ui/dialog';
// import {
//   Select,
//   SelectContent,
//   SelectGroup,
//   SelectItem,
//   SelectLabel,
//   SelectTrigger,
//   SelectValue,
// } from '../../../../@/components/ui/Common/Elements/Select/Select';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import addSign from '../../../../assets/plusSign.svg';
// import { Label } from '../../../../@/components/ui/Common/Elements/Label/Label';
import { useState } from 'react';
// import { useSelector } from 'react-redux';
// import { RootState } from '../../../../redux/store';
// import {
//   // createNewAssessment,
//   fetchAssessments,
//   fetchDepartments,
//   fetchProcesses,
// } from '../../../common/services/assessment';
// import { Fetch_Task_Overview_Assign_Data } from '../../../common/services/allPolicy';
// import { useDispatch } from 'react-redux';
// import {
//   setReloadTaskOverviewData,
//   setSelectedEntityId as setEntity,
// } from '../../../../redux/reducers/Assessment/AssessmentTaskOverviewSlice';
import StartNewAssessmentForm from './Form/start-new-assessment-form';

// interface Assessments {
//   id: number;
//   assessment_name: string;
//   key: string;
//   type: string;
// }
// interface Department {
//   id: number;
//   name: string;
// }

// function convertToTitleCase(string_: string): string {
//   // Split the string into words based on underscores
//   const words = string_.split('_');

//   // Capitalize the first letter of each word
//   const capitalizedWords = words.map((word) => word.charAt(0).toUpperCase() + word.slice(1));

//   // Join the capitalized words with spaces
//   return capitalizedWords.join(' ');
// }

const StartNewAssessmentModal = () => {
  // const dispatch = useDispatch();

  //! USE SELECTOR
  // const { entities, reloadTaskOverviewData } = useSelector(
  //   (state: RootState) => state.assessmentTaskOverview
  // );
  // const loginData = useSelector((state: any) => state.auth.login.login_details);

  //! USE STATE
  // const [usersData, setUsersData] = useState([]);
  // const [processes, setProcesses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  // const [isSubmitted, setIsSubmitted] = useState(false);
  // const [processId, setProcessId] = useState<any>('-1');
  // const [assigneeId, setAssigneeId] = useState<any>('-1');
  // const [reviewerId, setReviewerId] = useState<any>('-1');
  // const [departmentId, setDepartmentId] = useState<any>('-1');
  const [isDialogBoxOpen, setIsDialogBoxOpen] = useState(false);
  // const [departments, setDepartments] = useState<Department[]>([]);
  // const [assessments, setAssessments] = useState<Assessments[]>([]);
  // const [selectedEntityId, setSelectedEntityId] = useState<any>('-1');
  // const [selectedAssessmentId, setSelectedAssessmentId] = useState<any>('-1');

  //! VARIABLE DECLARATIONS

  // let customerId = 0;
  // if (loginData !== null) {
  //   customerId = loginData.customer_id;
  // }

  // const groupedAssessmentData = new Map<string, Assessments[]>();
  // if (assessments.length > 0) {
  //   for (const assessment of assessments) {
  //     if (groupedAssessmentData?.has(assessment.type)) {
  //       groupedAssessmentData?.get(assessment.type)?.push(assessment);
  //     } else {
  //       groupedAssessmentData?.set(assessment.type, [assessment]);
  //     }
  //   }
  // }

  // for (const key in groupedAssessmentData) {
  //   console.log("Key: " + key + ". Value: " + groupedAssessmentData[key]);
  // }

  //! USE EFFECT

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       setIsLoading(true);
  //       const response = await fetchDepartments(selectedEntityId);
  //       if (response.status !== 200) {
  //         throw new Error('Failed to fetch data.');
  //       }

  //       console.log(response);
  //       setDepartments(response?.data?.result?.rows);
  //     } catch (error) {
  //       if (error instanceof Error) {
  //         console.log('Fetching department data failed!');
  //       } else {
  //         console.log('An unknown error occurred:', error);
  //       }
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   if (selectedEntityId !== '-1') fetchData();
  // }, [selectedEntityId]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       setIsLoading(true);
  //       const response = await fetchProcesses(departmentId);
  //       if (response.status !== 200) {
  //         throw new Error('Failed to fetch data.');
  //       }

  //       setProcesses(response?.data?.result?.rows);
  //     } catch (error) {
  //       if (error instanceof Error) {
  //         console.log('Fetching process data failed!');
  //       } else {
  //         console.log('An unknown error occurred:', error);
  //       }
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   if (departmentId !== '-1') fetchData();
  // }, [departmentId]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       setIsLoading(true);
  //       const response = await Fetch_Task_Overview_Assign_Data('', customerId, selectedEntityId);
  //       if (!response.ok) {
  //         throw new Error('Failed to fetch data.');
  //       }
  //       const responseData = await response.json();
  //       setUsersData(responseData?.result?.rows);
  //     } catch (error) {
  //       if (error instanceof Error) {
  //         console.log('Fetching user data failed!');
  //       } else {
  //         console.log('An unknown error occurred:', error);
  //       }
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   if (selectedEntityId !== '-1') fetchData();
  // }, [selectedEntityId, customerId]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       setIsLoading(true);
  //       const responseData = await fetchAssessments();
  //       console.log(responseData);
  //       setAssessments(responseData);
  //     } catch (error) {
  //       if (error instanceof Error) {
  //         console.log('Fetching user data failed!', error.message);
  //       } else {
  //         console.log('An unknown error occurred:', error);
  //       }
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   fetchData();
  // }, []);

  //! HANDLER FUNCTIONS

  // const handleEntityChange = (value: string) => {
  //   setSelectedEntityId(Number(value));
  //   setDepartmentId('-1');
  //   setProcessId('-1');
  //   setReviewerId('-1');
  //   setAssigneeId('-1');
  // };

  // console.log(departmentId, typeof departmentId);
  // console.log(processId, typeof processId);
  // const handleDepartmentChange = (value: string) => {
  //   setDepartmentId(Number(value));
  // };

  // const handleProcessChange = (value: string) => {
  //   setProcessId(Number(value));
  // };

  // function handleReviewerChange(value: string) {
  //   setReviewerId(Number(value));
  // }
  // function handleAssigneeChange(value: string) {
  //   setAssigneeId(Number(value));
  // }
  // function handleAssessmentChange(value: string) {
  //   setSelectedAssessmentId(Number(value));
  // }

  // async function handleSubmit() {
  //   setIsSubmitted(true);
  //   if (
  //     selectedAssessmentId === '-1' ||
  //     selectedEntityId === '-1' ||
  //     reviewerId === '-1' ||
  //     assigneeId === '-1'
  //   ) {
  //     return;
  //   }

  //   let data: any = {
  //     assessment_id: selectedAssessmentId,
  //     entity_id: selectedEntityId,
  //     assigned_to: assigneeId,
  //     status: 'Yet to Start',
  //     reviewer_id: reviewerId,
  //   };

  //   if (departmentId !== '-1') {
  //     data = {
  //       ...data,
  //       ['department_id']: departmentId,
  //     };
  //   }

  //   if (processId !== '-1') {
  //     data = {
  //       ...data,
  //       ['process_id']: processId,
  //     };
  //   }

  //   console.log('Submit Body->', data);
  //   try {
  //     const response = await createNewAssessment(data);
  //     console.log('Response->', response);

  //     setSelectedAssessmentId('-1');
  //     setSelectedEntityId('-1');
  //     setDepartmentId('-1');
  //     setProcessId('-1');
  //     setReviewerId('-1');
  //     setAssigneeId('-1');

  //     setIsSubmitted(false);

  //     dispatch(setEntity(selectedEntityId));
  //     dispatch(setReloadTaskOverviewData(!reloadTaskOverviewData));
  //   } catch (error: any) {
  //     console.log(error);
  //   } finally {
  //     setIsDialogBoxOpen(false);
  //   }
  // }

  return (
    <Dialog open={isDialogBoxOpen} onOpenChange={setIsDialogBoxOpen}>
      <DialogTrigger asChild>
        <Button
          className="bg-custom-primary hover:bg-custom-primary"
          onClick={() => setIsDialogBoxOpen(true)}
        >
          <img src={addSign} alt="plus sign" className="size-4" />
          <span className="text-primary-background">Start New Assessment</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[550px] overflow-y-auto font-primary-text sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Start New Assessment</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col justify-between gap-4 font-primary-text">
          {/* <div className="flex flex-col flex-wrap gap-1">
            <div>
              <Label
                htmlFor="name"
                className="text-left text-black text-sm font-medium leading-tight"
              >
                Assessment Name
              </Label>
              <span className="text-red-500">*</span>
            </div>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select an Assessment" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Impact Assessment</SelectLabel>
                  <SelectItem value="Impact">
                    Legitimate Interest Assessment
                  </SelectItem>
                  <SelectItem value="EU">Privacy Impact Assessment</SelectItem>
                  <SelectItem value="Regulatory">
                    Transfer Impact Assessment
                  </SelectItem>
                </SelectGroup>
                <SelectGroup>
                  <SelectLabel>EU AI Assessment</SelectLabel>
                  <SelectItem value="Conformity">
                    Conformity Assessment
                  </SelectItem>
                  <SelectItem value="Ethical">
                    Ethical Impact Assessment{" "}
                  </SelectItem>
                  <SelectItem value="Transfer">
                    Transfer Impact Assessment
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div> */}
          {/* <div className="flex flex-col flex-wrap gap-1">
            <div>
              <Label
                htmlFor="name"
                className={`text-left ${
                  isSubmitted && selectedAssessmentId === '-1' ? 'text-red-500' : 'text-black'
                }  text-sm font-medium leading-tight`}
              >
                Assessment Name
              </Label>
              <span className="text-red-500">*</span>
            </div>
            <Select
              onValueChange={handleAssessmentChange}
              defaultValue={selectedAssessmentId.toString()}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="-1" disabled>
                  Select
                </SelectItem>
                {Array?.from(groupedAssessmentData?.entries())?.map(([type, assessments]) => (
                  <SelectGroup key={type}>
                    <SelectLabel>{convertToTitleCase(type)}</SelectLabel>
                    {assessments.map((assessment) => (
                      <SelectItem key={assessment.id} value={assessment.id.toString()}>
                        {assessment.assessment_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                ))}
              </SelectContent>
            </Select>
          </div> */}
          {/* <div className="flex flex-col flex-wrap gap-1">
            <div>
              <Label
                htmlFor="name"
                className={`text-left ${
                  isSubmitted && selectedEntityId === '-1' ? 'text-red-500' : 'text-black'
                }  text-sm font-medium leading-tight`}
              >
                Entity
              </Label>
              <span className="text-red-500">*</span>
            </div>
            <Select onValueChange={handleEntityChange} defaultValue={selectedEntityId.toString()}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="-1" disabled>
                    Select
                  </SelectItem>
                  {entities?.map((entity) => (
                    <SelectItem key={entity.id} value={entity.id.toString()} id={entity.name}>
                      {entity.name}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div> */}
          {/* {selectedEntityId !== '-1' && (
            <div className="flex flex-col flex-wrap gap-1">
              <div>
                <Label
                  htmlFor="name"
                  className="text-left text-black text-sm font-medium leading-tight"
                >
                  Department
                </Label>
              </div>
              <Select
                value={departmentId.toString()}
                onValueChange={handleDepartmentChange}
                defaultValue={departmentId.toString()}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="-1" disabled>
                      Select
                    </SelectItem>
                    {departments.length > 0 ? (
                      departments?.map((item) => (
                        <SelectItem key={item.id} value={item.id.toString()} id={item.name}>
                          {item.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="-2" disabled>
                        No Data
                      </SelectItem>
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          )} */}
          {/* {departmentId !== '-1' && (
            <div className="flex flex-col flex-wrap gap-1">
              <div>
                <Label
                  htmlFor="name"
                  className="text-left text-black text-sm font-medium leading-tight"
                >
                  Process
                </Label>
              </div>
              <Select
                value={processId.toString()}
                onValueChange={handleProcessChange}
                defaultValue={processId.toString()}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="-1" disabled>
                      Select
                    </SelectItem>
                    {processes.length > 0 ? (
                      processes?.map((item: any) => (
                        <SelectItem key={item.id} value={item.id.toString()} id={item.name}>
                          {item.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="-2" disabled>
                        No Data
                      </SelectItem>
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          )} */}
          {/* {selectedEntityId !== '-1' && (
            <div className="flex flex-col flex-wrap gap-1">
              <div>
                <Label
                  htmlFor="name"
                  className={`text-left ${
                    isSubmitted && assigneeId === '-1' ? 'text-red-500' : 'text-black'
                  }  text-sm font-medium leading-tight`}
                >
                  Assignee
                </Label>
                <span className="text-red-500">*</span>
              </div>
              <Select
                value={assigneeId.toString()}
                defaultValue={assigneeId.toString()}
                onValueChange={handleAssigneeChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="-1" disabled>
                      Select
                    </SelectItem>
                    {usersData.length > 0 ? (
                      usersData?.map((item: any, index: any) => (
                        <SelectItem key={item.id} value={item.id.toString()}>
                          {`${item?.firstName} ${item?.lastName}`}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="-2" disabled>
                        No Data
                      </SelectItem>
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          )} */}
          {/* {selectedEntityId !== '-1' && (
            <div className="flex flex-col flex-wrap gap-1">
              <div>
                <Label
                  htmlFor="name"
                  className={`text-left ${
                    isSubmitted && reviewerId === '-1' ? 'text-red-500' : 'text-black'
                  }  text-sm font-medium leading-tight`}
                >
                  Reviewer
                </Label>
                <span className="text-red-500">*</span>
              </div>
              <Select
                value={reviewerId.toString()}
                defaultValue={reviewerId.toString()}
                onValueChange={handleReviewerChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="-1" disabled>
                      Select
                    </SelectItem>
                    {usersData.length > 0 ? (
                      usersData?.map((item: any, index: any) => (
                        <SelectItem key={item.id} value={item.id.toString()}>
                          {`${item?.firstName} ${item?.lastName}`}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="-2" disabled>
                        No Data
                      </SelectItem>
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          )} */}
          {/* {isLoading && (
            <div className="flex justify-center items-center">
              <svg
                aria-hidden="true"
                className="w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentFill"
                />
              </svg>
            </div>
          )} */}
        </div>
        <StartNewAssessmentForm
          setIsDialogBoxOpen={setIsDialogBoxOpen}
          loading={isLoading}
          setloading={setIsLoading}
        />
        <DialogFooter>
          <DialogClose>
            <Button variant="secondary">Cancel</Button>
          </DialogClose>

          <Button
            type="submit"
            form="newAssessmentForm"
            className="bg-custom-primary text-tertiary-text hover:bg-custom-primary"
            // onClick={handleSubmit}
            disabled={isLoading}
          >
            Submit
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StartNewAssessmentModal;
