import axios from 'axios';
import Cookies from 'js-cookie';
import httpClient from '../../../api/httpClient';
import { api_key_work as api_key, ucm_gotrust_basi_api } from '../../../utils/helperData';
import {
  COOKIE_ADDITIONAL_MATRIX,
  COOKIE_DASHBOARD_CARD_DATA,
  DELETE_COOKIE_CONSENT_DOMAIN,
  FETCH_ANALYTICS_DATA,
  FETCH_COOKIE_LEGAL_FRAMEWORKS,
  FETCH_ENTITIES,
  FETCH_GEOGRAPHIC_DISTIBUTION,
  FETCH_OBSERVATION_DATA,
  GET_COOKIE_RISK_SCORE,
  POST_AUTO_SCAN,
  TRANSLATE_COOKIE_CATEGORY,
  TRANSLATE_COOKIE_ITSELF,
  TRANSLATE_COOKIE_SERVICE,
  UPDATE_ANWER_DOCUMENT,
} from '../api';

//? Domain Listing API
export const get_domain_list = async (customer_id: number | undefined) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/dashboard/display-domains-list-per-customer?customer_id=${customer_id}`,
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

//? Consent Record Table

//? Consent Counts
export const get_consent_rate_data = async (
  customer_id: number | undefined,
  domain_id: number | undefined
) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let query = '';
    if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/api/v2/dashboard/display-rates?customer_id=${customer_id}${query}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

//? Dashboard Bar Graph Data
export const get_monthly_status_count_data = async (
  customer_id: number | undefined,
  domain_id: number | undefined
) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let query = '';
    if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/api/v2/dashboard/monthly-status-counts?customer_id=${customer_id}${query}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

//? Dashboard Consent by Residency
export const get_consent_by_residency_data = async (
  customer_id: number | undefined,
  domain_id: number | undefined
) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let query = '';
    if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/api/v2/dashboard/continent-consent-counts?customer_id=${customer_id}${query}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

//? Dashboard Consent by Residency
export const get_domain_wise_consent_status = async (
  customer_id: number | undefined,
  domain_id: number | undefined
) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let query = '';
    if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/dashboard/domain-consent-counts?customer_id=${customer_id}${query}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

//? Dashboard Cookie Category & Consent Status
export const get_cookie_category_consent_status = async (
  customer_id: number | undefined,
  domain_id: number | undefined
) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    let query = '';
    if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/api/v2/dashboard/category-consent-counts?customer_id=${customer_id}${query}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );

    console.log('11', response.data);

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};
export const put_step2_data = async (data: object) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.put(`${ucm_gotrust_basi_api}/domain_route/step2`, data, {
      headers: {
        Authorization: 'Bearer ' + access_token,
        'Content-Type': 'application/json', // Specify the content type
      },
    });

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in put_cookie_service:', error);
    throw error; // Handle the error appropriately
  }
};

export const auto_scan_cookie = async (domain_id: number) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.post(
      `${POST_AUTO_SCAN}`,
      {
        domain_id,
      },
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          'Content-Type': 'application/json', // Specify the content type
        },
      }
    );

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in put_cookie_service:', error);
    throw error; // Handle the error appropriately
  }
};

//? Create Cookie -> POST Cookie Configuration Data
export const post_cookie_config_basic_info = async (
  body: object,
  step: string,
  domain_id?: number,
  method: 'post' | 'put' = 'post' // default is 'post'
) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    // Dynamically use 'post' or 'put' method
    const response = await axios({
      method, // This will be either 'post' or 'put'
      url: `${ucm_gotrust_basi_api}/domain_route/${step}${domain_id ? `?domain_id=${domain_id}` : ''}`,
      data: body,
      headers: {
        Authorization: 'Bearer ' + access_token,
        Accept: 'application/json',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);
    throw error; // Rethrow the error after handling
  }
};

export const get_cookies_categories = async (domain_id: number) => {
  try {
    const access_token = Cookies.get('access_token');
    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await axios.get(
      `${ucm_gotrust_basi_api}/cookie_consent_record/cookie-category?domain_id=${domain_id}`,
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          Accept: 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error;
  }
};
export const post_create_category = async (body: object, method: 'post' | 'put') => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await axios({
      method,
      url: `${ucm_gotrust_basi_api}/cookie_consent_record/cookie-category`,
      data: body,
      headers: {
        // 'x-api-key': api_key,
        Authorization: 'Bearer ' + access_token,
        Accept: 'application/json',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

export const get_Next_Page_Data = async (id: any) => {
  try {
    const access_token = Cookies.get('access_token');
    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await axios.get(
      `${ucm_gotrust_basi_api}/domain_route/next?domain_id=${id}&customer_id=496&next_step=2`,
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          Accept: 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error;
  }
};
export const get_cookies_services = async (domain_id: number) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let query = '';
    // if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/cookie_classification/cookie-service?domain_id=${domain_id}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};
export const get_cookies_cookiesetup = async (domain_id: number) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let query = '';
    // if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/domain_cookie/all?domain_id=${domain_id}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};
export const get_Consent_Domain = async (domain_id: number) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let query = '';
    // if (domain_id !== 0) query = `&domain_id=${domain_id}`;

    const response = await axios.get(
      `${ucm_gotrust_basi_api}/domain_route/domain/step5?domain_id=${domain_id}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          // Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

export const post_create_services = async (body: object) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await axios.post(
      `${ucm_gotrust_basi_api}/cookie_classification/cookie-service`,
      body,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};
export const post_create_cookies = async (body: object) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await axios.post(`${ucm_gotrust_basi_api}/domain_cookie/`, body, {
      headers: {
        // 'x-api-key': api_key,
        Authorization: 'Bearer ' + access_token,
        Accept: 'application/json',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};
//? Fetch Entities
export const get_entities = async (id: number | undefined) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.get(`${FETCH_ENTITIES}${id}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: 'Bearer ' + access_token,
      },
    });

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

//? Fetch Legal Framework
export const get_legal_frameworks = async (customer_id: number | undefined) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.get(
      `${FETCH_COOKIE_LEGAL_FRAMEWORKS}?customer_id=${customer_id}`,
      {
        headers: {
          'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

//? Dashboard Consent by Residency
export const get_cookie_consent_domain = async (
  customer_id: number | undefined,
  search?: string,
  page?: number,
  pageSize?: number
) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    let queryParams = [`customer_id=${customer_id}`];

    if (search && search.trim() !== '') {
      queryParams.push(`search=${encodeURIComponent(search.trim())}`);
    }

    if (page !== undefined) {
      queryParams.push(`page=${page}`);
    }

    if (pageSize !== undefined) {
      queryParams.push(`page_size=${pageSize}`);
    }

    const queryString = queryParams.join('&');

    const response = await axios.get(`${ucm_gotrust_basi_api}/domain_route/?${queryString}`, {
      headers: {
        Authorization: 'Bearer ' + access_token,
      },
    });

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);
    throw error;
  }
};

//? Create Cookie -> POST Cookie Configuration Data
export const get_cookie_domain_step_data = async (step: string, domain_id?: number) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await axios.get(
      `${ucm_gotrust_basi_api}/domain_route/domain/${step}${domain_id ? `?domain_id=${domain_id}` : ''}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

export const uploadFile = async (file: FormData) => {
  try {
    const access_token = Cookies.get('access_token');
    console.log('File:', file);

    const response = await axios.post(`${UPDATE_ANWER_DOCUMENT}`, file, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });

    return response.data; // Return the response data directly
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Axios error:', error.response?.data || error.message);
      // Customize error handling for specific error types
      if (error.response) {
        // Server-side error (response status code outside the range of 2xx)
        return {
          status: error.response.status,
          message: error.response.data?.message || 'Upload failed due to server error.',
        };
      } else if (error.request) {
        // No response received from the server
        return { status: 500, message: 'No response from server.' };
      }
    } else {
      // Non-Axios error (e.g., network issue)
      console.error('Unexpected error:', error);
      return { status: 500, message: 'Unexpected error occurred.' };
    }
  }
};
// Put in services
export const put_cookie_service = async (data: object) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.put(
      `${ucm_gotrust_basi_api}/cookie_classification/cookie-service`,
      data, // Sending data in the body as JSON
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          'Content-Type': 'application/json', // Specify the content type
        },
      }
    );

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in put_cookie_service:', error);
    throw error; // Handle the error appropriately
  }
};
// put in cookies
export const put_cookie = async (data: object) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.put(
      `${ucm_gotrust_basi_api}/domain_cookie/`,
      data, // Sending data in the body as JSON
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          'Content-Type': 'application/json', // Specify the content type
        },
      }
    );

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in put_cookie_service:', error);
    throw error; // Handle the error appropriately
  }
};
// get autoscan data

export const get_autoscan_data = async (domain_id?: number) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await axios.get(
      `${ucm_gotrust_basi_api}/domain_route/domain/step2?domain_id=${domain_id}`,
      {
        headers: {
          // 'x-api-key': api_key,
          Authorization: 'Bearer ' + access_token,
          Accept: 'application/json',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error; // Rethrow the error after handling
  }
};

export const get_banner_details = async (domain_id: number, selectedLanguage?: string) => {
  try {
    const access_token = Cookies.get('access_token');
    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const queryUrl = selectedLanguage
      ? `?domain_id=${domain_id}&language_code=${selectedLanguage}`
      : `?domain_id=${domain_id}`;
    const response = await axios.get(
      `${ucm_gotrust_basi_api}/domain_route/domain/step4${queryUrl}`,
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          Accept: 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error;
  }
};

export const get_languages = async (domain_id?: number, regulation_id?: number) => {
  try {
    const access_token = Cookies.get('access_token');
    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const query = domain_id ? `?domain_id=${domain_id}` : '';
    const query1 = regulation_id ? `&regulation_id=${regulation_id}` : '';
    const response = await axios.get(`${ucm_gotrust_basi_api}/cp${query}${query1}`, {
      headers: {
        Authorization: 'Bearer ' + access_token,
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error;
  }
};

export const get_translated_data = async (domain_id: number, selectedLanguage: string) => {
  try {
    const access_token = Cookies.get('access_token');
    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const query = domain_id ? `?domain_id=${domain_id}` : '';
    const response = await axios.get(`${ucm_gotrust_basi_api}/cp${query}`, {
      headers: {
        Authorization: 'Bearer ' + access_token,
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Unexpected Error:', error);

    throw error;
  }
};

// put in cookies
export const save_translated_data = async (data: object) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.post(
      `${ucm_gotrust_basi_api}/lang/create`,
      data, // Sending data in the body as JSON
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          'Content-Type': 'application/json', // Specify the content type
        },
      }
    );

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in put_cookie_service:', error);
    throw error; // Handle the error appropriately
  }
};

// put in cookies
export const update_translated_data = async (data: object) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await axios.put(
      `${ucm_gotrust_basi_api}/lang/edit`,
      data, // Sending data in the body as JSON
      {
        headers: {
          Authorization: 'Bearer ' + access_token,
          'Content-Type': 'application/json', // Specify the content type
        },
      }
    );

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in put_cookie_service:', error);
    throw error; // Handle the error appropriately
  }
};

export const translate_cookie_category = async (data: object, method: string) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }
    const response = await httpClient[method === 'put' ? 'put' : 'post'](
      TRANSLATE_COOKIE_CATEGORY,
      data
    );
    return response.data;
  } catch (error) {
    console.error('Error in translate_cookie_category:', error);
    throw error; // Handle the error appropriately
  }
};

export const translate_cookie_service = async (data: object, method: string) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await httpClient[method === 'put' ? 'put' : 'post'](
      TRANSLATE_COOKIE_SERVICE,
      data
    );

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in translate_cookie_service:', error);
    throw error; // Handle the error appropriately
  }
};

export const translate_cookie_itself = async (data: object, method: string) => {
  try {
    const access_token = Cookies.get('access_token');

    if (!access_token) {
      throw new Error('Access token is missing.');
    }

    const response = await httpClient[method === 'put' ? 'put' : 'post'](
      TRANSLATE_COOKIE_ITSELF,
      data
    );

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in translate_cookie_itself:', error);
    throw error; // Handle the error appropriately
  }
};

export const create_observation = async (
  domainId: number | undefined,
  domainCookieId: number | undefined,
  observation: string | undefined
) => {
  const body = {
    domain_id: domainId,
    domain_cookie_id: domainCookieId,
    observation,
  };
  try {
    const response = await httpClient.post(FETCH_OBSERVATION_DATA, body);

    return response.data; // Return the response data if necessary
  } catch (error) {
    console.error('Error in translate_cookie_itself:', error);
    throw error; // Handle the error appropriately
  }
};

export const cookie_additional_matrix = async (
  customer_id: number | undefined,
  domain_id: number | undefined
) => {
  try {
    const response = await httpClient.get(
      `${COOKIE_ADDITIONAL_MATRIX}?customer_id=${customer_id}${domain_id ? `&domain_id=${domain_id}` : ''}`
    );
    return response.data;
  } catch (error) {
    console.error('Error in translate_cookie_itself:', error);
    throw error; // Handle the error appropriately
  }
};

export const cookie_dashboard_card_data = async (
  customer_id: number | undefined,
  domainID: number | undefined
) => {
  try {
    const response = await httpClient.get(
      `${COOKIE_DASHBOARD_CARD_DATA}?customer_id=${customer_id}&domain_id=${domainID}`
    );
    return response.data;
  } catch (error) {
    console.error('Error in translate_cookie_itself:', error);
    throw error;
  }
};
export const delete_cookie = async (domainId: number | undefined) => {
  try {
    const response = await httpClient.patch(
      `${DELETE_COOKIE_CONSENT_DOMAIN}?domain_id=${domainId}`
    );
    return response.data;
  } catch (error) {
    console.error('Error in translate_cookie_itself:', error);
    throw error;
  }
};

export const get_cookie_risk_score = async (domain_id: number) => {
  try {
    const response = await httpClient.get(`${GET_COOKIE_RISK_SCORE}?domain_id=${domain_id}`);
    return response.data;
  } catch (error) {
    console.error('Error in translate_cookie_itself:', error);
    throw error;
  }
};

export const get_geographic_distribution = async (
  customer_id: number | undefined,
  domain_id: number | undefined
) => {
  try {
    const response = await httpClient.get(
      `${FETCH_GEOGRAPHIC_DISTIBUTION}?customer_id=${customer_id}${domain_id ? `&domain_id=${domain_id}` : ''}`
    );
    return response.data;
  } catch (error) {
    console.error('Error in fetching data:', error);
    throw error;
  }
};

export const get_analytics_data = async (customer_id : number)=>{
    try{
      const response = await httpClient.get(`${FETCH_ANALYTICS_DATA}/${customer_id}`);
      return response.data;
    }
    catch(error){
      console.error('Error in fetching data:', error);
      throw error;
    }
}
