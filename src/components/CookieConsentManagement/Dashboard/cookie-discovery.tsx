import { Switch } from '@radix-ui/react-switch';
import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { Button } from '../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import httpClient from '../../../api/httpClientNew';
import { RootState } from '../../../redux/store';
import { ICookieDetails } from '../../../types/cookie-consent-management';
import { FETCH_COOKIES_BY_CATEGORY, GET_COOKIE_DICTIONARY } from '../../common/api';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import { ChartCardLoader } from '../../common/LoadingUI';
import DynamicTable from '../../common/ShadcnDynamicTable/dynamic-table';
import { toHumanReadable } from '../CookieDictionary/cookie-dictionary';
import { EditCookieModal } from '../CookieDictionary/edit-cookie-modal';

export function CookieDiscovery({ selectedDomainId }: { selectedDomainId: number }) {
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );

  const { data: cookieCategoryData, isLoading: loadingCategoryData } = useQuery({
    queryKey: ['cookie_category_new_data', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await httpClient.get(
        `${FETCH_COOKIES_BY_CATEGORY}?customer_id=${customer_id}${selectedDomainId ? `&domain_id=${selectedDomainId}` : ''}`
      );
      return response?.data?.data?.result;
    },
    enabled: !!customer_id,
  });

  console.log(cookieCategoryData, 'cookieCategoryData');

  const [reloadCookieData, setReloadCookieData] = useState(false);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newSize: number) => {
    setSize(newSize);
    setPage(1);
  };

  const {
    data: cookieTableData,
    isLoading: isLoadingCookieDictionary,
    refetch,
  } = useQuery<ICookieDetails[]>({
    queryKey: ['GET_COOKIE_DICTIONARY', reloadCookieData, page, size, selectedDomainId],
    queryFn: async () => {
      try {
        let domainQuery = '';
        if (selectedDomainId !== 0) domainQuery = `&domain_id=${selectedDomainId}`;
        const response = await httpClient.get(
          `${GET_COOKIE_DICTIONARY}${customer_id}&page=${page}&page_size=${size}${domainQuery}`
        );
        setTotalCount(response?.data?.result?.count || 0);
        return response?.data?.result?.data || [];
      } catch (error) {
        console.log(error);
        setTotalCount(0);
      }
    },
    refetchOnWindowFocus: false,
  });
  const generateColumns = (data: ICookieDetails[]): ColumnDef<ICookieDetails>[] => {
    if (data.length === 0) return [];

    const keys = Object.keys(data[0]) as (keyof ICookieDetails)[];

    const filteredKeys = [
      ...keys?.filter(
        (item) =>
          item !== 'cookie_service_id' &&
          item !== 'id' &&
          item !== 'cookie_category_id' &&
          item !== 'customer_id' &&
          item !== 'domain_id'
      ),
      'action',
    ];

    return filteredKeys.map((key) => ({
      accessorKey: key,
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {toHumanReadable(key as string)}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        return key === 'action' ? (
          <EditCookieModal cookie={row.original} refetch={refetch} />
        ) : key === 'is_necessary_cookie' ||
          key === 'status' ||
          key === 'show_status' ||
          key === 'Secure' ||
          key === 'HttpOnly' ? (
          <Switch id={key} checked={row.getValue(key)} />
        ) : key === 'created_at' ||
          key === 'updated_at' ||
          key === 'show_status_changed_at' ||
          key === 'status_changed_at' ? (
          <div className="">
            {row.getValue(key) ? convertDateToHumanView(row.getValue(key)) : '-'}
          </div>
        ) : (
          <div className="">{row.getValue(key) ? row.getValue(key) : '-'}</div>
        );
      },
    }));
  };
  const columns: ColumnDef<ICookieDetails>[] = generateColumns(cookieTableData || []);

  return (
    <div className="space-y-6">
      {loadingCategoryData ? (
        <div className="flex items-center justify-center">
          <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
        </div>
      ) : (
        <div className={`mt-4 grid grid-cols-1 gap-6 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6`}>
          {cookieCategoryData?.length > 0 ? (
            cookieCategoryData?.map((categoryItem: any) => (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className={`text-lg font-medium`}>
                    {categoryItem?.category_name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div
                    className={`text-3xl font-semibold ${categoryItem?.category_name === 'Functional' ? 'text-blue-600' : categoryItem?.category_name === 'Essential' ? 'text-green-600' : categoryItem?.category_name === 'Analytics' ? 'text-purple-600' : categoryItem?.category_name === 'Advertising' ? 'text-yellow-600' : categoryItem?.category_name === 'Performance' ? 'text-orange-600' : 'text-gray-600'}`}
                  >
                    {categoryItem?.cookie_count}
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <></>
          )}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Cookie Inventory</CardTitle>
          <CardDescription>All discovered cookies across your domains</CardDescription>
        </CardHeader>
        <CardContent>
          <DynamicTable<ICookieDetails>
            data={cookieTableData || []}
            loading={isLoadingCookieDictionary}
            columns={columns}
            enableSorting
            ServerSidePaginationDetails={{
              totalRecords: totalCount,
              currentPage: page,
              currentSize: size,
              handlePageSizeChange: handlePageSizeChange,
              handlePageNumberChange: handlePageChange,
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
