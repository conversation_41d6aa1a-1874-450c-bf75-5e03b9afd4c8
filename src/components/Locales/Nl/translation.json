{"Common": {"CreateNew": "<PERSON><PERSON><PERSON> maken", "ClearFilters": "Filters wissen", "Search": "<PERSON><PERSON>", "RequiredField": "Dit veld is verplicht!", "Select": "Selecteren", "Submit": "<PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Save": "Opsla<PERSON>", "Saving": "<PERSON><PERSON> met op<PERSON><PERSON>...", "SaveAndContinue": "Opslaan en doorgaan", "Delete": "Verwijderen", "Edit": "Bewerken", "Next": "Volgende", "Prev": "Vorige", "Create": "Aanmaken", "Add": "Toevoegen", "AddNew": "<PERSON><PERSON><PERSON>", "Import": "Importeren", "All": "Alle", "Submitting": "Verzenden...", "Deleting": "Verwijderen...", "Adding": "Toevoegen...", "Updating": "Bijwerken...", "Reset": "Opnieuw instellen", "True": "<PERSON><PERSON>", "False": "<PERSON><PERSON><PERSON>", "Low": "Laag", "Medium": "Middelmatig", "High": "<PERSON><PERSON>", "Apply": "Toepassen", "Write": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Action": "<PERSON><PERSON>", "Name": "<PERSON><PERSON>", "NoResult": " <PERSON><PERSON>.", "Update": "Bijwerken", "No": "<PERSON><PERSON>", "YesProceed": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Copied": "Gekopieerd", "BackToList": "Terug naar lijst"}, "ErrorPage": {"Page": "<PERSON><PERSON><PERSON>", "NotFound": "<PERSON><PERSON>", "NotExist": "<PERSON><PERSON><PERSON> bestaat de pagina die u zoekt niet.", "GoBack": "Ga terug", "ReportError": "Fout melden"}, "Breadcrumbs": {"ControlRoom": "Controlekamer", "Policy Management": "Beleidbeheer", "All Policies": "Alle Beleidsregels", "Privacy Notice": "Privacyverklaring", "Audit Log": "Auditlogboek", "Data Subject Rights Management": "<PERSON><PERSON><PERSON>ubjectrecht<PERSON>", "Pending Requests": "In Afwachting van Verzoeken", "View Request": "Verzoek Bekijken", "Approved Requests": "Goedgekeurde Verzoeken", "Reject In Progress Requests": "Afgewezen Verzoeken in Behandeling", "Rejected Requests": "Afgewezen Verzoeken", "Completed Requests": "Voltooide Verzoeken", "Profile": "<PERSON><PERSON>", "Change Password": "Wachtwoord Wijzigen", "Blogs": "Blogs", "View Blog": "Blog Bekijken", "About GoTrust": "Over GoTrust", "Company Structure": "Bedrijfsstructuur", "Group Detail": "Groepsdetails", "Customer Management": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Role Management": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Role Details": "Rol Details", "Add Role": "Rol <PERSON>n", "Edit Role": "Rol Bewerken", "Edit Role Details": "Rol Details Bewerken", "User Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Home": "Startpagina", "Insights into Processing Activities": "Inzichten in verwerkingsactiviteiten", "Task Overview": "<PERSON><PERSON><PERSON> van taken", "ROPA": "ROPA", "ROPA Review": "ROPA-beoordeling", "Basic Information": "Basisinformatie", "Add Control": "<PERSON>e toe<PERSON>n", "PII Inventory": "PII-inventaris", "Unstructured Data Inventory": "Ongeordende gegevensinventaris", "Data Catalogue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Structured": "Gestructureerd", "Unstructured": "Ongestructureerd", "Services": "<PERSON><PERSON><PERSON>", "Ingestion": "Opname", "Data Catalogue Dashboard": "Dashboard gegevenscatalogus", "PII List": "PII-lijst", "File Classification": "Bestandsclassificatie", "DSR Form Repository": "DSR-formulierrepository", "DSR Form Builder": "DSR-formulierbouwer", "Create Form": "<PERSON><PERSON><PERSON> maken", "Form Review": "Formulierbeoordeling", "Add Question": "Vraag toe<PERSON>n", "Schema Entity": "Schema-entiteit", "Table Entity": "Tabelentiteit", "Column Entity": "Kolomentiteit", "Profile Entity": "Profielentiteit", "Template": "Sjabloon", "Assessment Management": "Beoordelingsbeheer", "Dashboard": "Dashboard", "Templates": "Sjablonen", "Vendor Risk Management": "Leveranciersris<PERSON>beheer", "Vendor List": "Leverancierslijst", "Details": "Details", "Internal Assessment": "Interne <PERSON>", "Vendor Assessment": "Leveranciersbeoordeling", "Mitigation": "Be<PERSON>king", "Cookie Consent Management": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Cookie Consent Domain": "<PERSON><PERSON> voor Cook<PERSON>-toes<PERSON><PERSON>", "Cookie Configuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Universal Consent Management": "<PERSON><PERSON>", "Preference Center": "Voorkeurencentrum", "Add Preference Center": "Voorkeurencentrum Toevoegen", "Update Preference Center": "Voorkeurencentrum Bijwerken", "Consent Collection": "Toestemmingsverzameling", "Consent Upload": "Toestemming Uploaden", "Custom Parameters": "Aangepaste Parameters", "Subject Consent Types": "Soorten Toestemming van Betrokkenen", "Subject Consent Detail": "Details van Toestemming van Betrok<PERSON>en", "Form": "<PERSON><PERSON><PERSON>", "Consent Collection Templates": "Sjablonen voor Toestemmingsverzameling", "Create Consent Collection Template": "Sjabloon voor Toestemmingsverzameling Maken", "Processing Category": "Verwerkingscategorie", "Processing Purpose": "Verwerkingsdoel", "Consent Purpose": "Toestemmingsdoel", "PII Label": "PII-label", "Privacy Notice Details": "Details van de Privacyverklaring", "View Privacy Notice": "Privacyverklaring Bekijken", "Subject Consent List": "<PERSON><PERSON><PERSON> van Toestemmingen van Betrokkenen", "Customization": "<PERSON><PERSON>", "Requests": "Verzoeken", "View Requests": "Bekijk Verzoeken", "Tasks": "Taken", "View Tasks": "Bekijk Taken", "Reject in Progress Requests": "A<PERSON><PERSON><PERSON><PERSON> van Lopende Verzoeken", "Create Request": "Verzoek Aanmaken", "Email Templates": "E-mailsjablonen", "Create Email Template": "E-mailsjabloon Aanmaken", "Retention Schedule": "Bewaartermijnschema", "Workflow": "Werkstroom", "Add Workflow": "Werkstroom Toevoegen", "Edit Workflow": "Werkstroom Bewerken", "My Request": "<PERSON><PERSON>", "My Request View": "<PERSON><PERSON>", "View Workflow": "Werkstroom Bekijken", "Support": "Ondersteuning", "Create Ticket": "Ticket Aanmaken", "View Ticket": "<PERSON><PERSON><PERSON>", "Edit Ticket": "Ticket Bewerken", "Finance": "Financiën", "Universal Control Framework": "<PERSON><PERSON>", "Improvements": "Verbeteringen", "Risk Dashboard": "Risicodashboard", "Compliance Dashboard": "Nalevingsdashboard", "Privacy Ops": "Privacybewerkingen", "Document Repository": "Documentenarchief", "Assessment Repository": "Beoordelingsarchief", "Processing Activities": "Verwerkingsactiviteiten", "Regulations": "Regelgeving", "Risk Register": "Risicoregister", "Duty": "<PERSON><PERSON><PERSON>", "Action": "<PERSON><PERSON>", "Improvement Actions": "Verbeteringsacties", "Breaches": "Inbreuken", "Breach Details": "Details van Inbreuk", "Subject Consent Manager": "Toestemmingsbeheerder voor Onderwerpen"}, "SideBar": {"GroupLabel": {"Account Setup": "Accountinstellingen", "Theme": "<PERSON>a", "Data Mapping": "<PERSON><PERSON><PERSON>sma<PERSON>", "Policy Management": "<PERSON><PERSON><PERSON>", "Data Subject Rights Management": "<PERSON><PERSON><PERSON> van recht<PERSON> van bet<PERSON>", "Assessment Management": "<PERSON><PERSON><PERSON>", "Universal Consent Management": "<PERSON><PERSON><PERSON>", "Universal Control Framework": "Universeel controleframework", "Cookie Consent Management": "<PERSON><PERSON><PERSON> van <PERSON><PERSON><PERSON><PERSON>", "Vendor Risk Management": "<PERSON><PERSON><PERSON> leveranciersrisico's", "Awareness Program": "Bewustwordingsprogramma", "Blogs": "Blogs", "Support": "Ondersteuning", "Finance/Commercials": "Financiën/Commercieel", "Workflow Automation": "Workflow-automatisering", "Data Discovery": "Gegevensontdekking", "DPO Runbook": "DPO-handboek", "Data Breach Management": "<PERSON><PERSON><PERSON>", "Configurations": "Configuraties", "Policy & Notice Management": "<PERSON><PERSON><PERSON> en Kennisgevingen"}, "SideBarData": {"Profile Configuration": "Profielconfiguratie", "On-boarding Questionnaire": "Inschrijvingsvragenlijst", "Company Structure": "Bedrijfsstructuur", "Access Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Role Management": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "User Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Vendor Management": "Leveranciers<PERSON><PERSON>r", "About GoTrust": "Over GoTrust", "Customization": "<PERSON><PERSON>", "Dashboard": "Dashboard", "Task Overview": "Taakoverzicht", "Content Profiles": "Inhoudsprofielen", "Data Catalogue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Structure": "Gestructureerd", "Unstructure": "Ongestructureerd", "Data Catalogue V0": "Gegevenscatalogus V0", "File Classification": "Bestandsclassificatie", "DSR Lab": "DSR-lab", "DSR Form Builder": "DSR-formulierbouwer", "DSR Report": "DSR-rapport", "DSR Form Repository": "DSR-formulierarchief", "DSR Email Templates": "DSR-e-mailsjablonen", "DSR Retention Schedule": "DSR-retentieschema", "Workflow": "Workflow", "Impact Assessment": "Impactanalyse", "Privacy Impact Assessment": "Privacy-impactanalyse", "Privacy by Design Assessment": "Privacy by Design-beoordeling", "Legitimate Interests Assessment": "Be<PERSON><PERSON><PERSON> van legitieme belangen", "Transfer Impact Assessment": "<PERSON><PERSON><PERSON><PERSON> van de overdrachtsimpact", "EU AI Assessment": "EU AI-beoordeling", "Assessment Lab": "Beoordelingslab", "Assessment Templates": "Beoordelingssjablonen", "Subject Consent Types": "<PERSON><PERSON> van bet<PERSON>", "Subject Consent List": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "Privacy Notice": "Privacyverklaring", "Source": "<PERSON><PERSON>", "Consent Upload": "<PERSON><PERSON><PERSON>", "UCM Lab": "UCM-lab", "Processing Category": "Verwerkingscategorie", "Processing Purpose": "Verwerkingsdoel", "Consent Purpose": "<PERSON><PERSON>", "Consent POC": "Contactpunt voor toestemming", "PII Label": "PII-label", "Consent Collection Builder": "<PERSON><PERSON><PERSON><PERSON><PERSON> toestemmingenbouwer", "Preference Form": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cookie Consent Domain": "<PERSON>ie-<PERSON>temming domein", "VRM Lab": "VRM-lab", "Vendor Assessment Templates": "Leveranciersbeoordelingssjablonen", "Course Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Registration": "Registratie", "Enrollment": "Inschrijving", "Project View": "Projectweergave", "Blogs": "Blogs", "Billing & Invoice": "Facturatie & facturen", "Workflow Automation": "Workflow-automatisering", "Data Discovery": "Gegevensontdekking", "Risk Dashboard": "Risicodashboard", "Compliance Dashboard": "Nalevingsdashboard", "Regulations": "Regelgeving", "Risk Register": "Risicoregister", "Activities": "Activiteiten", "Duties": "Taken", "Actions": "Acties", "Improvements": "Verbeteringen", "Repository": "<PERSON><PERSON>", "Document Repository": "Documentenarchief", "Assessment Repository": "Beoordelingsarchief", "Record of Processing Activities Repository": "<PERSON><PERSON>kingsactiviteiten", "User Guide": "Gebruikershandleiding", "Breach List": "<PERSON><PERSON><PERSON> van datalekken", "Subject Consent Manager": "Toestemmingsbeheerder voor Onderwerpen", "Theme Customization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Data Visualization": "Gegevensvisualisatie", "Data Insights": "Inzichten in Gegevens", "Data Flow Diagram": "Gegevensstroomdiagram", "Data Vizualization": "Gegevensvisualisatie"}}, "Home": {"Account Setup": "Accountinstellingen", "Theme": "<PERSON>a", "Data Mapping": "<PERSON><PERSON><PERSON>sma<PERSON>", "Policy & Notice Management": "Beleids- en kennisgevingsbeheer", "Data Subject Rights Management": "<PERSON><PERSON><PERSON> van <PERSON>ubjectrechten", "Assessment Management": "<PERSON><PERSON><PERSON>", "Universal Consent Management": "<PERSON><PERSON>", "Universal Control Framewok": "Universeel controlekader", "Cookie Consent Management": "<PERSON><PERSON><PERSON> van <PERSON><PERSON><PERSON><PERSON>", "Vendor Risk Management": "Leveranciersris<PERSON>beheer", "Awareness Program": "Bewustwordingsprogramma", "Blogs": "Blogs", "Support": "Ondersteuning", "Finance/Commercials": "Financiën/Commercieel", "Workflow Automation": "Workflowautomatisering", "Data Discovery": "Gegevensontdekking", "DPO Runbook": "DPO-handleiding", "Data Breach Management": "<PERSON><PERSON><PERSON>", "Data Retention": "Gegevensretentie", "Universal Control Framework": "Universeel Controle Raamwerk"}, "CompanyStructure": {"GroupDetails": {"Name": "<PERSON><PERSON>", "Parent": "Bovenliggend", "NoOfUsers": "Aantal gebruikers", "CreatedOn": "Aangemaakt op", "UpdatedOn": "Bijgewerkt op", "FirstName": "<PERSON><PERSON><PERSON><PERSON>", "LastName": "Achternaam"}, "CompanyTree": {"BusinessUnit": "Zakelijke eenheid", "DepartmentUnit": "Afdeling", "ProcessUnit": "<PERSON><PERSON> e<PERSON>heid"}, "CompanyView": {"Name": "<PERSON><PERSON>", "Added On": "Toegevoegd op", "Last Updated": "Laatst Bijgewerkt", "No. Of Users": "Aantal Gebruikers", "Actions": "Acties"}}, "RoleManagement": {"AddRole": {"AddRole": "Rol <PERSON>n", "Heading": "Rol details", "RoleName": "Rolnaam", "EnterRoleName": "<PERSON><PERSON><PERSON> r<PERSON> in", "GivenAccess": "<PERSON><PERSON><PERSON> verleend aan geb<PERSON>r"}, "RoleTable": {"RoleName": "Rolnaam", "CreatedBy": "Gemaakt door", "Totaluser": "Totaal aantal gebruikers", "CreatedOn": "Gemaakt op", "UpdatedOn": "Bijgewerkt op", "Action": "<PERSON><PERSON>"}, "ActiveStatus": {"Active": "Actief", "Inactive": "Inactief", "Archived": "Gearchiveerd"}, "ViewRole": {"Heading": "Rol details", "RoleName": "Rolnaam", "EnterRoleName": "<PERSON><PERSON><PERSON> r<PERSON> in", "CreatedBy": "Gemaakt door", "Status": "Status", "CreatedDate": "A<PERSON>maak<PERSON><PERSON>", "UpdatedDate": "Bijgewerkt op", "GivenAccess": "<PERSON><PERSON><PERSON> verleend aan geb<PERSON>r", "EditRole": "Rol bewerken"}, "EditRole": {"Heading": "Rol details", "RoleName": "Rolnaam", "Status": "Status", "GivenAccess": "<PERSON><PERSON><PERSON> verleend aan geb<PERSON>r"}}, "UserManagement": {"AddUser": {"Heading": "Gebruikersdetails", "FirstName": "<PERSON><PERSON><PERSON><PERSON>", "EnterFirstName": "<PERSON><PERSON><PERSON> v<PERSON> in", "LastName": "Achternaam", "EnterLastName": "<PERSON><PERSON><PERSON> achternaa<PERSON> in", "Email": "E-mail", "EnterEmail": "<PERSON><PERSON>r e-mail in", "Phone": "Telefoon", "EnterPhoneNumber": "Voer telefoonnummer in", "SelectRole": "Selecteer rol", "GroupAssigned": "Toegewezen groep", "AccessesUser": "<PERSON><PERSON><PERSON> verleend aan geb<PERSON>r", "SeeAccesses": "Selecteer rol om toegang te zien"}, "EditUser": {"Heading": "Gebruikersgegevens", "FirstName": "<PERSON><PERSON><PERSON><PERSON>", "EnterFirstName": "<PERSON><PERSON><PERSON> v<PERSON> in", "LastName": "Achternaam", "EnterLastName": "<PERSON><PERSON><PERSON> achternaa<PERSON> in", "Email": "E-mail", "EnterEmail": "<PERSON><PERSON>r e-mail in", "Phone": "Telefoon", "EnterPhoneNumber": "Voer telefoonnummer in", "SelectRole": "Selecteer rol", "Status": "Status", "GroupAssigned": "Toegewezen groep", "AccessesUser": "Toegangen verleend aan geb<PERSON>iker", "SeeAccesses": "Selecteer rol om toegangen te zien"}, "ViewUser": {"Heading": "Gebruikersgegevens", "FirstName": "<PERSON><PERSON><PERSON><PERSON>", "EnterFirstName": "<PERSON><PERSON><PERSON> v<PERSON> in", "LastName": "Achternaam", "EnterLastName": "<PERSON><PERSON><PERSON> achternaa<PERSON> in", "Email": "E-mail", "EnterEmail": "<PERSON><PERSON>r e-mail in", "Phone": "Telefoon", "EnterPhoneNumber": "Voer telefoonnummer in", "SelectRole": "Selecteer rol", "Status": "Status", "CreatedDate": "A<PERSON>maak<PERSON><PERSON>", "UpdatedDate": "Bijgewerkte datum", "GroupAssigned": "Toegewezen groep", "AccessesUser": "Toegangen verleend aan geb<PERSON>iker", "SeeAccesses": "Selecteer rol om toegangen te zien", "EditUser": "Bewerk gebruiker"}, "UserTable": {"AddUser": "<PERSON>eb<PERSON><PERSON><PERSON>", "FirstName": "<PERSON><PERSON><PERSON><PERSON>", "LastName": "Achternaam", "Email": "E-mail", "Phone": "Telefoon", "AddedDate": "Toegevoegd op", "UpdatedDate": "Bijgewerkt op", "Group": "<PERSON><PERSON><PERSON>", "RoleName": "Rolnaam", "Action": "<PERSON><PERSON>"}}, "CustomerManagement": {"AddCustomer": {"Heading": "Klantgegevens", "Email": "E-mail", "EnterEmail": "<PERSON><PERSON>r e-mail in", "Address": "<PERSON><PERSON>", "EnterAddress": "<PERSON><PERSON><PERSON> ad<PERSON> in", "AdminDetails": "Beheerdersdetails", "AdminName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EnterAdminName": "<PERSON><PERSON><PERSON> be<PERSON> in", "Phone": "Telefoon", "EnterPhoneNumber": "Voer telefoonnummer in", "AccessesUser": "<PERSON><PERSON><PERSON> verleend aan geb<PERSON>r", "SeeAccesses": "Selecteer rol om toegang te zien"}}, "CookieConsentManagement": {"ServiceSavedSuccessfully": "Service succesvol opgeslagen", "DomainDetails": {"BusinessUnit_Tooltip": "De bedrijfseenheid waar dit domein onder valt. Helpt bij het toewijzen van cookie-toestemmingsbanners aan het juiste team of functie binnen uw organisatie.", "DomainGroup_Tooltip": "De naam van het domein waar de cookie-scanning zal worden uitgevoerd.", "DomainURL_Tooltip": "De volledige website-URL waar de cookie-scanning zal worden uitgevoerd. Voorbeeld: https://www.example.com.", "Owner_Tooltip": "De persoon of afdeling die verantwoordelijk is voor het beheer van cookie-toestemmingen voor dit domein. Wordt gewaarschuwd bij wijzigingen of problemen.", "OwnerEmail_Tooltip": "E-mailadres waar cookie-toestemmingsgerelateerde waarschuwingen en updates worden verzonden. Kan een team-e-mailadres of een individueel adres zijn.", "CookiePolicyLink_Tooltip": "Directe link naar de cookiebeleidspagina van het domein. Dit wordt getoond op de toestemmingsbanner om gebruikers te informeren over gegevenspraktijken.", "Entity_Tooltip": "De beschermingsregelgeving die van toepassing is op deze specifieke domein-URL.", "ConsentFramework_Tooltip": "De beschermingsregelgeving die van toepassing is op deze specifieke domein-URL.", "BusinessUnit": "Bed<PERSON>jfs<PERSON><PERSON><PERSON>", "CookiePolicyLink": "Cookiebeleidslink", "GroupDomainName": "<PERSON><PERSON><PERSON>", "Heading": "<PERSON><PERSON>", "DomainGroup": "Domeingroep", "DomainGroup_Placeholder": "Selecteer", "DomainName": "Domeinnaam", "DomainName_Placeholder": "<PERSON><PERSON><PERSON> in", "URL": "URL", "DomainURL": "<PERSON><PERSON>", "URL_Placeholder": "Voer URL in", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Owner_Placeholder": "<PERSON><PERSON><PERSON> eigenaar naam in", "OwnerEmail": "E-mail van eigenaar", "OwnerEmail_Placeholder": "<PERSON><PERSON>r e-mail van eigenaar in", "Entity": "Entiteit", "Entity_Placeholder": "Selecteer", "CreatedOn": "Aangemaakt op", "ConsentFramework": "Toestemmingskader", "CreatedOn_Placeholder": "<PERSON>es een datum", "CompliancePolicyLink": "Compliancebeleid link", "CompliancePolicyLink_Placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "VendorRiskManagement": {"CreateNewVendor": {"name": "Leverancier", "EnterName": "<PERSON><PERSON><PERSON> leveranciersnaam in", "SelectName": "Selecteer leveranciers<PERSON>am", "entity": "Entiteit", "SelectEntity": "Selecteer entite<PERSON><PERSON><PERSON>", "department": "Afdeling", "SelectDepartment": "Selecteer a<PERSON><PERSON><PERSON><PERSON><PERSON>", "assigned_to": "Toegewezen aan", "SelectAssignee": "Selecteer toegewezene naam", "reviewer": "<PERSON><PERSON><PERSON><PERSON>", "SelectReviewer": "Selecteer be<PERSON><PERSON><PERSON> naam", "template": "Sjabloon", "SelectTemplate": "Selecteer s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DefaultTemplate": "Vendor Risk Scoring Framework (standaard)"}, "Assessment": {"next": "Volgende", "previous": "Vorige", "controls": "Controles", "collaborator": "Samenwerker", "upload": "Bij<PERSON> uploaden"}, "Lab": {"upload": "Sjabloon uploaden", "DropHere": "Sleep hier om bij te voegen of", "Upload": "upload", "FileType": "CSV- of XLSX-bestand | Max grootte: 10MB", "name": "Sjabloonnaam", "PasteURL": "Plak URL"}, "ViewDetails": {"CollaboratorsProgressInVRM": "Categorie Voortgang in interne beoordelingen"}}, "AboutUs": {"OurMission": "<PERSON><PERSON> missie", "KeyFeature": "Belangrijke kenmerken", "WhyChoose": "Waarom kiezen voor GoTrust?"}, "Ropa": {"PiiHandbook": {"PIIHandBook": "PII Handleiding", "AddPii": "PII toevoegen", "PiiName": "PII Type", "Description": "Beschrijving", "Tags": "Tags", "TypeTag": "<PERSON><PERSON><PERSON> een tag in", "Status": "Status", "Cancel": "<PERSON><PERSON><PERSON>", "Save": "Opsla<PERSON>", "AddingPii": "PII toevoegen...", "PiiType": "PII Type", "PiiCategory": "PII Categoría", "PiiValue": "PII Waarde", "PiiDescription": "PII Beschrijving", "PiiTags": "PII Tags", "PiiTypeTag": "Inserisci un tag y pulsa Enter", "PiiStatus": "Status", "PiiCancel": "<PERSON><PERSON><PERSON>", "PiiSave": "Opsla<PERSON>", "PiiNamePlaceholder": "<PERSON><PERSON><PERSON> in", "PiiDescriptionPlaceholder": "<PERSON><PERSON><PERSON> in", "PiiTagsPlaceholder": "Inserisci un tag y pulsa Enter", "PiiStatusPlaceholder": "Selecteer Status", "PiiTypePlaceholder": "Selecteer PII Type", "PiiCategoryPlaceholder": "Selecteer PII Categoría", "PiiValuePlaceholder": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> van PII in", "PiiSaving": "Opslaan...", "PiiEditing": "Bewerken...", "PiiAdding": "Toevoegen...", "PiiDeleting": "Verwijderen...", "PiiDeletingConfirmation": "Weet je zeker dat je dit PII wilt verwijderen?", "PiiEditPii": "PII bewerken", "PiiPiiType": "PII Type", "PiiPiiTypePlaceholder": "Selecteer PII Type", "PiiPiiDescription": "PII Beschrijving", "PiiPiiDescriptionPlaceholder": "<PERSON><PERSON><PERSON> in", "PiiPiiTags": "PII Tags", "PiiPiiTagsPlaceholder": "Inserisci un tag e premi Enter", "PiiPiiStatus": "Status", "PiiPiiStatusPlaceholder": "Selecteer Status", "PiiPiiSource": "Fonte", "PiiPiiSourcePlaceholder": "Inserisci Fonte", "PiiPiiAddedSuccessfully": "PII toegevoegd met succes", "PiiPiiAddedFailed": "PII toevoegen mislukt", "PiiPiiUpdatedSuccessfully": "PII bijgewerkt met succes", "PiiPiiUpdatedFailed": "PII bijwerken mislukt", "PiiPiiDeletedSuccessfully": "PII verwijderd met succes", "PiiPiiDeletedFailed": "PII verwijderen mislukt", "PiiPiiAlreadyExists": "PII bestaat al", "PiiPiiDeletePii": "PII verwijderen", "PiiPiiDeletePiiDescription": "Weet je zeker dat je dit PII wilt verwijderen ", "withTag": "met tag", "PiiPiiDelete": "Verwijderen", "PiiPiiCancel": "<PERSON><PERSON><PERSON>", "PiiPiiDeleting": "Verwijderen...", "PiiPiiAddFailed": "PII toevoegen mislukt", "PiiPiiDeleteFailed": "PII verwijderen mislukt", "PiiPiiUpdateFailed": "PII bijwerken mislukt"}, "Dashboard": {"ByDepartment": {"Heading": "Verwerkingsactiviteiten per afdeling", "Count": "Totaal aantal"}, "ByOrganization": {"Heading": "Verwerkingsactiviteiten per organisatierol", "Count": "Totaal aantal"}, "ByLawfullBasis": {"Heading": "Verwerkingsactiviteiten op basis van wettelijke grondslag", "y-axis": "Toegepaste wettelijke grondslag", "x-axis": "Aantal verwerkingsactiviteiten"}, "ByPersonalData": {"Heading": "Afdelingsgewijze persoonlijke & gevoelige persoonsgegevens", "y-axis": "Afdelingen", "x-axis": "Aantal persoonlijke gegevens"}, "ThirdPartiesList": {"Heading": "<PERSON><PERSON><PERSON>", "TableHeading": {"Vendor": "Leverancier", "Services": "<PERSON><PERSON><PERSON>", "Department": "Afdeling", "Location": "Locatie", "PersonalData": "Betrokken persoonlijke gegevens"}}, "DataSystemList": {"Heading": "Lijst van datasystemen/toepassingen", "Count": "Totaal aantal", "TableHeading": {"Purpose": "<PERSON><PERSON>", "InHouse": "Intern/Derde partij", "ThirdPartyName": "<PERSON><PERSON> partij", "Location": "Locatie"}}, "DPIA": {"Heading": "DPIA-verplichting", "y-axis": "Aantal risicovolle verwerkingsactiviteiten", "x-axis": "Afdeling"}, "ROPAV3": {"ROPA Management": "ROPA Beheer", "ROPA Dashboard": "ROPA Dashboard", "ROPA Assessments": "ROPA-beoordelingen", "Overview of all processing activity assessments": "Overzicht van alle beoordelingen van verwerkingsactiviteiten", "Comprehensive Records of Processing Activities management and compliance tracking": "Uit<PERSON><PERSON><PERSON> beheer van <PERSON> van Verwerkingsactiviteiten en nalevingsmonitoring", "ROPA Management Dashboard": "ROPA Beheer Dashboard", "Records of Processing Activities (Article 30 GDPR Compliance)": "Register<PERSON> van Verwerkingsactiviteiten (Artikel 30 AVG Naleving)", "Select Entity": "Selecteer En<PERSON>it", "Active assessments": "Actieve beoordelingen", "Total ROPAs": "Totaal ROPAs", "Yet to Start": "Nog te beginnen", "In Progress": "In uitvoering", "Completed": "Voltooid", "Processing Activities by Organization Role": "Verwerkingsactiviteiten per Organisatierol", "Total count": "Totaal aantal", "Loading organization role data...": "Organisatierol gegevens laden...", "Failed to load organization role data": "<PERSON><PERSON> van organisatierol gegevens mislukt", "Controller": "Verwerkingsverantwoordelijke", "Joint-Controller": "Gezamenlijke Verwerkingsverantwoordelijke", "Processor": "Verwerker", "Sub-Processor": "Onderverwerker", "ROPAs": "ROPAs", "Processing Activities by Department": "Verwerkingsactiviteiten per Afdeling", "Total processes": "Totaal processen", "Departments": "Afdelingen", "Error loading department data": "Fout bij laden afdelingsgegevens", "processes": "processen", "ROPA Progress": "ROPA Voortgang", "Total": "Totaal", "Completed Department": "Voltooid", "In Progress Department": "In uitvoering", "No department data available": "<PERSON><PERSON> afdelingsgegevens be<PERSON>", "List of Third Parties by Department": "<PERSON><PERSON><PERSON> van derde partijen per afdeling", "Loading...": "Laden...", "Failed to load data.": "<PERSON><PERSON> van g<PERSON> mislukt.", "Vendor": "Leverancier", "Services": "<PERSON><PERSON><PERSON>", "Department": "Afdeling", "Location": "Locatie", "Personal Data Involved": "Betrokken Persoonsgegevens", "No data available": "<PERSON><PERSON> g<PERSON><PERSON>", "No records found.": "Geen records gevonden.", "Active Collaborators": "Actieve Medewerkers", "Team members working on ROPA assessments": "Teamleden die werken aan ROPA-beoordelingen", "Loading collaborators...": "Medewerkers laden...", "Failed to fetch collaborator data": "<PERSON><PERSON><PERSON> van medewerkergegevens mislukt", "Failed to load collaborator data": "Laden van medewerkergegevens mislukt", "No active collaborators found": "Geen actieve medewerkers gevonden", "ROPAs assigned": "ROPAs toegewezen", "completed": "voltooid", "Complete": "Voltooid", "Progress": "Voortgang", "more": "meer", "total ROPAs": "totaal ROPAs", "Recent ROPA Activities": "Recente ROPA Activiteiten", "Latest updates on processing activity assessments": "Laatste updates over verwerkingsactiviteit beoordelingen", "View All": "Bekijk Alles", "Loading recent activities...": "Recente activiteiten laden...", "No recent ROPA activities found": "Geen recente ROPA activiteiten gevonden", "Assignee": "Toegewezen aan", "Importing a new document will result in the loss of all previous ROPA performed. Are you sure you want to continue?": "Het importeren van een nieuw document zal resulteren in het verlies van alle eerder uitgevoerde ROPA's. Weet u zeker dat u wilt doorgaan?", "Manage Records of Processing Activities assessments": "<PERSON><PERSON><PERSON> be<PERSON> van <PERSON>s van Verwerkingsactiviteiten", "Department Level": "Afdelingsniveau", "Process Level": "Procesniveau"}}, "Activity": {"TableHeading": {"Department": "Afdeling", "Process": "<PERSON><PERSON>", "SPOC": "Contactpersoon", "StartDate": "Startdatum", "UpdatedDate": "Bijgewerkte Datum", "AssignedTo": "Toegewezen Aan", "is_assigned": "<PERSON><PERSON>", "Reviewer": "<PERSON><PERSON><PERSON><PERSON>", "progress": "Voortgang", "Review": "Beoordeling", "Risks": "Risico", "Status": "Status", "Action": "<PERSON><PERSON>", "Import": "Importeren", "ImportNewFile": "Nieuw Bestand Importeren", "DownloadSampleFile": "Voorbeeldbestand Downloaden", "Filter": "Filteren", "ClearFilter": "<PERSON><PERSON>", "Start": "Starten", "Re-Start": "Opnieuw Starten", "View": "Bekijken", "Assign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ROPA Details": "ROPA Details", "Type": "Type", "Assignment": "Opdracht"}}, "ViewDetails": {"Reviewer": "<PERSON><PERSON><PERSON><PERSON>", "AssignedTo": "Toegewezen aan", "Department": "Afdeling", "Entity": "Entiteit", "UpdatedDate": "Bijgewerkte datum", "SPOC": "SPOC", "Review": "Beoordeling", "Start": "Starten", "Assign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CollaboratorsProgressInRopa": "Categorie Voortgang in ROPA", "RopaDetails": "ROPA Details", "TentativeCompletionDate": "Geschatte Einddatum", "PickaDate": "Selecteer een da<PERSON><PERSON>", "Category": "Categorie", "Collaborators": "Samenwerkers", "ROPADetails": "ROPA Details"}, "DataCatalogue": {"DashBoard": {"PIIHandBook": "PII Handleiding", "TabList": {"Structured": "Gestructureerd", "Unstructured": "Ongestructureerd"}, "Structured": {"ServiceOwner": "Service-eigenaar", "DataManagement": "<PERSON><PERSON><PERSON><PERSON>behee<PERSON>", "DataDetails": "Gegevensdetails", "TotalPIIs": "Totaal PII's", "TotalPIICategories": "Totaal aantal PII-categorieën", "DistinctPIIsDetected": "Onderscheiden PII's gede<PERSON>teerd", "DataLocations": {"DataLocations": "G<PERSON><PERSON><PERSON><PERSON>ati<PERSON>", "DataLocation": "Gegevenslocatie", "DataElements": "Gegevenselementen", "Databases": "Databases", "Schemas": "<PERSON><PERSON><PERSON>'s", "Tables": "Tabellen", "DataSystems": "Gegevenssystemen"}, "SankeyGraph": {"DisplayAllData": "Alle gegevenselementen weergeven", "NoDataAvailable": "<PERSON><PERSON> g<PERSON><PERSON>"}, "DataSensitivityCard": {"Data Sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NoDataAvailable": "<PERSON><PERSON> g<PERSON><PERSON>"}, "PIICategories": "PII-categorieën", "PIIDistributions": {"PII": "PII", "PIICount": "Aantal PII's", "PIIDistribution": "PII-verdeling", "Columns": "<PERSON><PERSON><PERSON><PERSON>", "Schemas": "<PERSON><PERSON><PERSON>'s", "Database": "Database", "DataSystems": "Gegevenssystemen", "Location": "Locatie"}}, "Unstructured": {"DataService": "Dataservice", "DataLocations": "G<PERSON><PERSON><PERSON><PERSON>ati<PERSON>", "DataManagement": "<PERSON><PERSON><PERSON><PERSON>behee<PERSON>", "ScannedDocuments": "Gescande Documenten", "ScannedVolume": "Gescand Volume", "ScannedFileFormat": "Gescand Bestand Formaat", "DataDetails": "Gegevensdetails", "TotalPIICategories": "Totaal aantal PII-categorieën", "DistinctPIIsDetected": "Onderscheiden PII's gede<PERSON>teerd", "TotalDetectedPIIs": "Totaal gedetecteerde PII's", "SankeyGraph": {"DisplayAllElements": "Alle gegevenselementen weergeven", "NoDataAvailable": "<PERSON><PERSON> g<PERSON><PERSON>"}, "DocumentCards": {"Documents": "Documenten", "DocumentName": "Documentnaam", "DocumentType": "Documenttype", "PIIsDetected": "PII's gedetecteerd", "DocumentSize": "Documentgrootte", "Location": "Locatie", "DataService": "Dataservice", "SubService": "Subdienst"}, "DataLocationCard": {"DataLocation": "Gegevenslocatie", "DataServiceLoc": "Locatie van dataservice", "Document": "Document", "DocumentType": "Documenttype", "DataService": "Dataservice"}, "DataSensitivityCard": {"DataSensitivity": "Gegevensgevoeligheid", "NoDataAvailable": "<PERSON><PERSON> g<PERSON><PERSON>"}, "FileFormats": "Bestandsformaten", "PIICard": {"PII": "PII", "PIICount": "Aantal PII's", "Documents": "Documenten", "Location": "Locatie", "DataService": "Dataservice"}, "Services": "<PERSON><PERSON><PERSON>", "ID": "ID", "ServiceName": "Servicenaam", "ServiceType": "Servicetype", "Status": "Status", "Running": "Bezig", "Done": "Voltooid", "Failed": "Mislukt", "NA": "N.V.T.", "Action": "<PERSON><PERSON>", "AddNewService": "Nieuwe service toevoegen", "SelectIngestion": "Selecteer een ingestie", "Microsoft365": "Microsoft 365", "Aws": "AWS", "GoogleWorkspace": "Google Workspace", "AzureDataLake": "Azure Data Lake"}, "PIIList": {"AddNew": "<PERSON><PERSON><PERSON>", "PIICategory": "PII-categorie", "PIIValue": "PII-waarde", "PIIDescription": "PII-beschrijving", "Applicability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Actions": "Acties", "EnterDescription": "<PERSON><PERSON><PERSON> hier je beschrijving in"}}, "TotalColumns": "Totaal Kolo<PERSON>n", "TotalTables": "Totaal Tabellen", "TotalDatabases": "Totaal Databases", "DistinctPIIsCount": "Aantal Onderscheidende PII's", "ColumnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TableName": "Tabelnaam", "ServiceName": "Servicenaam", "DataCategory": "Datacategorie", "PIIDetected": "PII Gedetecteerd", "Sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataSystem": "Gegevenssysteem", "DataSystemOwner": "<PERSON><PERSON><PERSON><PERSON> van het Gegevenssysteem", "DataServiceCount": "Aantal Gegevensservices", "ScannedDocumentCount": "Aantal Gescande Documenten", "TotalVolume": "Totaal Volume", "FileFormatCount": "Aantal Bestandsformaten", "DocumentName": "Documentnaam", "DocumentType": "Documenttype", "PIIsDetected": "PII's <PERSON><PERSON><PERSON><PERSON><PERSON>", "Size": "Grootte", "Location": "Locatie", "DataService": "Gegevensservice", "SubService": "Subdienst", "FileLocation": "Bestandslocatie", "DropHere": "Sleep hier om bij te voegen of", "Upload": "Uploaden", "SelectedFiles": "Geselecteerde Bestanden", "MaxSize": "Maximale Grootte: 10MB", "AcceptedFormats": "Geaccepteerde Formaten", "Datatype": "Gegevenstype", "Tags": "Tags", "PIIType": "PII-type", "Version": "<PERSON><PERSON><PERSON>", "TagDetails": "Tagdetails", "DataSystemCount": "Aantal Gegevenssystemen", "DetectedData": "Gedetecteerde Gegevens", "ConfidenceScore": "Vertrouwensscore"}, "ChartData": {"Controller": "Verwerkingsverantwoordelijke", "Joint-Controller": "Gezamenlijke verwerkingsverantwoordelijke", "Processor": "Verwerker", "Sub-Processor": "Subverwerker", "Finance": "Financiën", "AI": "AI", "Cloud": "Cloud", "Frontend": "<PERSON>oorkan<PERSON>", "ML": "Machine learning", "Consultancy": "Advies", "Backend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HR": "HR", "IT": "IT", "DevOps": "DevOps", "Sales": "Verkoop", "Consent": "Toestemming", "PII": "Persoonlijk identificeerbare informatie", "SPI": "Gevoelige persoonlijke informatie", "Performance of a contract": "Nako<PERSON> van een contract", "A legitimate interest": "<PERSON>en gerecht<PERSON>igd belang", "A vital interest": "<PERSON>en vitaal belang", "A legal requirement": "<PERSON><PERSON> wettelijke verp<PERSON>"}}, "Policy": {"Dashboard": {"Total": "Totaal aantal beleidsregels", "InUse": "Totaal aantal beleidsregels in gebruik", "Expiring": "Totaal aantal beleidsregels die binnen 45 dagen verlopen", "Policies": "Beleidsregels", "NoOfPolicies": "Aantal beleidsregels", "WorkflowHeading": "Beleidsregels per workflowfase", "Entities": "Entiteiten", "EntitiesHeading": "Beleidsregels per entiteit", "DepartmentsHeading": "Beleidsregels per Afdelingen"}, "AllPolicies": {"PolicyName": "Beleidsnaam", "PolicyCategory": "Beleidscategorie", "Entity": "Entiteit", "Recurrence": "Herhaling", "RenewalDate": "Vernieuwingsdatum", "WorkflowStage": "Workflowfase", "Department": "Afdeling", "Action": "<PERSON><PERSON>", "CreationofPolicy": "Aanma<PERSON> van <PERSON>", "ReviewofPolicy": "<PERSON><PERSON><PERSON><PERSON> van <PERSON>", "ApprovalofPolicy": "<PERSON><PERSON><PERSON><PERSON> van <PERSON>", "PolicyinUse": "Beleid in gebruik"}, "NewPolicyModal": {"Heading": "<PERSON><PERSON><PERSON> be<PERSON> a<PERSON>", "PolicyName": "<PERSON><PERSON><PERSON> in", "PolicyDescription": "Beleidsbeschrijving", "PolicyCategory": "Beleidscategorie", "EntityName": "<PERSON><PERSON>", "Language": "Taal", "PolicyAuthor": "Beleidsauteur", "PolicyReviewer": "Beleidsbeoordelaar", "PolicyApprover": "Beleidsgoedkeurder", "EffectiveDate": "Ingangsdatum", "RecurrencePeriod": "Herhalingsperiode", "Department": "Afdeling", "VersionNumber": "Versienummer", "PolicyID": "Beleid ID", "RelevantStandard/Law": "Relevante norm/wet", "Create": "AANMAKEN"}, "PolicyRequirement": {"Heading": "Beleidsvereiste", "Reviewer": "<PERSON><PERSON><PERSON><PERSON>", "Approver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Department": "Afdeling", "Entity": "Entiteit", "ReviewDate": "Beoordelingsdatum", "Recurrence": "Herhaling", "Collaborator": "Medewerker"}, "PolicyAttachment": {"Heading": "B<PERSON>jlage", "CreateWithAI": "<PERSON><PERSON><PERSON> met AI", "UpdateWithAI": "Beleid bijwerken met AI", "UploadedAttachment": "Geüploade bijlage"}, "AddAttachment": {"Heading": "Nieuwe versie aanmaken", "DropHere": "Sleep hier om bij te voegen of", "Upload": "upload", "FileType": "PDF- of Word-bestand | Max grootte: 5MB", "URL": "URL", "PasteURL": "Plak URL"}}, "AssessmentManagement": {"Dashboard": {"TotalAssessments": "Totaal aantal beoordelingen", "ByReadiness": "Verdeling op basis van g<PERSON>", "ByRegulation": "Per regelgevingstype", "ByOwners": "Beoordeling door eigenaren", "RecentAssessments": "<PERSON><PERSON> be<PERSON>", "ViewAll": "<PERSON><PERSON> be<PERSON>en", "AssessmentName": "<PERSON><PERSON>", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Entity": "Entiteit", "Department": "Afdeling", "ProcessUnit": "Procesunit", "StartDate": "Startdatum", "UpdatedDate": "Bijgewerkte datum", "Reviewer": "<PERSON><PERSON><PERSON><PERSON>", "AssignedTo": "Toegewezen aan", "Risks": "Risico's", "Progress": "Voortgang", "Status": "Status"}, "ViewDetails": {"CollaboratorsProgressInRopa": " Categorie Voortgang in beoordelingsgesprekken"}}, "DSR": {"Dashboard": {"Total": "Totaal aantal verzoeken", "Request": "V<PERSON>zoek", "RequestStats": "Verzoekstatistieken", "Approved Requests": "Goedgekeurde verzoeken", "Pending Requests": "In behandeling zijnde verzoeken", "Rejected Requests": "Afgewezen verzoeken", "Completed Requests": "Voltooide verzoeken", "Reject in Progress": "Afwijzing in behandeling", "Extended": "Verlengde verzoeken", "RequestsByRights": "Verzoeken per rechten", "TotalCount": "Totaal aantal", "RequestStatistics": "Verzoekstatistieken", "MoreThan": "<PERSON><PERSON> dan", "newRequests": "nieuwe verzoeken", "RecentRequests": "<PERSON><PERSON> ve<PERSON>", "Monthly": "Ma<PERSON><PERSON><PERSON><PERSON>", "Annually": "Jaarlijks", "NumberOfApprovedRequest": "Aantal goedgekeurde verzoeken", "RequestsByStages": "Verzoeken per fase", "Verify": "Verifiëren", "Stages": "Fasen", "days": "dagen", "NoData": "<PERSON><PERSON>", "ApproachingDeadline": "Nadere deadline", "Acknowledgement": "Bevestiging", "Count": "Aantal", "RequestByResidency": "Verzoek per verblijf", "RequestStatusPerOwner": "Verzoekstatus per eigenaar", "Completed": "Voltooid", "Pending": "In behandeling", "Last7days": "Laatste 7 dagen", "Last14days": "Laatste 14 dagen", "Last30days": "Laatste 30 dagen", "Last60days": "Laatste 60 dagen", "All": "Alle", "RequestTypes": "Verzoektypen"}, "TaskOverView": {"CreateRequest": "Verzoek aanmaken", "CreateForm": "<PERSON><PERSON><PERSON> maken", "DueDays": "<PERSON>gen tot taak vervalt", "TaskType": "Taaktype", "Automation Workflow": "Automatiseringsworkflow", "UpdateTask": "Taak bijwerken", "EnterTaskTitle": "<PERSON><PERSON><PERSON> in", "UpdateNote": "Update notitie", "Approve": "<PERSON><PERSON><PERSON><PERSON>", "Reject": "Afwijzen", "DataSubjectRequestDetailsID": "Gegevenssubjectverzoekdetails ID", "RequestID": "Verzoek ID", "NoResult": "<PERSON><PERSON>", "Assign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "View": "Bekijken", "EmailVerified": "E-mail Gecontroleerd", "Region": "Regio", "RequestDate": "Verzoekdatum", "RequestedBy": "Aangevraagd Door", "RejectRequest": "Weet u zeker dat u dit verzoek wilt afwijzen?", "ConfirmApprove": "Weet u zeker dat u dit verzoek wilt goedkeuren?", "Attachments": "Bijlagen", "NoAttachmentsAvailable": "<PERSON>n bi<PERSON>lagen be<PERSON>", "PostalCode": "Postcode", "Location": "Locatie", "Email": "E-mail", "FirstName": "<PERSON><PERSON><PERSON><PERSON>", "LastName": "Achternaam", "Phone": "Telefoon", "UserDetails": "Gebruikersdetails", "RequestDescription": "Verzoekbeschrijving", "Status": "Status", "RelationshipWithUs": "<PERSON><PERSON><PERSON> met ons", "RequestType": "Verzoektype", "CreatedOn": "Aangemaakt op", "RequestInformation": "Verzoeksinformatie", "AddNote": "Notitie toevo<PERSON>n", "Approved": "Goedgekeurd", "COMPLETED": "VOLTOOID", "APPROVED": "GOEDGEKEURD", "Rejected": "Afgewezen", "RejectionInProgress": "Afwijzing in behandeling", "Extended": "Verlengd", "Confirm": "Bevestigen", "ConfirmExtend": "Weet u zeker dat u de deadline wilt verlengen?", "Approver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Deadline": "Deadline", "BusinessUnit": "Bed<PERSON>jfs<PERSON><PERSON><PERSON>", "WebForm": "Webformulier", "DSAR": "DSAR Formulier (V12)", "Form Preview": "Formuliervoorbeeld", "PreferredLanguage": "Voorkeurstaal", "DSRID": "DSR ID", "SubjectType": "Onderwerpstype", "FullName": "Volledige Naam", "StepsStatus": "<PERSON><PERSON><PERSON><PERSON>", "Assigned": "Toegewezen", "WantToApprove": "Weet u zeker dat u wilt goedkeuren?", "WantToReject": "Weet u zeker dat u wilt afwijzen?", "ReasonOfRejection": "Reden voor afwijzing", "Action": "<PERSON><PERSON>", "UploadCompletionDocuments": "Upload voltooiingsdocumenten", "ClickToUploadDocuments": "Klik om documenten te uploaden", "FormPreview": "Formuliervoorbeeld", "TasksTitle": "Taaktitel", "NotStarted": "<PERSON><PERSON> gestart", "InProgress": "Bezig", "RejectTask": "<PERSON><PERSON> a<PERSON>", "Steps": "Stappen", "Attachment": "B<PERSON>jlage", "DownloadAuditLog": "Download", "Automate": "Automatiseren"}, "EmailTemplate": {"ConfirmDelete": "Bevestig ver<PERSON>", "CreateTemplate": "Sjabloon maken", "ConfirmDeleteQuestion": "Weet je zeker dat je deze sjabloon wilt verwijderen?", "CancelBtn": "<PERSON><PERSON><PERSON>", "DeleteBtn": "Verwijderen", "EmailTemplate": "E-mailsjabloon", "Modified": "Gewijzigd", "CreatedAt": "Aangemaakt op", "Email": "E-mail", "Name": "<PERSON><PERSON>", "Action": "<PERSON><PERSON>", "Preview": "Voorbeeld", "TemplateName": "Onderwerp / Sjabloonnaam", "CreateEmailTemplate": "E-mailsjabloon maken", "EmailTemplatePreview": "Voorbeeld van e-mailsjabloon"}, "FormBuilder": {"FormBuilder": "Formulierbouwer", "FormName": "Formuliernaam", "Regulations": "Regelgeving", "URLNotAvailableForDraftForms": "URL niet beschikbaar voor conceptformulieren", "TranslateForm": "<PERSON><PERSON><PERSON> vertalen", "DoYouWantToTranslateThisForm": "Wil je dit formulier vertalen?", "NoPublishNow": "<PERSON><PERSON>, nu publiceren", "YesTranslateForm": "<PERSON><PERSON>, formulier vertalen", "SelectLanguage": "Taal selecteren", "ChooseLanguage": "<PERSON><PERSON> een taal", "FormURL": "Formulier URL", "Cancel": "<PERSON><PERSON><PERSON>", "Translating": "Vertalen...", "StartTranslation": "Vertaling starten", "VerificationMethod": "Verificatiemethode", "ConfirmDeleteForm": "Weet je zeker dat je dit wilt verwijderen", "Form": "form<PERSON>er", "ActionVersion": "Actie<PERSON><PERSON>", "URL": "URL", "Action": "<PERSON><PERSON>", "Entity": "Entiteit", "LastUpdated": "Laatst bijgewerkt", "Email": "E-mail", "Published": "Gepubliceerd", "UploadingLogo": "Logo uploaden...", "LogoUploadedSuccessfully": "Logo succesvol geüpload", "FailedToUploadLogo": "Logo uploaden mislukt", "SaveFormBeforeCreatingRules": "<PERSON>la het formulier eerst op om regels aan te maken", "FillAllRuleFields": "Vul alle regelvelden in", "SelectValidFieldsForRule": "Selecteer geldige velden voor de regel", "ViewCodeSnippets": "Bekijk codefragmenten", "ViewForm": "<PERSON><PERSON><PERSON> formulier", "ViewFormDescription": "Bekijk formulierdetails en integratie codefragmenten", "FormDetails": "Formulierdetails", "BusinessUnit": "Bed<PERSON>jfs<PERSON><PERSON><PERSON>", "Status": "Status", "Draft": "Concept", "FormID": "Formulier ID", "CodeSnippets": "Codefragmenten", "CodeSnippetsDescription": "Gebruik de volgende codefragmenten om dit DSR-formulier te integreren in uw mobiele applicatie", "MobileSDKCode": "Mobiele SDK-code", "WebLink": "Web URL-link", "FormPreview": "Formuliervoorbeeld", "CodeSnippet": "Codefragment", "RulesAppliedSuccessfully": "Regels succesvol toegepast", "OptionRemovedSuccessfully": "Optie succesvol verwijderd!", "FailedToRemoveOption": "Verwijderen van optie mislukt", "CustomerIDAndFormIDRequired": "Klant-ID en formulier-ID vereist", "FailedToSaveFormContent": "<PERSON><PERSON><PERSON> mis<PERSON>", "URLCopiedToClipboard": "URL gekopieerd naar klembord", "PublicURLGenerated": "Publieke URL gegenereerd", "FailedToGeneratePublicURL": "Genereren van publieke URL mislukt", "FormSubmissionFailed": "<PERSON><PERSON> van formulier mislukt"}, "WorkFlow": {"WorkFlow": "Werkstroom", "EnterFlowType": "Voer stroomtype in", "AddWorkflow": "Werkstroom toevoegen", "TaskTitle": "Taaktitel", "Department": "Afdeling", "AddTask": "<PERSON>ak <PERSON>", "StartDate": "Startdatum", "DueDate": "Vervaldatum", "AddAssignee": "Toegewezene toevoegen", "GuidanceText": "Begeleidingstekst", "AddFiles": "<PERSON><PERSON><PERSON>", "TaskChecklist": "<PERSON><PERSON><PERSON><PERSON>", "UploadedDocuments": "Geüploade documenten", "ConfirmDeleteFile": "Weet je zeker dat je dit bestand wilt verwijderen?", "DeleteFile": "Bestand verwijderen", "Yes": "<PERSON>a", "SelectEntity": "Selecteer entiteit", "SelectRegulations": "Selecteer regelgeving"}, "AssigneeModal": {"Assignee": "Toegewezene", "FlowType": "Stroomtype", "DSRForm": "Verzoekformulier voor betrokkene", "PublishForm": "<PERSON><PERSON>er publiceren"}}, "Cookies": {"Interaction Count": "Interactie telling", "DownloadReport": "Rapport downloaden", "View": "Bekijken", "Source": "<PERSON><PERSON>", "ScanFinished": "Scan voltooid", "IsThisTheDefaultBanner": "Is dit de standaardbanner?", "IfThisIsTheDefaultBanner": "Als dit de standaardbanner is, wordt deze weergegeven aan alle gebruikers die de domeinnaam bezoeken wanneer de regio niet is opgegeven.", "DefaultBanner": "Standaardbanner", "SelectCategory": "Categorie selecteren", "PolicyUpdatedSuccessfully": "Beleid bijgewerkt", "FailedToAddPolicy": "Fout bij het toe<PERSON><PERSON><PERSON> van <PERSON>", "FailedToUpdatePolicy": "Fout bij het bijwerken van beleid", "PolicyAddedSuccessfully": "Beleid toegevoegd", "AllCategories": "Alle categorieën", "Consent": "Toestemming", "Details": "Details", "About": "Over", "ThisWebsiteUsesCookies": "Deze website gebruikt cookies", "BannerDescription": "We gebruiken cookies om inhoud en advertenties aan te passen, om sociale media-functies te bieden en om onze verkeer te analyseren. We delen ook informatie over uw gebruik van onze site met onze sociale media, advertentie- en analysepartners die deze kunnen combineren met andere informatie die u ons heeft verstrekt of hebben verzameld uit uw gebruik van hun diensten.", "OnlyNecessary": "<PERSON><PERSON>", "AllowSelection": "<PERSON><PERSON><PERSON>", "AllowAll": "<PERSON><PERSON><PERSON>", "AboutSectionContent": "Cookies zijn kleine tekstbestanden die door websites kunnen worden gebruikt om de gebruikerservaring efficiënter te maken. De wet stelt dat we cookies op uw apparaat kunnen opslaan als ze strikt noodzakelijk zijn voor de werking van deze site. Voor alle andere soorten cookies hebben we uw toestemming nodig. Deze site gebruikt verschillende soorten cookies. Sommige cookies worden ingevoegd door derde partijen die in onze pagina's verschijnen. Je kunt op elk moment uw toestemming wijzigen of intrekken via de Cookie-verklaring op onze website. Ontdek meer over wie we zijn, hoe u ons kunt bereiken en hoe we uw persoonlijke gegevens verwerken in onze privacyverklaring. Geef uw toestemming ID en datum op wanneer u ons contact opneemt in verband met uw toestemming.", "DefaultBannerError": "<PERSON><PERSON> moet precies één standaardbanner zijn. Ajusteer de selectie.", "ChooseRegulation": "Regulatie selecteren", "CreateOrUseExisting": "<PERSON><PERSON><PERSON> maken of bestaande gebruiken?", "Existing": "<PERSON><PERSON><PERSON>", "New": "<PERSON><PERSON><PERSON>", "SelectOrCreatePolicy": "Selecteer een bestaand cookiebeleid of maak een nieuw cookiebeleid.", "SelectVersion": "Versie selecteren", "SelectCookiePolicy": "Cookiebeleid selecteren", "Version": "<PERSON><PERSON><PERSON>", "TranslatedLanguages": "<PERSON><PERSON><PERSON><PERSON> talen", "SavingTranslation": "Vertaling opslaan...", "TranslationSaved": "Vertaling opgeslagen", "SaveTranslation": "Vertaling opslaan", "UnableToTranslate": "Kan geen vertaling maken", "UnableToFetchData": "Kan geen gegevens ophalen", "No Interaction Count": "<PERSON><PERSON> interactie telling", "Last Interaction": "Laatste interactie", "Cookie Name": "Cookienaa<PERSON>", "Cookie Value": "<PERSON><PERSON> waarde", "Cookie Path": "Cookie pad", "Consent Count": "<PERSON><PERSON><PERSON> telling", "Declined Count": "<PERSON><PERSON><PERSON><PERSON><PERSON> telling", "SubjectIdentity": "Onderwerp identiteit", "GeoLocation": "Geolocatie", "CookieCategory": "Cookie categorie", "Consent Status": "Toestemmingsstatus", "Consent Date": "Toestemmingsdatum", "DomainURL": "<PERSON><PERSON>", "Domain/Subdomain": "Domein/Subdomein", "Domain Group": "Domeingroep", "Last Scanned": "Laatst gescand", "Scan Frequency": "Scanfrequentie", "Next Scan": "Volgende scan", "Total Cookies": "Totaal cookies", "Cookie Policy": "Cookiebeleid", "NoResults": "<PERSON><PERSON> resultaten.", "Cookie Consent Domain": "<PERSON><PERSON> domein", "Banner Published": "<PERSON> gep<PERSON>", "Consent Policy": "Toestemmingsbeleid", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "NoData": "<PERSON><PERSON>", "Cookie Configuration": "<PERSON><PERSON> configuratie", "All Domains": "<PERSON><PERSON> domeinen", "Basic Information": "Basisinformatie", "Website Scan": "Website scan", "Categorize Cookie": "<PERSON><PERSON> categoriseren", "Customize Banner": "<PERSON> a<PERSON>en", "Language Support": "Taalondersteuning", "Consent Code": "Toestemmingscode", "AutoScan": "Automatisch scannen", "ScanDescription": "Scan uw website om een gedetailleerd cookie-rapport te krijgen.", "ScanNow": "<PERSON><PERSON> scannen", "Scanning": "Scannen...", "ScanStarted": "Scan succesvol gestart", "ScanFailed": "<PERSON><PERSON> mis<PERSON>t", "SelectFrequency": "Selecteer frequentie", "Weekly": "Wekelijks", "Monthly": "Ma<PERSON><PERSON><PERSON><PERSON>", "Yearly": "Jaarlijks", "Daily": "Dagelijks", "Category": "Categorie", "Service": "Service", "Cookie": "<PERSON><PERSON>", "AddCategory": "Categorie toevoegen", "AddServices": "Services toevoegen", "AddCookies": "<PERSON><PERSON> toevoegen", "DeviceInfo": "Apparaatinformatie", "ErrorOccurred": "Er is een onverwachte fout opgetreden.", "AddCodeToHead": "Voeg de volgende code toe aan het begin van de <head> tag:", "CookiePolicyURL": "<PERSON><PERSON>d URL", "Copied": "Gekopieerd", "DeployedCode": "Geïmplementeerde Code", "Granted Count": "Toegestaan Aantal", "IntegrateCookiePolicy": "Om het cookiebeleid op uw website te integreren, kopieer deze URL en plak deze in de voettekstsectie van uw website.", "TestingCode": "Test Code", "Total Users": "Totaal Gebruikers", "UseTestCodeInStaging": "Gebruik deze testcode op uw staging website voordat u deze implementeert op uw productiewebsite.", "WhenShouldICopy": "<PERSON><PERSON> moet ik dit kopiëren?"}, "DataBreach": {"Dashboard": {"TotalBreaches": "Totaal aantal inbreuken", "OpenBreaches": "Open inbreuken", "ClosedBreaches": "Gesloten inbreuken"}}, "CookiePolicy": {"CookiePolicyName": "Cookiebeleid", "Version": "<PERSON><PERSON><PERSON>", "UpdatedOn": "Bijgewerkt op", "Action": "<PERSON><PERSON>", "CopiedToClipboard": "URL gekopieerd naar klembord!", "Search": "<PERSON><PERSON>", "Create": "Aanmaken", "Cookie Policy": "Cookiebeleid", "View Policy": "Beleid Bekijken", "Edit Policy": "Beleid Bewerken", "Create Policy": "Beleid Aanmaken", "SelectEntity": "Selecteer entiteit"}, "CookieCenter": {"Sections": "Secties", "NoSectionsAvailable": "Geen secties beschikbaar", "NewSectionTitle": "<PERSON><PERSON><PERSON> van Nieuwe Sectie", "AddSection": "<PERSON><PERSON><PERSON>", "SelectSectionFromList": "Selecteer een sectie uit de lijst om de inhoud te bewerken", "NoContentYet": "Nog geen inhoud. Klik op de bewerkknop om inhoud toe te voegen.", "SaveChanges": "Wijzigingen Opslaan", "UpdateChanges": "Wijzigingen Bijwerken", "Save": "Opsla<PERSON>", "ErrorSelectEntity": "Selecteer een entiteit om door te gaan.", "ErrorFillTitle": "Vul alstublieft de titel in.", "ErrorAddSection": "Voeg ten minste één sectie toe.", "UpdatingPolicy": "Beleid bijwerken...", "AddingPolicy": "Beleid toevoegen...", "DoYouWantNewVersion": "Wilt u een nieuwe versie van dit beleid aan<PERSON>ken?", "No": "<PERSON><PERSON>", "Yes": "<PERSON>a", "DiscardChanges": "Wijzigingen negeren"}, "CookiePolicyEditor": {"EnterCookiePolicyTitle": "<PERSON><PERSON><PERSON> de titel van het privacybeleid in", "SelectEntity": "Selecteer entiteit"}, "ConsentCode": {"TestingCode": "Testcode", "TestCodeDescription": "Deze testcode is bedoeld voor gebruik op je staging-website voordat je deze op je productie-website implementeert.", "TestCodeAddCode": "Voeg de volgende code toe aan het begin van de <head>-tag:", "DeployedCode": "Geïmplementeerde Code", "Copied": "Gekopieerd", "WhenShouldICopyThis": "<PERSON><PERSON> moet ik dit kopiëren?", "CookiePolicyURL": "Cookiebeleid URL", "CookiePolicyURLDescription": "Om het cookiebeleid op je website te integreren, kopieer deze URL en plak deze in de voettekst van je website.", "IntegrateCookiePolicy": "Om het cookiebeleid op je website te integreren, kopieer deze URL en plak deze in de voettekst van je website."}, "CommonErrorMessages": {"Sending": "<PERSON><PERSON> met verz<PERSON><PERSON>", "AddingWorkflowNewStep": "Nieuwe stap aan workflow toevoegen...", "UpdatingTaskAutomation": "Taakautomatisering bijwerken...", "RemovingAutomation": "Automatisering verwijderen...", "UpdatingTask": "Taak bijwerken...", "DownloadingAuditLog": "Auditlogboek downloaden...", "SendingConsentLink": "Toestemmingslink verzenden...", "Saving": "<PERSON><PERSON> met op<PERSON><PERSON>...", "VerifyingEmail": "E-mailadres verifiëren...", "AccessExpired": "Toegang is verlopen. Vernieuw uw abonnement.", "AccountLocked": "Account is vergrendeld. Neem contact op met de ondersteuning.", "AccountNotVerified": "Account is niet g<PERSON><PERSON><PERSON>. Controleer uw e-mail.", "AnErrorOccurred": "Er is een fout opgetreden", "AnUnexpectedErrorOccurred": "Er is een onverwachte fout opgetreden.", "AnswerSavedSuccessfully": "Antwoord succesvol opgeslagen", "AssessmentCreatedSuccessfully": "Beoordeling succesvol aangemaakt", "AssessmentReviewedSuccessfully": "Beoordeling succesvol beoordeeld!", "AssigningUserFailed": "Gebruiker toewijzen mislukt!", "AuthenticationRequired": "Authenticatie is vereist.", "AuthorizationFailed": "Autorisatie mislukt.", "BadRequest": "Slecht verzoek. Controleer uw invoer.", "BandwidthExceeded": "Bandbreedtelimiet overschreden.", "CPULimitExceeded": "CPU-limiet overschreden.", "CacheError": "Cache-operatiefout.", "CalculationError": "Berekeningsfout.", "CertificateError": "Certificaatvalidatiefout.", "CheckConstraintViolation": "Controlebeperking overtreding.", "ChooseAtLeastOneCollaborator": "kies ten minste één medewerker", "CircuitBreakerOpen": "Circuit breaker is open.", "CompressionError": "Datacompressiefout.", "ConcurrentModification": "Resource werd gewijzigd door een andere gebruiker.", "ConfirmDelete": "Weet u zeker dat u dit item wilt verwijderen?", "ConfirmSave": "Weet u zeker dat u wijzigingen wilt opslaan?", "ConfirmUpdate": "Weet u zeker dat u dit item wilt bijwerken?", "Conflict": "Er is een conflict opgetreden. De resource is mogelijk gewijzigd.", "ConnectionLost": "Verbinding verloren. Controleer uw internetverbinding.", "ConnectionRefused": "Verbinding geweigerd door server.", "ConversionError": "Dataconversiefout.", "CouldntAddVendor": "Kon leverancier niet <PERSON>n", "CouldntSaveAnswers": "<PERSON>n antwoorden niet op<PERSON>an", "CouldntSaveReviews": "Kon beoordelingen niet opslaan", "CouldntStartAssessment": "Kon beoordeling niet starten", "CouldntDownloadAssessment": "Kon beoordeling niet <PERSON>en", "CouldntUpdateVendor": "Kon leverancier niet bijwerken", "DNSError": "DNS-resolutiefout.", "DataDeletedSuccessfully": "Gegevens succesvol verwijderd", "DataSavedSuccessfully": "Gegevens succesvol opgeslagen", "DataTruncation": "Dataafkappingsfout.", "DataUpdatedSuccessfully": "Gegevens succesvol bijgewerkt", "DatabaseError": "Databasefout opgetreden.", "DateTimeError": "Datum/tijd formaatfout.", "DecryptionError": "Dataontsleutelingsfout.", "DeleteFailed": "Verwijderen mislukt", "DependencyError": "Kan operatie niet voltooien vanwege afhankelijkheden.", "DeserializationError": "Datadeserialisatiefout.", "DivisionByZero": "Delen door nul fout.", "DomainError": "Wiskundige domeinfout.", "DuplicateKey": "<PERSON><PERSON><PERSON> sle<PERSON>lfout.", "EmailAlreadyExists": "E-mailadres bestaat al.", "EmailNotFound": "E-mailadres niet gevonden.", "EncodingError": "Karaktercoderingsfout.", "EncryptionError": "Dataversleutelingsfout.", "EntityIdRequiredForDeletion": "Entiteit-ID vereist voor verwijdering", "Error": "Fout", "ErrorDot": "Fout.", "ErrorSubmittingForm": "Fout bij het indienen van formulier", "ExecutionTimeout": "Uitvoeringstimeout.", "ExternalServiceError": "Externe servicefout opgetreden.", "FailedToCreateAssessment": "Beoordeling aanmaken mislukt", "FailedToDeleteData": "Gegevens verwijderen mislukt", "FailedToFetch": "<PERSON><PERSON><PERSON> mis<PERSON>t", "FailedToFetchData": "<PERSON><PERSON><PERSON><PERSON> op<PERSON> mislukt.", "FailedToSaveData": "<PERSON><PERSON><PERSON><PERSON> op<PERSON> mislukt", "FailedToUpdateData": "Gegevens bijwerken mislukt", "FeatureNotAvailable": "Deze functie is niet be<PERSON> in uw huidige plan.", "FileSizeExceeded": "Bestandsgrootte overschrijdt de maximale limiet", "FileUploadFailed": "Bestand uploaden mislukt", "FileUploadSuccessful": "Bestand succesvol geüpload", "FirewallBlocked": "Verzoek geblokkeerd door firewall.", "ForeignKeyConstraint": "<PERSON><PERSON><PERSON><PERSON> sleutelbeperking overtreding.", "FormSubmittedSuccessfully": "Formulier succesvol ingediend", "FormatError": "Dataformaatfout.", "HashingError": "Datahashingfout.", "HostUnreachable": "Host on<PERSON><PERSON><PERSON><PERSON><PERSON>.", "IntegrityConstraintViolation": "Data-integriteitsbeperking overtreding.", "InvalidConfiguration": "Ongeldige configuratie gedetecteerd.", "InvalidCredentials": "Ongeldige gebruikersnaam of wachtwoord.", "InvalidFileFormat": "Ongeldig bestandsformaat", "InvalidInput": "Ongeldige invoer verstrekt", "InvalidOperation": "Ongeldige wiskundige operatie.", "InvalidState": "Ongeldige status voor deze operatie.", "InvalidToken": "Ongeldig of verlopen token.", "LoadBalancerError": "Load balancer fout.", "LoadingData": "Laden...", "LoadingTranslations": "Vertalingen laden...", "MaintenanceMode": "<PERSON><PERSON><PERSON><PERSON> is in onderhoud. <PERSON><PERSON><PERSON> het later opnieuw.", "MappingDeletedSuccessfully": "Mapping succesvol verwijderd", "MappingError": "Datamappingfout.", "MemoryLimitExceeded": "Geheugenlimiet overschreden.", "MessageExpired": "Bericht is verlopen.", "NetworkTimeout": "Netwerk timeout fout.", "NotFound": "De gevraagde resource werd niet gevonden.", "NumericOverflow": "Numerieke overflow fout.", "OperationFailed": "<PERSON><PERSON> mis<PERSON>t", "OperationNotAllowed": "Deze operatie is niet toe<PERSON>.", "OperationSuccessful": "Operatie succesvol voltooid", "OverflowError": "Overflow fout.", "ParsingError": "Dataparsing fout.", "PartialFailure": "Operatie voltooid met g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "PasswordMismatch": "Wachtwoorden komen niet overeen.", "PermissionDenied": "U heeft geen toestemming om deze actie uit te voeren", "PleaseAgreeToAllRequiredConsents": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> met alle vereiste toestemmingen voordat u indient.", "PleaseEnterCorrectURL": "<PERSON><PERSON><PERSON> de juiste U<PERSON> in", "PleaseFilTheInput": "Vul de invoer in", "PleaseReviewAllQuestions": "Bekijk alle vragen!", "PortClosed": "Poort is gesloten of geblokkeerd.", "PrecisionLoss": "Precisieverliesfout.", "ProcessCompleted": "Proces succesvol voltooid!", "Processing": "Verwerken", "ProcessingEllipsis": "Verwerken...", "ProcessingIn": "Verwerken in...", "ProgressBarError": "Voortgangsbalk fout!", "ProtocolError": "Protocolfout.", "ProxyError": "Proxy server fout.", "QuestionDeletedSuccessfully": "Vraag is succesvol verwijderd", "QueueFull": "Berichtenwachtrij is vol.", "QuotaExceeded": "Quotum overschreden. Upgrade uw plan.", "RangeError": "Wiskundige bereikfout.", "RateLimited": "Verzoekfrequentie beperkt.", "RequiredFieldsMissing": "Vul alle vereiste velden in", "ResourceExhausted": "Systeembronnen uitgeput.", "ResourceLocked": "Resource is <PERSON><PERSON> vergrendeld door een andere gebruiker.", "RetryLimitExceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> overschreden.", "ReviewSavedSuccessfully": "Beoordeling succesvol opgeslagen", "RoundingError": "Afrondingsfout.", "SSLError": "SSL/TLS verbindingsfout.", "SavedSuccessfully": "Succesvol opgeslagen!", "SendingOnboardingDataFailed": "Onboarding gegevens verzenden mislukt!", "SerializationError": "Dataserialisatiefout.", "ServerError": "Serverfout opgetreden. Probeer het later opnieuw.", "ServiceDegraded": "Service draait in gedegradeerde modus.", "ServiceUnavailable": "Service is ti<PERSON><PERSON><PERSON><PERSON> niet be<PERSON>.", "SessionTimeout": "<PERSON><PERSON> sessie is verlopen. Log opnieuw in.", "SignatureError": "Digitale handtekeningfout.", "SomethingWentWrong": "Er is iets misgegaan", "SpecialCharacterNotAllowed": "speciaal karakter niet <PERSON>", "StorageQuotaExceeded": "Opslagquotum overschreden.", "Success": "Succes!", "TextFieldAdded": "Tekstveld toegevoegd", "TimeoutError": "Verzoek timeout. Probeer het opnieuw.", "TooManyRequests": "Te veel verzoeken. <PERSON><PERSON><PERSON> het later opnieuw.", "TransformationError": "Datatransformatiefout.", "UnderflowError": "Underflow fout.", "UnsavedChanges": "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u wilt vertrekken?", "UserAssignedSuccessfully": "Gebruiker succesvol toegewezen!", "UserNotFound": "Gebruiker niet gevonden.", "ValidationError": "Validatiefout opgetreden.", "VendorAddedSuccessfully": "Leverancier succesvol toegevoegd", "VendorUpdatedSuccessfully": "Leverancier succesvol bijgewerkt", "VersionMismatch": "Versieverschil fout.", "WeakPassword": "Wachtwoord is te zwak. Kies een sterker wachtwoord."}, "FrontEndErrorMessage": {"AccountSetup": {"CompanyStructure": {"BusinessUnitCreatedSuccessfully": "Bedrijfseenheid succesvol aangemaakt", "DepartmentCreatedSuccessfully": "Afdeling succesvol aangemaakt", "DepartmentsCreatedSuccessfully": "Afdelingen succesvol aangemaakt.", "FailedToCreateBusinessUnit": "Bedrijfseenheid aanmaken mislukt", "FailedToCreateDepartment": "Afdeling aanmaken mislukt", "FailedToCreateProcess": "Proces aan<PERSON>ken mislukt", "GroupIdRequired": "Groep-ID vereist maar kon niet worden bepaald", "NameIsRequired": "Naam is vereist", "PleaseFillInField": "Vul het {field} in", "PleaseSelectRegionField": "Selecteer het regioveld", "PleaseUploadCSVFile": "Upload een CSV-bestand", "ProcessCreatedSuccessfully": "Proces succesvol aangemaakt"}, "RoleManagement": {"FailedToAddRole": "Rol toevoegen mislukt", "FailedToDeleteRole": "Rol verwijderen mislukt", "FailedToUpdateRole": "Rol bijwerken mislukt", "RoleAddedSuccessfully": "Rol succesvol toegevoegd!", "RoleDeletedSuccessfully": "Rol succesvol verwijderd!", "RoleUpdatedSuccessfully": "Rol succesvol bijgewerkt!"}, "UserManagement": {"FailedToAddUser": "Gebruiker toe<PERSON>n mislukt", "FailedToDeleteUser": "Gebruiker verwijderen mislukt", "FailedToUpdateUser": "Gebruiker bijwerken mislukt", "Processing": "Verwerken...", "ProcessingIn": "Verwerken in...", "UserAddedSuccessfully": "Gebruiker succesvol toegevoegd!", "UserDeletedSuccessfully": "Gebruiker succesvol verwijderd!", "UserUpdatedSuccessfully": "Gebruiker succesvol bijgewerkt!"}}, "ApiErrors": {"AccessDenied": "Toegang geweigerd. U heeft mogelijk geen toestemming om toegang te krijgen tot deze resource.", "AnErrorOccurred": "Er is een fout opgetreden", "BadRequest": "Slecht verzoek", "ErrorReportSentSuccessfully": "Foutrapport succesvol verzonden.", "ForbiddenError": "403 Verboden fout", "InternalServerError": "Interne serverfout", "NetworkError": "Netwerkfout.", "NotFound": "Resource niet gevonden", "ServiceUnavailable": "Service niet be<PERSON>", "SomethingWentWrong": "Er is iets misgegaan.", "UnexpectedErrorOccurred": "Er is een onverwachte fout opgetreden.", "SomeUpdatesFailed": "Sommige updates zijn mi<PERSON>", "FailedToFetchData": "Kan gege<PERSON>s niet ophalen"}, "AssessmentManagement": {"AssessmentCreatedSuccessfully": "Beoordeling succesvol aangemaakt", "AssessmentDeletedSuccessfully": "Beoordeling succesvol verwijderd", "AssessmentUpdatedSuccessfully": "Beoordeling succesvol bijgewerkt", "ErrorInCreatingQuestion": "Fout bij het aanmaken van vraag", "FailedToCreateAssessment": "Beoordeling aanmaken mislukt", "FailedToDeleteAssessment": "Beoordeling verwijderen mislukt", "FailedToUpdateAssessment": "Beoordeling bijwerken mislukt"}, "Authentication": {"CodeSentSuccessfully": "Code succesvol verzonden!", "FailedToResendOTP": "OTP opnieuw verzenden mislukt", "FailedToVerifyOTP": "OTP verificatie mislukt. Probeer het opnieuw.", "InvalidCredentials": "Ongeldige inloggegevens", "InvalidOTPPleaseTryAgain": "Ongeldige OTP. Probeer het opnieuw.", "LoggingIn": "Inloggen...", "LoginSuccessful": "Inloggen succesvol", "LogoutSuccessful": "Uitloggen succesvol", "OTPVerifiedSuccessfully": "OTP succesvol geverifieerd!", "PasswordUpdatedSuccessfully": "Wachtwoord succesvol bijgewerkt!", "PasswordsDoNotMatch": "Wachtwoorden komen niet overeen!", "PleaseAgreeTermsConditions": "G<PERSON> <PERSON>k<PERSON>ord met onze algemene voorwaarden", "PleaseEnterValidOTP": "<PERSON><PERSON>r een geldige <PERSON> in", "PleaseProvidePassword": "Geef een wachtwoord op!", "SendingCode": "Code verzenden...", "SessionExpired": "<PERSON><PERSON> verlopen", "UnauthorizedAccess": "Ongeautoriseer<PERSON>", "VerificationCodeResentSuccessfully": "Verificatiecode succesvol opnieuw verzonden!"}, "CookieManagement": {"DefaultBannerError": "<PERSON><PERSON> moet precies <PERSON>én standaardbanner zijn. Pas uw selectie aan.", "FailedToAddPolicy": "Beleid toevoegen mislukt", "FailedToUpdatePolicy": "Beleid bijwerken mislukt", "PolicyAddedSuccessfully": "Beleid succesvol toegevoegd", "PolicyUpdatedSuccessfully": "Beleid succesvol bijgewerkt", "SavingTranslation": "Vertaling opslaan...", "ScanFailed": "<PERSON><PERSON> mis<PERSON>t", "ScanStartedSuccessfully": "Scan succesvol gestart", "TranslationSavedSuccessfully": "Vertaling succesvol opgeslagen", "UnableToFetchData": "Kan gege<PERSON>s niet ophalen", "UnableToTranslateData": "Kan gegevens niet vertalen", "AutoScanRequired": "Autoscan is vereist.", "NoGTMConfigurationToApply": "<PERSON><PERSON>-configuratie om toe te passen", "AdobeLaunchSettingsAppliedToAll": "Adobe Launch-instellingen toegepast op alle regels", "NoAdobeLaunchConfigurationToApply": "<PERSON>n <PERSON> Launch-configuratie om toe te passen"}, "DSR": {"FailedToCreateRequest": "Verzoek aanmaken mislukt", "FailedToDeleteRequest": "Verzoek verwijderen mislukt", "FailedToPublishForm": "Formulier publiceren mislukt", "FailedToUpdateRequest": "Verzoek bijwerken mislukt", "FormPublishedSuccessfully": "Formulier succesvol gepubliceerd", "RequestCreatedSuccessfully": "Verzoek succesvol aangemaakt", "RequestDeletedSuccessfully": "Verzoek succesvol verwijderd", "RequestUpdatedSuccessfully": "Verzoek succesvol bijgewerkt", "DocumentsAddedSuccessfully": "Documenten succesvol toegevoegd!", "DocumentDeletedSuccessfully": "Document succesvol verwijderd!", "FailedToDeleteDocument": "Verwijderen van document mislukt. Probeer het opnieuw.", "FailedToDownloadFile": "Download<PERSON> van bestand mislukt. Probeer het opnieuw.", "SubjectIsRequired": "Onderwerp is verplicht", "EmailContentIsRequired": "E-mailinhoud is verplicht", "SubmittingForm": "Formulier verzenden...", "FormSubmittedSuccessfully": "Formulier succesvol verzonden", "FailedToSubmitForm": "<PERSON><PERSON><PERSON><PERSON> van formulier mislukt", "UploadingLogo": "Logo uploaden...", "LogoUploadedSuccessfully": "Logo succesvol geüpload", "FailedToUploadLogo": "Uploaden van logo mislukt", "TemplateDeletedSuccessfully": "Sjabloon succesvol verwijderd!", "DocumentUploadedSuccessfully": "Document succesvol geüpload!", "FailedToUploadDocument": "Uploaden van document mislukt", "DocumentsDeletedSuccessfully": "Documenten succesvol verwijderd!", "RequestApprovedSuccessfully": "Aanvraag succesvol goedgekeurd!", "RequestRejectedSuccessfully": "Aanvraag succesvol afgewezen!", "DeadlineExtendedSuccessfully": "Deadline succesvol verlengd!", "AuditLogDownloading": "Auditlogboek downloaden...", "ProcessingEllipsis": "<PERSON>zig met verwerken...", "UpdatingTask": "Taak bijwerken...", "TaskUpdatedSuccessfully": "Taak succesvol bijgewerkt", "ErrorUpdatingTask": "Bijwerken van taak mislukt", "ErrorLoadingData": "Fout bij het laden van gege<PERSON>s", "MessageCannotBeEmpty": "Bericht mag niet leeg zijn.", "RequestUnderVerification": "<PERSON> a<PERSON>ag wordt gecontroleerd", "ErrorFetchingAssignees": "Fout bij op<PERSON><PERSON> van toe<PERSON>wezen personen", "PleaseSelectAssignee": "Selecteer een toe<PERSON>e", "FailedToAssignUser": "<PERSON><PERSON><PERSON><PERSON><PERSON> mislukt", "ErrorAssigningUser": "Fout bij toe<PERSON><PERSON><PERSON> van gebruiker", "PleaseEnterEmail": "<PERSON>oer een e-mailadres in.", "VerificationCodeSent": "Verificatiecode succesvol verzonden!", "SomethingWrong": "Er is iets mis!", "FailedToResendVerificationCode": "Opnieuw verzenden van verificatiecode mislukt.", "FailedToResendVerificationCodeTryAgain": "Opnieuw verzenden van verificatiecode mislukt. Probeer opnieuw.", "InvalidOTPTryAgain": "Ongeldige code. Probeer opnieuw.", "BusinessUnitUpdated": "Businessunit succesvol bijgewerkt", "Success": "Succes", "FailedToUpdateTask": "Bijwerken van taak mislukt", "CannotDeleteLastStep": "Laatste stap van workflow kan niet worden verwijderd", "WorkflowStepDeletedSuccessfully": "Workflowstap succesvol verwijderd", "FailedToDeleteWorkflowStep": "Verwijderen van workflowstap mislukt", "ErrorDeletingWorkflowStep": "Fout bij verwijderen van workflowstap", "StepTitleCannotBeEmpty": "Stapnaam mag niet leeg zijn", "WorkflowIdMissing": "Workflow-ID ontbreekt", "Processing": "<PERSON>zig met verwerken...", "WorkflowUpdatedSuccessfully": "Workflow succesvol bijgewerkt", "InvalidAPIRoute": "Ongeldig API-pad.", "FailedToUpdateWorkflow": "Bijwerken van workflow mislukt. Probeer het later opnieuw.", "TitleCannotBeEmpty": "Titel mag niet leeg zijn", "ControlUpdatedSuccessfully": "Control succesvol bijgewerkt", "FailedToUpdateControl": "Bijwerken van control mislukt", "TitleRequired": "Titel is verplicht", "FailedToAddQuestion": "Vraag toe<PERSON>n mislukt", "QuestionAddedSuccessfully": "Vraag succesvol toegevoegd", "FailedToAddWorkflow": "Workflow toevoegen mislukt", "WorkflowAddedSuccessfully": "Workflow succesvol toegevoegd", "AddingWorkflow": "Workflow toevoegen", "DuplicateWorkflow": "Dubbele workflow", "PleaseEnterWorkflowType": "<PERSON><PERSON><PERSON> een workflowtype in", "WorkflowCreatedSuccessfully": "Workflow succesvol aangemaakt", "FailedToCreateWorkflow": "Aanmaken van workflow mislukt", "ErrorCreatingWorkflow": "Fout bij aanmaken van workflow", "SubmittedSuccessfully": "Succesvol ingediend", "ProcessingRequest": "Verzoek verwerken", "FiltersAppliedSuccessfully": "Filters succesvol toegepast", "AddingTask": "<PERSON>ak <PERSON>", "TaskAddedSuccessfully": "Taak succesvol toegevoegd", "FailedToAddTask": "Taak toe<PERSON>n mislukt", "UserUnauthaorized": "User unauthorized"}, "DataMapping": {"FailedToAddPii": "PII toevoegen mislukt", "FailedToDeletePii": "PII verwijderen mislukt", "FailedToUpdatePii": "PII bijwerken mislukt", "PiiAddedSuccessfully": "PII succesvol toegevoegd", "PiiAlreadyExists": "PII bestaat al", "PiiDeletedSuccessfully": "PII succesvol verwijderd", "PiiUpdatedSuccessfully": "PII succesvol bijgewerkt"}, "DataValidation": {"DataValidationFailed": "Gegevensvalidatie mislukt", "DuplicateEntryFound": "Dubbele invoer gevonden", "InvalidDataFormat": "Ongeldig gegevensformaat", "InvalidDateFormat": "Ongeldig datumformaat", "InvalidNumberFormat": "Ongeldig nummerformaat", "RequiredDataMissing": "Vereiste gegevens ontbreken", "ValueOutOfRange": "<PERSON><PERSON><PERSON> buiten bereik"}, "ErrorBoundary": {"ErrorCaughtByErrorBoundary": "Fout opgevangen door ErrorBoundary", "ErrorDetails": "Foutdetails", "PageCrashed": "Deze pagina is gecrasht", "RefreshPage": "<PERSON><PERSON><PERSON>", "ReportIssue": "<PERSON><PERSON><PERSON>", "SomethingWentWrong": "Er is iets misgegaan", "UnexpectedError": "Er is een onverwachte fout opgetreden"}, "FileUpload": {"FailedToUploadFile": "Bestand uploaden mislukt", "FileSizeTooLarge": "Bestandsgrootte is te groot", "FileUploadFailed": "Bestand uploaden mislukt", "FileUploadedSuccessfully": "Bestand succesvol geüpload", "InvalidFileType": "Ongeldig bestandstype", "MaxFileSizeExceeded": "Maximale bestandsgrootte overschreden", "PleaseSelectFile": "Selecteer een bestand", "UploadInProgress": "Upload bezig...", "UploadingFile": "Bestand uploaden...", "FailedToDownloadSampleFile": "Kon voorbeeldbestand niet downloaden", "FailedToImportFile": "Kan bestand niet importeren"}, "FormValidation": {"AddressRequired": "<PERSON><PERSON> is vereist", "AdminNameRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is vereist", "ChooseCategory": "Kies alstublieft ten minste één categorie", "QuestionScope": "Selecteer alstublieft de vraagomvang", "AuthenticationTypeRequired": "Authenticatietype is vereist wanneer verificatie is ingeschakeld", "BusinessUnitRequired": "Dit veld is vereist", "CountryRequired": "Land is vereist", "CustomerNameRequired": "Klantnaam is vereist", "ExpiryIsRequired": "Vervaldatum is vereist!", "FormNameRequired": "Dit veld is vereist", "FrequencyValueRequired": "Frequentiewaarde is vereist wanneer frequentie is ingeschakeld!", "IndustryRequired": "Industrie is vereist", "InvalidAdminEmail": "Ongeldig beheerder e-mailadres", "InvalidEmailAddress": "Ongeldig e-mailadres", "InvalidPhoneNumber": "Ongeldig telefoonnummer", "MustAgreeToTerms": "U moet akkoord gaan met de algemene voorwaarden", "PhoneNumberIsRequired": "Telefoonnummer is vereist", "PleaseEnterValidEmailAddress": "<PERSON>oer een geldig e-mailadres in", "PleaseProvideEmailAndPassword": "<PERSON>f zowel e-mail als wachtwoord op.", "PleaseProvidePassword": "Geef een wachtwoord op.", "RegulationRequired": "Ten minste één regelgeving moet worden geselecteerd", "ResourceRequired": "Ten minste één resource moet worden geselecteerd", "TenDigitsRequired": "10 cijfers zijn vereist", "ThisFieldIsRequired": "Dit veld is vereist!", "UsernameMinLength": "Gebruikersnaam moet ten minste 2 karakters zijn.", "EnterName": "<PERSON><PERSON><PERSON> in", "EnterDescription": "<PERSON><PERSON><PERSON> in", "Error": "Fout", "ErrorDot": "Fout.", "PleaseAgreeToAllRequiredConsents": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> met alle vereiste toestem<PERSON>en.", "ErrorSubmittingForm": "Fout bij het indienen van het formulier", "PleaseProvideYourConsentFirst": "<PERSON><PERSON> eerst uw toestemming.", "PleaseInputYourEmail": "<PERSON><PERSON><PERSON> uw e-mailadres in.", "PleaseEnterValidEmail": "<PERSON>oer een geldig e-mailadres in.", "PleaseEnterValidPhoneNumber": "<PERSON><PERSON>r een geldig 10-cij<PERSON><PERSON> telefoonnummer in.", "InvalidInputTypeSpecified": "Ongeldig invoertype ges<PERSON>eerd.", "PleaseEnterValidOTP": "<PERSON><PERSON><PERSON> een geldige 6-cij<PERSON>ige OTP in", "FailedToChangePreferences": "<PERSON>n voor<PERSON>uren niet wijzigen", "InvalidOTP": "Ongeldige OTP", "PreferenceDataNotAvailable": "Voorkeursgegevens niet be<PERSON>.", "PleaseSaveChanges": "Sla wijzigingen op!"}, "HttpClient": {"ForbiddenError": "403 Verboden fout", "NoAccessTokenAvailable": "Geen toegangstoken beschikbaar voor verzoek", "RefreshTokenExpired": "Vernieuwingstoken verlopen", "RequestFailed": "Verzoek mislukt", "ResponseError": "Responsfout", "TokenRefreshFailed": "Token vernieuwen mislukt"}, "Onboarding": {"OnboardingFailed": "Onboarding mislukt", "OnboardingSuccessful": "Onboarding succesvol voltooid", "ProcessCompletedSuccessfully": "Proces succesvol voltooid!", "SendingOnboardingDataFailed": "Onboarding gegevens verzenden mislukt!"}, "PrivacyOps": {"FailedToAddRegulation": "Regelgeving toevoegen mislukt", "FailedToCreateRisk": "Risico aanmaken mislukt", "FailedToDeleteRisk": "Risico verwijderen mislukt", "FailedToUpdateRisk": "Risico bijwerken mislukt", "RegulationAddedSuccessfully": "Regelgeving succesvol toegevoegd", "RiskCreatedSuccessfully": "Risico succesvol aangemaakt", "RiskDeletedSuccessfully": "Risico succesvol verwijderd", "RiskUpdatedSuccessfully": "Risico succesvol bijgewerkt"}, "Profile": {"FailedToSaveChanges": "Wijzigingen opslaan mislukt.", "PasswordUpdateFailed": "Wachtwoord bijwerken mislukt!"}, "ROPA": {"PleaseAnswerAllQuestions": "Beantwoord alle vragen voordat u indient.", "ProgressBarError": "Voortgangsbalk fout!", "RopaSubmittedSuccessfully": "ROPA is succesvol ingediend", "TentativeDateUpdatedSuccessfully": "Voorlopige datum succesvol bijgewerkt", "RecurrenceDateUpdatedSuccessfully": "Herhalingsdatum succesvol bijgewerkt"}, "Unauthorized": {"AccessDenied": "<PERSON><PERSON><PERSON> geweigerd", "ContactAdministrator": "Neem contact op met uw beheerder", "InsufficientPermissions": "U heeft onvoldoende rechten", "LoginRequired": "Inloggen vereist om toegang te krijgen tot deze pagina", "RoadBlockAhead": "Wegversperring vooruit", "Whoops": "Oeps!"}, "VRM": {"AccurateInformationRequired": "Nauwkeurige informatie is vereist.", "AssessmentReviewedSuccessfully": "Beoordeling succesvol beoordeeld!", "AssessmentSubmittedSuccessfully": "Beoordeling succesvol ingediend!", "CommentRequired": "Opmerking is vereist.", "CouldntSaveReview": "Kon beoordeling niet opslaan", "PleaseAnswerAllQuestions": "Beantwoord alle vragen!", "PleaseReviewAllQuestions": "Bekijk alle vragen!", "ReviewSavedSuccessfully": "Beoordeling succesvol opgeslagen", "RiskScoreRequired": "Risicoscore is vereist.", "ProcessingIn": "Verwerken...", "CouldntStartAssessment": "Kon beoordeling niet starten", "CouldntAddVendor": "Kon leverancier niet <PERSON>n", "CouldntUpdateVendor": "Kon leverancier niet bijwerken", "VendorAddedSuccessfully": "Leverancier succesvol toegevoegd", "VendorUpdatedSuccessfully": "Leverancier succesvol bijgewerkt", "PlanSavedSuccessfully": "Plan succesvol opgeslagen", "CouldntSaveMitigationPlan": "Kon mitigatieplan niet op<PERSON>.", "AnErrorOccurred": "Er is een fout opgetreden", "DeletedSuccessfully": "Succesvol verwijderd", "UploadedSuccessfully": "Succesvol geüpload", "DownloadLinkSent": "De downloadlink is verzonden naar uw e-mailadres. Controleer uw inbox", "Success": "Succes", "NewVendor": "+ <PERSON><PERSON>we leverancier", "UserAssignedSuccessfully": "Gebruiker succesvol toegewezen!", "AssigningUserFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> van gebruiker mislukt!", "MitigationPlanSubmittedSuccessfully": "Mitigatieplan succesvol ingediend.", "QuestionUpdatedSuccessfully": "Vraag succesvol bijgewerkt.", "QuestionDeletedSuccessfully": "Vraag is succesvol verwijderd", "Error": "Fout", "ErrorExclamation": "Fout!", "UploadValidFile": "Upload geldig bestand", "ControlAndDescriptionRequired": "Controle en beschrijving zijn vereist.", "UploadFilesOrEnterURL": "Upload bestand(en) of voer URL in", "EnterValidURL": "<PERSON><PERSON>r geldige URL in", "FailedToOpenWindow": "Kon het venster niet openen. Het kan zijn geblokkeerd door de browser.", "EnterTemplateName": "<PERSON><PERSON><PERSON> in"}, "VendorManagement": {"CouldntAddVendor": "Kon leverancier niet <PERSON>n", "FailedToDeleteVendor": "Leverancier verwijderen mislukt", "FailedToUpdateVendor": "Leverancier bijwerken mislukt", "VendorAddedSuccessfully": "Leverancier succesvol toegevoegd", "VendorDeletedSuccessfully": "Leverancier succesvol verwijderd", "VendorUpdatedSuccessfully": "Leverancier succesvol bijgewerkt"}, "UniversalConsentManagement": {"downloadFailed": "Download mislukt", "AddedSuccessfully": "succesvol toegevoegd", "CouldntCreate": "Kon niet a<PERSON>ken", "FillRequiredFields": "Vul verplichte velden in.", "UploadValidFile": "Upload geldig bestand", "Processing": "Verwerking...", "UploadedSuccessfully": "Succesvol geüpload", "UnexpectedError": "Een onverwachte fout is opgetreden", "ProcessingIn": "Verwerking in...", "NameIsRequired": "Naam is verplicht", "DescriptionIsRequired": "Beschrijving is verplicht", "CategoryIsRequired": "Categorie is verplicht", "DataRetentionIsRequired": "Gegevensbewaring is verplicht", "LawfulBasisIsRequired": "Rechtmatige grondslag is verplicht", "PurposeIsRequired": "<PERSON><PERSON> is verplicht", "TypeIsRequired": "Type is verplicht", "ComplianceOfficerIsRequired": "Compliance officer is <PERSON><PERSON><PERSON><PERSON>", "DataSubjectIsRequired": "Betrokkene is verplicht", "SourceIsRequired": "<PERSON><PERSON> is verplicht", "DataImporterIsRequired": "Gegevensimporteur is verplicht", "DataExporterIsRequired": "Gegevensexporteur is verplicht", "PleaseSelectSource": "Selecteer een bron.", "TitleDescriptionRequired": "Voer een titel en beschrijving in voor uw formulier", "FormDescriptionRequired": "<PERSON><PERSON>r een beschrijving in voor uw formulier", "FormTitleRequired": "<PERSON><PERSON>r een titel in voor uw formulier", "PleaseSelectConsentPurpose": "Selecteer een <PERSON>", "FillAllRequiredFields": "Vul alle verplichte velden in.", "NameRequired": "<PERSON>am is verplicht.", "ConsentDescriptionRequired": "Beschrijving is verplicht.", "EnterName": "<PERSON><PERSON><PERSON> in", "EnterDescription": "<PERSON><PERSON><PERSON> in", "PleaseEnterAllFields": "<PERSON>oer alle velden in", "ConsentPurposeUpdatedSuccessfully": "Toestemmingsdoel succesvol bijgewerkt", "ConsentPurposeDeletedSuccessfully": "Toestemmingsdoel succesvol verwijderd", "ProcessingPurposeUpdatedSuccessfully": "Verwerkingsdoel succesvol bijgewerkt", "ProcessingPurposeDeletedSuccessfully": "Verwerkingsdoel succesvol verwijderd", "PiiLabelUpdatedSuccessfully": "PII-label succesvol bijgewerkt", "PiiLabelDeletedSuccessfully": "PII-label succesvol verwijderd", "RecordUpdatedSuccessfully": "Record succesvol bijgewerkt", "PrivacyNoticeSavedSuccessfully": "Privacyverklaring succesvol opgeslagen", "TemplateDownloadedSuccessfully": "Sjabloon succesvol gedownload", "TemplateActivatedSuccessfully": "Sjabloon succesvol geactiveerd", "TemplateInactivatedSuccessfully": "Sjabloon succesvol g<PERSON>erd", "CouldntUpdateConsentPurpose": "<PERSON><PERSON><PERSON><PERSON> niet bijwerken", "CouldntUpdatePiiLabel": "Kon PII-label niet bijwerken", "CouldntUpdateProcessingPurpose": "Kon verwerkingsdoel niet bijwerken", "CouldntUpdateRecordData": "Kon recordgegevens niet bijwerken"}}, "ucm": {"PII Label": "PII-label", "Consent Purpose": "Toestemmingsdoel", "Processing Purpose": "Verwerkingsdoel", "Processing Category": "Verwerkingscategorie", "Dashboard": {"ConsentStatus": "Toestemmingsstatus", "PurposeConsentStatus": "Toestemmingsstatus per <PERSON>", "ConsentByResidency": "Toestemming per Woonp<PERSON>ats", "TotalUsers": "Totaal Aantal Gebruikers", "TotalGrantedConsent": "Toestemmingen Verleend", "TotalDeclinedConsent": "Toestemmingen Geweigerd", "WithdrawnConsent": "Toestemming Ingetrokken"}, "PrivacyNotice": {"PrivacyNoticeName": "<PERSON><PERSON> van het Privacybeleid", "Version": "<PERSON><PERSON><PERSON>", "UpdatedOn": "Bijgewerkt op", "Action": "<PERSON><PERSON>", "Create": "Aanmaken", "SaveDetails": "Details Opslaan", "EnterPrivacyNoticeTitle": "<PERSON><PERSON><PERSON> T<PERSON>l van het Privacybeleid in", "AddSection": "<PERSON><PERSON><PERSON>", "NoSectionsAvailable": "Geen secties beschikbaar", "AddContent": "<PERSON><PERSON><PERSON>", "SelectToViewContent": "Selecteer een sectie om de inhoud te bekijken", "NewContent": "<PERSON><PERSON><PERSON>", "NewSectionTitle": "<PERSON><PERSON><PERSON> van Nieuwe Sectie", "Sections": "Secties"}, "ProcessingCategory": {"ProcessingCategoryName": "<PERSON><PERSON>scategorie", "Description": "Beschrijving", "Action": "<PERSON><PERSON>", "UpdateCategory": "Categorie Bijwerken", "Name": "<PERSON><PERSON>", "AddCategory": "Categorie Toevoegen", "ProcessingCategory": "Verwerkingscategorie", "AddProcessingCategory": "Verwerkingscategorie To<PERSON>n", "ProcessingPurpose": "Verwerkingsdoel", "DeleteCategory": "Verwerkingscategorie Verwijderen", "SureToDelete": "Weet u zeker dat u deze verwerkingscategorie wilt verwijderen?", "No": "<PERSON><PERSON>", "YesProceed": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "EnterCategoryName": "<PERSON><PERSON>r Categorien<PERSON> in", "EnterDescription": "Voer Categorie Beschrijving in"}, "ProcessingPurpose": {"AddProcessingPurpose": "Verwerkingsdoel Toevo<PERSON>n", "ProcessingPurpose": "Verwerkingsdoel", "Description": "Beschrijving", "ProcessingCategory": "Verwerkingscategorie", "Action": "<PERSON><PERSON>", "UpdateProcessingPurpose": "Verwerkingsdoel Bijwerken", "Name": "<PERSON><PERSON>", "DeleteProcessingPurpose": "Verwerkingsdoel Verwijderen", "SureToDelete": "Weet u zeker dat u dit verwerkingsdoel wilt verwijderen?", "EnterPurposeName": "<PERSON><PERSON><PERSON> in", "EnterDescription": "<PERSON><PERSON><PERSON> in", "EnterCategoryName": "<PERSON><PERSON>r Categorien<PERSON> in", "EnterCategoryDescription": "Voer Categorie Beschrijving in"}, "ConsentPurpose": {"AddConsentPurpose": "<PERSON><PERSON>mming<PERSON><PERSON><PERSON>", "ConsentPurpose": "Toestemmingsdoel", "EnterConsentName": "<PERSON><PERSON><PERSON> in", "EnterDescription": "<PERSON><PERSON><PERSON> in", "Description": "Beschrijving", "ProcessingPurpose": "Verwerkingsdoel", "Action": "<PERSON><PERSON>", "UpdateConsentPurpose": "Toestemmingsdoel Bijwerken", "Name": "<PERSON><PERSON>", "EnterCategoryDescription": "Voer Categorie Beschrijving in", "EnterCategoryName": "<PERSON><PERSON>r Categorien<PERSON> in", "DeleteConsent": "Toestemmingsdoel Verwijderen", "SureToDelete": "Weet u zeker dat u dit toestemmings<PERSON> wilt verwijderen?", "RetentionType": "Bewaringstype", "RetentionPeriod": "Bewaarperiode", "ExpiryPeriod": "Vervaldatum"}, "PII": {"AddPII": "PII Toevoegen", "PIILabel": "PII-label", "EnterPIIName": "Voer PII-label<PERSON>am in", "EnterDescription": "Voer Beschrijving van het PII-label in", "Description": "Beschrijving", "UniversalIdentifier": "Universele Identifier", "Action": "<PERSON><PERSON>", "DeletePIILabel": "PII-label Verwijderen", "SureToDelete": "Weet u zeker dat u dit PII-label wilt verwijderen?", "UpdatePIILabel": "PII-label Bijwerken", "Name": "<PERSON><PERSON>", "EnterPIILabelName": "Voer PII-label<PERSON>am in", "EnterPIIDescription": "Voer beschrijving van het PII-label in", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>"}, "CollectionBuilder": {"TemplateName": "Sjabloonnaam", "DataIdentifierType": "Gegeven-ID Type", "EntityName": "Entiteitnaam", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "CreatedDate": "A<PERSON>maak<PERSON><PERSON>", "ModifiedDate": "Wijzigingsdatum", "FormURL": "Toestemmingsformulier URL", "CenterURL": "Voorkeurencentrum URL", "Status": "Status", "Action": "<PERSON><PERSON>", "CreateTemplate": "Maak Sjabloon voor Toestemmingsverzameling"}, "PreferenceCenter": {"AddPreferenceCenter": "Voeg Voorkeurencentrum Toe", "PreferenceCenterName": "<PERSON><PERSON> van het Voorkeurencentrum", "DateandTime": "Datum en Tij<PERSON> van Voorkeurencentrum", "URL": "URL", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Status": "Status", "Actions": "Acties", "BasicInfo": "Basisinformatie", "Customize": "<PERSON><PERSON><PERSON><PERSON>", "Code": "Code", "TemplateName": "Sjabloonnaam", "Description": "Beschrijving", "SubjectIdentityType": "Type Onderwerpidentiteit", "Template": "Sjabloon", "OwnerEmail": "E-mail van <PERSON>", "OwnerName": "<PERSON><PERSON>", "PrivacyPolicyLink": "Privacybeleid Link"}, "SubjectConsentManager": {"Templates": "Sjablonen", "Lists": "<PERSON><PERSON><PERSON>", "ConsentDistributionChartbyTemplate": "Toestemmingsverdeling per <PERSON><PERSON>", "ConsentSourceDistribution": "<PERSON><PERSON>mmingsbronne<PERSON>", "CollectionTemplateName": "<PERSON><PERSON>", "TotalUserConsents": "Totaal Gebruikerstoestemmingen", "Status": "Status", "Action": "<PERSON><PERSON>", "WebForm": "Webformulier", "Form": "<PERSON><PERSON><PERSON>", "WebPreferenceCenter": "Webvoorkeurencentrum", "Manually": "<PERSON><PERSON><PERSON>", "PrefereceCenter": "Voorkeurencentrum", "API": "API", "SubjectIdentity": "Identiteit van het Onderwerp", "Source": "<PERSON><PERSON>", "GeoLocation": "Geolocatie", "ConsentedAt": "Toestemming Gegeven Op", "PIILabelName": "<PERSON><PERSON>-label", "TotalConsents": "Totaal Toestemmingen", "WebConsents": "Webtoestemmingen", "MobileConsents": "<PERSON><PERSON>le To<PERSON>mmingen", "ApiConsents": "API-toestemmingen", "ConsentStatusTypes": "<PERSON>rten Toestemmingsstatus", "Granted": "<PERSON><PERSON><PERSON><PERSON>", "Declined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Withdrawn": "Ingetrokken", "ConsentDistributionByProcessing": "Toestemmingsverdeling per Verwerkingsdoel", "SubjectPreferenceList": "Voorkeurslijst van het Onderwerp", "UserLogs": "Gebruikerslogboeken", "ConsentName": "<PERSON><PERSON>", "Frequency": "Frequentie", "ConsentExpiration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ConsentStatus": "Toestemmingsstatus", "Filter": "Filter", "UserDetails": "Gebruikersdetails", "ConsentFlowVisualization": "<PERSON><PERSON><PERSON> van het Toestemmingsproces", "NoDataAvailable": "<PERSON><PERSON> g<PERSON><PERSON>", "LoadingData": "G<PERSON>vens laden...", "ListOfTemplateTable": "<PERSON><PERSON><PERSON> <PERSON>abel"}}, "DPO": {"Dashboard": {"Risk": {"EntityRisks": "Entiteitsrisico's", "DataMappingRisks": "Risico's <PERSON>", "VendorRisks": "Leveranciersrisico's", "AssessmentRisks": "Beoordelingsrisico's", "RiskByCategory": "Risico per Categorie", "RiskByStages": "<PERSON><PERSON><PERSON> per <PERSON>asen", "MonetaryRisk": "Monetair Risico", "RiskByImpactProbability": "Risico op Basis van Impact en Waarschijnlijkheid", "RegulatoryComplianceBreakdown": "Overzicht van Regelgevende Naleving", "ControlByFramework": "<PERSON><PERSON>", "RecentRisks": "Recente R<PERSON>'s", "RiskID": "Risico-ID", "RiskTitle": "Risicotitel", "RiskCategory": "Risicocategorie", "SourceOfRisk": "<PERSON><PERSON> van het Risico", "DateIdentified": "<PERSON><PERSON> van Identificatie", "RiskDescription": "Beschrijving van het Risico", "Identification": "Identificatie", "Evaluation": "Eva<PERSON>atie", "Mitigation": "<PERSON><PERSON><PERSON><PERSON>", "Closure": "Afsluiting", "Compliance": "<PERSON><PERSON><PERSON>", "DataBreach": "Datalek", "Legal": "<PERSON><PERSON><PERSON>", "Likelihood": "Waarschijnlijkheid", "Severity": "<PERSON>", "GDPR": "AVG", "CCPA": "CCPA", "NIST": "NIST", "DPDPA": "DPDPA", "UAEPDPL": "VAE PDPL", "UAE": "VAE", "DPD": "WBP", "EUG": "EU G", "Controls": "Controles", "Completed": "Voltooid", "Pending": "<PERSON> Afspraak", "Score": "Score"}, "Compliance": {"ComplianceScoreByCategories": "Nalevingsscore per Categorie", "OverAllScore": "Totale Score", "YourScore": "<PERSON><PERSON><PERSON>", "DataProtection": "Gegevensbescherming", "OrganizationControls": "Organisatorische Controles", "TechnologyControls": "Technologische Controles", "SystemSecurity": "Systeembeveiliging", "RegulatoryReporting": "Regelgevende Rapportage", "Policies": "Beleid", "KeyImprovementActions": "Belangrijke Verbeteracties", "ImprovementAction": "Verbeteracties", "Impact": "Impact", "Status": "Status", "Group": "<PERSON><PERSON><PERSON>", "ActionType": "Actietype", "Completed": "Voltooid", "NotCompleted": "Niet Voltooid", "OutOfScope": "<PERSON><PERSON><PERSON>", "ViewImprovementActions": "Bekijk Verbeteracties", "ViewAll": "Bekijk Alles", "PointAchieved": "<PERSON><PERSON><PERSON>", "ComplianceScore": "De nalevingsscore meet je voortgang bij het voltooien van aanbevolen acties die helpen om risico’s rond gegevensbescherming en regelgeving te verminderen.", "DataProtectionDescription": "<PERSON><PERSON><PERSON> versle<PERSON>ling in, beheer toegangsrechten en voorkom datalekken of gegevensverlies.", "OrganizationControlsDescription": "Definieer beveiligingsbeleid, verantwoordelijkheden en kaders om informatiemiddelen te beschermen.", "TechnologyControlsDescription": "Gebruik geautomatiseerde beveiliging, systeemverharding en dataintegriteitsmaatregelen om beleid af te dwingen.", "SystemSecurityDescription": "Beoordeelt de beveiligingsmaatregelen die zijn geïmplementeerd in IT-systemen.", "RegulatoryReportingDescription": "Zorg voor naleving van regelgeving omtrent gegevensbescherming en rapportageverplichtingen.", "PoliciesDescription": "Beoordeelt de geschiktheid en actualiteit van privacybeleid."}}, "ControlHandbook": {"AddCategory": "Categorie Toevoegen", "ControlCategory": "Controlecategorie", "AddRegulation": "Regelgeving Toevoegen", "CreatedOn": "Aangemaakt op", "UpdatedOn": "Bijgewerkt op", "Actions": "Acties", "ControlNo": "Controlenummer", "ControlDescription": "Controlebeschrijving", "SummaryOfInScopeRegulations": "<PERSON><PERSON><PERSON><PERSON> van Relevante Regelgevingen", "LoadingRegulations": "Regelgevingen laden", "data_submitted_successfully": "Gegevens succesvol verzonden", "please_fill_all_fields": "Vul alstublieft alle velden in", "please_add_business_requirement": "Voeg alstublieft een bedrijfsvereiste toe", "category_name_required": "Categorie naam is verplicht", "control_number_required": "Controlenummer is verplicht", "category_number_required": "Categorienummer is verplicht", "control_description_required": "Controlebeschrijving is verplicht", "AddControlCategory": {"CategoryName": "Categorienaam", "ControlDescription": "Controlebeschrijving", "ControlNo": "Controlenummer", "CategoryNo": "Categorie Nummer", "EnterCategoryName": "<PERSON><PERSON>r Categorien<PERSON> in", "EnterControlDescription": "<PERSON><PERSON><PERSON> in", "EnterControlNo": "<PERSON><PERSON><PERSON> in", "EnterCategoryNo": "<PERSON><PERSON><PERSON> in", "Reference": "Referentie", "EnterReference": "<PERSON><PERSON><PERSON>-ID in", "BusinessRequirement": "Zakelijke Vereiste", "EnterBusinessRequirement": "<PERSON><PERSON>r Zakelijke Vereisten in", "EnterCommaSeparatedValues": "<PERSON><PERSON>r komma-gescheiden waarden in", "SaveBusinessRequirement": "Zakelijke Vereiste Opslaan"}, "AddRegulations": {"AddRegulation": "Nieuwe regelgeving toevoegen", "FillInDetailsToAddRegulation": "Vul de details in om een nieuwe regelgeving toe te voegen.", "Geography": "G<PERSON><PERSON><PERSON>", "MappingColumnHeader": "Kolomkop voor mapping", "Source": "<PERSON><PERSON>", "AuthoritativeSource": "Gezaghebbende bron", "Version": "<PERSON><PERSON><PERSON>", "URL": "URL", "Available": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GroupIDs": "Groep-ID's", "ISRegulationAvailable": "Is deze regelgeving momenteel besch<PERSON>?", "SelectAssignee": "Selecteer toegewezene", "RegulationAddedSuccessfully": "Regelgeving succesvol toegevoegd", "FailedToAddRegulation": "Kon regelgeving niet toe<PERSON>n"}}, "Regulation": {"RegulationName": "Regelgeving", "CreatedOn": "Aangemaakt op", "UpdatedOn": "Bijgewerkt op", "ComplianceStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewDetails": "Details bekijken", "SelectEntity": "Selecteer entiteit", "importSign": "importteken", "RepositoryDocumentUpload": "Privacy-Ops repository document upload", "AddControlDialog": {"AddControl": "<PERSON>e toe<PERSON>n", "ControlNumber": "Controlenummer", "ControlDescription": "Controledescription", "ArticleNumber": "Artikelnummer", "RegulationSummary": "<PERSON><PERSON><PERSON><PERSON> van regelgeving"}, "RegulationDetails": {"ComplianceStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PartialComplianceStatus": "Gedeeltelijke nalevingsstatus", "NonComplianceStatus": "Ni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NavHeadings": {"Controls": "Controles", "Documents": "Documenten", "Duties": "Taken", "Actions": "Acties", "Improvements": "Verbeteringen"}, "EditControl": {"EditControl": "Controle bewerken", "Applicable": "<PERSON>", "Document": "Document", "SelectDocument": "Selecteer document", "NotApplicable": "<PERSON><PERSON>", "ComplianceStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Observation": "Observatie", "AddObservation": "Observatie <PERSON>", "Select": "Selecteren", "Compliant": "Conform", "PartiallyCompliant": "Gedeeltelijk conform", "NonCompliant": "Niet conform", "AddYourObservationHere": "Voeg hier uw observatie toe..."}, "Controls": {"ControlNumber": "Controlenummer", "ControlDescription": "Controlebeschrijving", "RegulationSummary": "<PERSON><PERSON><PERSON><PERSON> van toepasselijke regelgeving", "NoDataAvailable": "<PERSON><PERSON> g<PERSON><PERSON>"}, "ExtendedControlTabel": {"ReferenceId": "Referentie-ID", "BusinessRequirement": "Zakelijke vereiste", "ArticleNumber": "Artikelnummer", "Applicability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Document": "Document", "ComplianceStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Observation": "Observatie", "Action": "<PERSON><PERSON>", "AddToAction": "Toevoegen aan actie", "AddToDuty": "Toevoege<PERSON> aan p<PERSON>t", "AddToImprovement": "Toevoegen aan verbetering", "Action Added Successfully": "<PERSON><PERSON> succesvol toegevoegd"}, "Documents": {"Document": "Document", "Description": "Beschrijving", "Category": "Categorie", "CreatedOn": "Aangemaakt op", "CreatedBy": "Aangemaakt door", "Attachment": "B<PERSON>jlage"}, "Duties": {"Overdue": "Achterstallig", "NoOverdueData": "<PERSON><PERSON> acht<PERSON>ge gegevens", "Open": "Open", "NoOpenData": "Geen open gegevens"}, "Actions": {"ActionTitle": "Actietitel", "AssignedTo": "Toegewezen aan", "AssignedBy": "Toegewezen door", "AssignedDate": "Toegewezen datum", "Deadline": "Deadline", "Status": "Status", "NoResult": "<PERSON><PERSON>"}, "Improvements": {"Overdue": "Achterstallig", "NoOverdueDuties": "<PERSON><PERSON> a<PERSON>ge taken", "NoCompletedDuties": "<PERSON><PERSON> taken", "Open": "Open"}}, "RiskRegister": {"NavHeadings": {"Activity": "Activiteit", "Audit Log": "Auditlogboek", "RiskEvaluationUpdatedSuccessfully": "Risicoevaluatie succesvol bijgewerkt"}, "Activity": {"RiskTitle": "Risicotitel", "RiskCategory": "Risicocategorie", "SourceOfRisk": "<PERSON><PERSON> van het risico", "DateIdentified": "Datum geïdentificeerd", "RiskDescription": "Risicobeschrijving", "Regulation": "Regelgeving", "Module": "<PERSON><PERSON><PERSON>", "Entity": "Entiteit", "CreateRisk": "Risico aanmaken"}, "RiskForm": {"RiskTitle": "Risicotitel", "Write": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DescriptionOfRisk": "Beschrijving van het risico", "EnterDescription": "<PERSON><PERSON>r een beschrijving in...", "Module": "<PERSON><PERSON><PERSON>", "Select": "Selecteren", "RiskCategory": "Risicocategorie", "TentativeDate": "Voorlopige datum", "PickADate": "<PERSON>es een datum", "SourceOfRisk": "<PERSON><PERSON> van het risico", "Threat": "Bedreiging", "Vulnerability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Regulation": "Regelgeving", "Entity": "Entiteit", "RiskCreatedSuccessfully": "Risico succesvol aangemaakt"}, "Steeper": {"SteeperHeadings": {"Identified": "Geïdentificeerd", "Evaluation": "Eva<PERSON>atie", "Mitigation": "Minderwaardiging", "Closure": "Sluiting", "Monitoring": "Bewaking"}, "EvaluationStep": {"Evaluation": "Eva<PERSON>atie", "Category": "Categorie", "Compliance": "<PERSON><PERSON><PERSON>", "DateClosed": "<PERSON><PERSON>", "PickADate": "<PERSON>es een datum", "DateCreated": "A<PERSON>maak<PERSON><PERSON>", "Deadline": "Deadline", "Description": "Beschrijving", "RiskId": "Risico-ID", "InherentRiskLevel": "<PERSON><PERSON><PERSON>", "ResidualRiskLevel": "Rest Risiconiveau", "Reminder": "<PERSON><PERSON><PERSON>", "Organization": "Organisatie", "Result": "Resultaat", "RiskApprover": "<PERSON><PERSON><PERSON>", "RiskName": "Risiconaam", "RiskOwner": "<PERSON><PERSON><PERSON>", "RiskTemplate": "Risico <PERSON>", "Source": "<PERSON><PERSON>", "TargetRiskLevel": "<PERSON><PERSON>", "Treatment": "Behandeling", "TreatmentPlan": "Behandelplan", "TreatmentStatus": "Behandelstatus", "Threat": "Bedreiging", "Type": "Type", "Vulnerability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "MitigationStep": {"Strategy": "Strategie", "Write": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "YourProgress": "<PERSON><PERSON>", "AddAction": "<PERSON><PERSON>", "ActionTitle": "Actietitel", "AssignedTo": "Toegewezen aan", "AssignedBy": "Toegewezen door", "AssignedDate": "Toewijzingsdatum", "Deadline": "Deadline", "Status": "Status", "ActionAddedSuccessfully": "<PERSON><PERSON> succesvol toegevoegd", "ProceedToNextStep": "Ga naar volgende stap", "ConfirmationText": "Bent u tevreden over de migratie", "AddActionForm": {"AddAction": "<PERSON><PERSON>", "Category": "Categorie", "Entity": "Entiteit", "Select": "Selecteren", "Description": "Beschrijving", "AssignedBy": "Toegewezen door", "AssignedTo": "Toegewezen aan", "AssignDate": "Toewijzingsdatum", "Deadline": "Deadline", "PickADate": "<PERSON>es een datum"}}, "ClosureStep": {"ClosureReview": "Afsluitingsbeoordeling", "Write": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AssociateDuty": "<PERSON><PERSON> p<PERSON>t", "Duty": "<PERSON><PERSON><PERSON>", "AssociatePolicyProcedure": "Koppel beleidsprocedure", "PolicyProcedure": "Beleidsprocedure", "AssociateImprovement": "<PERSON><PERSON> verbetering", "Improvement": "Verbetering"}, "MonitoringStep": {"Title": "Titel", "Comment": "Opmerking", "Standard": "Norm", "CreatedDate": "A<PERSON>maak<PERSON><PERSON>", "DueDate": "Vervaldatum", "Status": "Status"}}}, "Activities": {"Duty": {"TOTAL": "TOTAAL", "OPEN": "OPEN", "ARCHIVE": "ARCHIEF", "NoArchiveData": "<PERSON><PERSON>", "Archieved": "Gearchiveerd", "Active": "Actief", "Open": "Open", "Overdue": "Achterstallig", "NoOpenData": "Geen open gegevens", "AddNew": "NIEUWE TOEVOEGEN", "DutyDetails": "Taakdetails", "Fulfilment": "Uitvoering", "AuditLog": "Auditlogboek", "Description": "Beschrijving", "Name": "<PERSON><PERSON>", "Date": "Datum", "Time": "Tijd", "AddDutyForm": {"AddDuty": "<PERSON>ak <PERSON>", "AddTag": "Tag toevoegen", "DutyTitle": "<PERSON><PERSON><PERSON>", "AddAssignee": "To<PERSON><PERSON><PERSON><PERSON> toevoegen", "SelectAssignee": "Selecteer een ve<PERSON>", "DueDate": "Vervaldatum", "PickADate": "<PERSON>es een datum", "Entity": "Entiteit", "Select": "Selecteren", "Standards": "Normen", "EnterYourStandardsHere": "<PERSON><PERSON><PERSON> hier uw normen in...", "Comment": "Opmerking", "EnterYourCommentsHere": "<PERSON><PERSON><PERSON> hier uw op<PERSON>en in...", "Attachment": "B<PERSON>jlage", "AddDocument": "Document toevoegen", "SelectedFiles": "Geselecteerde bestanden:"}, "DutyDetailForm": {"DutyTitle": "<PERSON><PERSON><PERSON>", "Dummy": "dummy", "AssignedTo": "Toegewezen aan", "SelectAssignee": "Selecteer ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Status": "Status", "Open": "OPEN", "Completed": "VOLTOOID", "StartDate": "Startdatum", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "DueDate": "Vervaldatum", "DueDateValue": "7 mei 2025", "Frequency": "Frequentie", "Select": "Selecteer", "Standards": "Normen", "Criteria": "Criteria", "DefinitionOfEvidence": "<PERSON><PERSON><PERSON><PERSON>", "Comments": "Opmerkingen", "Attachment": "B<PERSON>jlage", "AddDocument": "Document toevoegen", "SelectedFiles": "Geselecteerde bestanden:", "Annually": "Jaarlijks", "BiAnnual": "Halfjaarlijks", "TriMonthly": "Dr<PERSON><PERSON>andeli<PERSON><PERSON>", "Monthly": "Ma<PERSON><PERSON><PERSON><PERSON>"}, "DutyFulfilmentForm": {"DutyInterval": "<PERSON><PERSON><PERSON><PERSON>", "PickDate": "<PERSON>es een datum", "Assigned": "Toegewezen", "Select": "Selecteer", "FulfilledBy": "Uitgevoerd door", "Write": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Status": "Status", "FulfilmentProof": "<PERSON><PERSON><PERSON><PERSON>", "VerificationQuestion": "Is de effectiviteit van de taak geverifieerd?", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>", "Attachments": "Bijlagen", "Vector": "vector", "AddDocuments": "Documenten toevoegen", "URLLink": "URL-link", "LandingPageForm": "Landingspagina<PERSON><PERSON><PERSON>", "WebsiteSignup": "Website-aanmelding", "SocialMedia": "Sociale media", "Comment": "Opmerking", "EnterURL": "Voer URL in", "Finalized": "Gefinaliseerd"}}, "Action": {"AddActionForm": {"AddAction": "<PERSON><PERSON>", "Category": "Categorie", "Entity": "Entiteit", "Select": "Selecteren", "Description": "Beschrijving", "AssignedBy": "Toegewezen door", "AssignedTo": "Toegewezen aan", "AssignDate": "Toewijzingsdatum", "PickDate": "<PERSON>es een datum", "Deadline": "Deadline"}, "ActionActivity": {"TotalActionItems": "Totaal aantal actie-items", "OpenActionItems": "Open actie-items", "ClosedActionItems": "Gesloten actie-items", "AddNew": "NIEUWE TOEVOEGEN", "ActionAddedSuccessfully": "<PERSON><PERSON> succesvol toegevoegd", "ActionTitle": "Actietitel", "AssignedTo": "Toegewezen aan", "AssignedBy": "Toegewezen door", "AssignedDate": "Toewijzingsdatum", "Deadline": "Deadline", "Status": "Status"}}, "Improvement": {"TOTAL": "TOTAAL", "INPROGRESS": "BEZIG", "ARCHIVE": "ARCHIEF", "Open": "Open", "Completed": "Voltooid", "Overdue": "Achterstallig", "ImprovementDetails": "Verbeteringsdetails", "Evaluation": "Eva<PERSON>atie", "AuditLog": "Auditlogboek", "NoCompletedData": "<PERSON><PERSON> voltooide gege<PERSON>s", "ImprovementDetailsForm": {"DutyTitle": "<PERSON><PERSON>", "DummyValue": "pkjw", "AssignedTo": "Toegewezen aan", "SelectAssignee": "Selecteer <PERSON><PERSON><PERSON><PERSON><PERSON>", "Status": "Bezig", "Completed": "Voltooid", "DueDate": "Vervaldatum", "DueDateValue": "10 maart 2025", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Regulation": "Regelgeving", "Select": "Selecteren", "Finding": "Andere gebieden beïnvloed door dezelfde bevinding", "RootCause": "O<PERSON>zaak", "TreatmentPlan": "Behandelplan", "Attachment": "B<PERSON>jlage", "AddDocument": "Document toevoegen", "TechTeamInvolved": "Is er een technisch team betrokken bij de implementatie?", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>", "SelectedFiles": "Geselecteerde bestanden:", "AddProgress": "Voeg je voortgang toe"}, "AddNewImprovementForm": {"AddImprovement": "Verbetering Toevoegen", "AddTag": "Label Toevoegen", "ImprovementActionTitle": "<PERSON><PERSON><PERSON> van Verbeteringsactie", "Title": "Titel", "AddAssignee": "Toegewezene Toevoegen", "SelectAssignee": "Selecteer <PERSON><PERSON><PERSON><PERSON><PERSON>", "DueDate": "Vervaldatum", "PickDate": "<PERSON>es een datum", "Regulation": "Regelgeving", "Select": "Selecteren", "Entity": "Entiteit", "Comment": "Opmerking", "Comments": "Opmerkingen", "Attachment": "B<PERSON>jlage", "AddDocument": "Document Toevoegen", "SelectedFiles": "Geselecteerde Bestanden:"}, "EvaluationForm": {"ReviewedBy": "Beoordeeld door", "ReviewedByPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EffectOnFinding": "Heeft het invloed op de bevinding?", "EffectOnFindingPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OtherAreasAffected": "Andere gebieden getroffen door dezelfde bevinding", "OtherAreasAffectedPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ManagementReviewTopic": "Onderwerp van managementbeoordeling", "Select": "Selecteren", "ManagementReviewText": "Tekst voor de managementbeoordeling", "LandingPageForm": "Landingspagina<PERSON><PERSON><PERSON>", "WebsiteSignup": "Website-aanmelding", "SocialMedia": "Sociale media", "Source": "<PERSON><PERSON>"}}}, "Repository": {"DocumentRepository": {"Total": "Totaal", "Creation": "<PERSON><PERSON><PERSON>", "In Progress": "In Behandeling", "In Use": "In Gebruik", "Document": "Document", "Description": "Beschrijving", "Category": "Categorie", "CreatedOn": "Gemaakt op", "CreatedBy": "Gemaakt door", "Attachments": "Bijlagen"}, "AssesmentRepository": {"Total": "Totaal", "In Progress": "In Behandeling", "Completed": "Voltooid", "Department": "Afdeling", "AssessmentName": "Beoordelingsnaam", "AssessmentType": "Beoordelingstype", "CreatedDate": "Gemaakt op", "CreatedBy": "Gemaakt door", "Action": "<PERSON><PERSON>"}, "RecordOfProcessingActivitiesRepository": {"Total": "Totaal", "In Progress": "In Behandeling", "Completed": "Voltooid", "Department": "Afdeling", "Process": "Proces", "CreatedDate": "Gemaakt op", "CreatedBy": "Gemaakt door", "Version": "<PERSON><PERSON><PERSON>", "Action": "<PERSON><PERSON>"}}}, "AuditLog": {"RequestStatusChangedToAcknowledgement": "Status van aanvraag gewijzigd naar Erkenning", "RequestStatusChangedToDataGathering": "Status van aanvraag gewijzigd naar Gegevensverzameling"}, "ToastMessages": {"Authentication": {"OTPVerifiedSuccessfully": "OTP succesvol geverifieerd!", "OTPSentSuccessfully": "OTP succesvol verzonden", "OTPResentSuccessfully": "OTP succesvol opnieuw verzonden", "VerificationCodeSentSuccessfully": "Verificatiecode succesvol verzonden", "VerificationCodeResentSuccessfully": "Verificatiecode succesvol opnieuw verzonden", "PasswordUpdatedSuccessfully": "Wachtwoord succesvol bijgewerkt", "CodeSentSuccessfully": "Code succesvol verzonden", "FailedToSendOTP": "Kan O<PERSON> niet verzenden. Probeer opnieuw."}, "CookieConsent": {"CategorySavedSuccessfully": "Categorie succesvol opgeslagen", "ServiceSavedSuccessfully": "Service succesvol opgeslagen", "CookieSavedSuccessfully": "<PERSON><PERSON> succesvol opgeslagen", "DomainDetailsUpdatedSuccessfully": "Domeingegevens succesvol bijgewerkt.", "CookieCreatedSuccessfully": "<PERSON><PERSON> succes<PERSON>l aangema<PERSON>t"}, "Forms": {"FormSubmittedSuccessfully": "Formulier succesvol ingediend", "FormTranslatedSuccessfully": "<PERSON>ulier succesvol vertaald", "DataSavedSuccessfully": "Gegevens succesvol opgeslagen", "DataUpdatedSuccessfully": "Gegevens succesvol bijgewerkt", "OperationSuccessful": "Bewerking succesvol", "ProcessCompletedSuccessfully": "Proces succesvol voltooid", "SubmittedSuccessfully": "Succesvol ingediend", "AllChangesSaved": "Alle wijzigingen opgeslagen", "SavedSuccessfully": "Succesvol opgeslagen"}, "UCM": {"ProcessingPurposeUpdatedSuccessfully": "Verwerkingsdoel succesvol bijgewerkt"}, "Files": {"FileUploadedSuccessfully": "Bestand succesvol geüpload", "FileImportedSuccessfully": "Bestand succesvol geïmporteerd", "FileProcessedSuccessfully": "Bestand succesvol verwerkt", "DocumentUploadedSuccessfully": "Document succesvol geüpload", "DocumentDeletedSuccessfully": "Document succesvol verwijderd", "DocumentsAddedSuccessfully": "Documenten succesvol toegevoegd", "DocumentsDeletedSuccessfully": "Documenten succesvol verwijderd", "UploadedSuccessfully": "Succesvol geüpload", "TemplateDownloadedSuccessfully": "Sjabloon succesvol gedownload"}, "Assessment": {"AssessmentCreatedSuccessfully": "Beoordeling succesvol aangemaakt", "AssessmentSubmittedSuccessfully": "Beoordeling succesvol ingediend", "AssessmentReviewedSuccessfully": "Beoordeling succesvol beoordeeld", "AnswerSavedSuccessfully": "Antwoord succesvol opgeslagen", "ReviewSavedSuccessfully": "Beoordeling succesvol opgeslagen", "PleaseAnswerAllQuestions": "Beantwoord alle vragen!", "ProgressBarError": "Voortgangsbalk fout!", "ErrorInUpdatingTheQuestion": "Fout bij het bijwerken van de vraag", "FailedToCreateAssessment": "Beoordeling aanmaken mislukt", "FailedToLoadCategoryTypes": "Kon categorietypes niet laden", "PleaseReviewAllQuestions": "Bekijk alle vragen!"}, "ROPA": {"ROPASubmittedSuccessfully": "ROPA succesvol ingediend", "ROPAStartedSuccessfully": "ROPA succesvol gestart", "ROPADataUpdatedSuccessfully": "ROPA-gegevens succesvol bijgewerkt", "TentativeDateUpdatedSuccessfully": "Voorlopige datum succesvol bijgewerkt"}, "Users": {"UserAssignedSuccessfully": "Gebruiker succesvol toegewezen", "UserAddedSuccessfully": "Gebruiker succesvol toegevoegd", "UserUpdatedSuccessfully": "Gebruiker succesvol bijgewerkt", "PasswordResetEmailSentSuccessfully": "Wachtwoord reset e-mail succesvol verzonden"}, "Vendors": {"VendorAddedSuccessfully": "Leverancier succesvol toegevoegd", "VendorUpdatedSuccessfully": "Leverancier succesvol bijgewerkt"}, "Cookies": {"CookieUpdatedSuccessfully": "Cookie succesvol bijgewerkt", "TranslationSavedSuccessfully": "Vertaling succesvol opgeslagen", "PolicyAddedSuccessfully": "Beleid succesvol toegevoegd", "PolicyUpdatedSuccessfully": "Beleid succesvol bijgewerkt"}, "Consent": {"ConsentPurposeUpdatedSuccessfully": "Toestemmingsdoel succesvol bijgewerkt", "ConsentPurposeDeletedSuccessfully": "Toestemmingsdoel succesvol verwijderd", "ProcessingPurposeAddedSuccessfully": "Verwerkingsdoel succesvol toegevoegd", "ProcessingPurposeUpdatedSuccessfully": "Verwerkingsdoel succesvol bijgewerkt", "ProcessingPurposeDeletedSuccessfully": "Verwerkingsdoel succesvol verwijderd"}, "Workflow": {"WorkflowCreatedSuccessfully": "Workflow succesvol aangemaakt", "WorkflowUpdatedSuccessfully": "Workflow succesvol bijgewerkt", "WorkflowAddedSuccessfully": "Workflow succesvol toegevoegd", "TaskAddedSuccessfully": "Taak succesvol toegevoegd", "TaskUpdatedSuccessfully": "Taak succesvol bijgewerkt", "TaskAutomationUpdatedSuccessfully": "Taakautomatisering succesvol bijgewerkt", "AutomationRemovedSuccessfully": "Automatisering succesvol verwijderd", "StepAddedSuccessfully": "Stap succesvol toegevoegd", "WorkflowStepDeletedSuccessfully": "Workflow-stap succesvol verwijderd", "WorkflowStepRenamedSuccessfully": "Workflow-stap succesvol hernoemd"}, "PII": {"PIIAddedSuccessfully": "PII succesvol toegevoegd", "PIIEditedSuccessfully": "PII succesvol bewerkt", "PIIUpdatedSuccessfully": "PII succesvol bijgewerkt", "PIIDeletedSuccessfully": "PII succesvol verwijderd", "PIIsUpdatedSuccessfully": "PII's succesvol bijgewerkt"}, "DataCatalogue": {"ServiceAddedSuccessfully": "Service succesvol toegevoegd", "TaskTriggeredSuccessfully": "Taak succesvol geactiveerd", "CredentialsSavedSuccessfully": "Inloggegevens succesvol opgeslagen", "FailedToSaveCredentials": "<PERSON>n inloggege<PERSON>s niet opsla<PERSON>. Controleer uw invoer en probeer opnieuw."}, "General": {"CopiedToClipboard": "Gekopieerd naar klembord!", "DashboardUpdatedSuccessfully": "Dashboard succesvol bijgewerkt", "DashboardDeletedSuccessfully": "Dashboard succesvol verwijderd", "URLCopiedToClipboard": "URL gekopieerd naar klembord!", "ScanStartedSuccessfully": "Scan succesvol gestart", "ErrorReportSentSuccessfully": "Foutrapport succesvol verzonden", "TicketCreatedSuccessfully": "Ticket succesvol aangemaakt", "BusinessUnitUpdatedSuccessfully": "Bedrijfseenheid succesvol bijgewerkt", "DepartmentUpdatedSuccessfully": "Afdeling succesvol bijgewerkt", "ProcessUpdatedSuccessfully": "Proces succesvol bijgewerkt", "QuestionAddedSuccessfully": "Vraag succesvol toegevoegd", "QuestionUpdatedSuccessfully": "Vraag succesvol bijgewerkt", "QuestionDeletedSuccessfully": "Vraag succesvol verwijderd", "TextFieldAdded": "Tekstveld toegevoegd", "DeletedSuccessfully": "Succesvol verwijderd", "Success": "Succes!", "RequestApprovedSuccessfully": "Verzoek succesvol goedgekeurd", "RequestRejectedSuccessfully": "Verzoek succesvol afgewezen", "RequestArchivedSuccessfully": "Verzoek succesvol gearchiveerd", "RequestUnarchivedSuccessfully": "Verzoek succesvol uit archief gehaald", "RequestCompletedSuccessfully": "Verzoek succesvol voltooid", "MailTemplateSentSuccessfully": "E-mailsjabloon succesvol verzonden", "TemplateDeletedSuccessfully": "Sjabloon succesvol verwijderd", "FiltersAppliedSuccessfully": "Filters succesvol toegepast", "RulesAppliedSuccessfully": "Regels succesvol toegepast", "OptionRemovedSuccessfully": "Optie succesvol verwijderd", "LogoUploadedSuccessfully": "Logo succesvol geüpload", "URLCopiedSuccessfully": "URL succesvol gekopieerd", "FormCreatedSuccessfully": "<PERSON>ulier succesvol aangemaakt", "QuestionOrderUpdatedSuccessfully": "Vraagvolgorde succesvol bijgewerkt", "QuestionRemovedSuccessfully": "Vraag succesvol verwijderd", "FormSavedSuccessfully": "Formulier succesvol opgeslagen", "FormVersionCreatedSuccessfully": "Formulierversie succesvol aangemaakt", "FailedToFetchControls": "Kon controles niet ophalen", "RejectMessageCannotBeEmpty": "Afwijzingsbericht kan niet leeg zijn", "FailedToSendMailTemplate": "Kon e-mailsjabloon niet verzenden", "DataSubjectEmailNotAvailable": "E-mail van betrokkene niet besch<PERSON>", "FailedToFetchReviewers": "<PERSON><PERSON> be<PERSON> niet ophalen", "AutoScanRequired": "Automatische scan is vereist", "FailedToUpdateCookie": "Kon cookie niet bijwerken", "PIIAnalysisCompletedSuccessfully": "PII-analyse succesvol voltooid", "RetentionRuleSavedSuccessfully": "Bewaarregel succesvol opgeslagen", "FailedToSaveRetentionRule": "Kon bewa<PERSON>regel niet opslaan", "UploadFilesOrEnterURL": "Upload bestanden of voer URL in", "EnterValidURL": "<PERSON><PERSON>r geldige URL in", "UploadValidFile": "Upload geldig bestand", "EnterTemplateName": "<PERSON><PERSON><PERSON> in", "DuplicateStepsChooseDifferentName": "<PERSON><PERSON><PERSON> stappen. <PERSON><PERSON> een andere naam", "StepAddedSuccessfully": "Stap succesvol toegevoegd", "FailedToAddWorkflowStep": "Kon workflowstap niet toe<PERSON>n", "ErrorWhileAddingWorkflow": "Fout bij toevoegen van workflow", "TentativeDateUpdatedSuccessfully": "Voorlopige datum succesvol bijgewerkt", "CouldntUpdateTentativeDate": "Kon voorlopige datum niet bijwerken", "PublicURLGenerated": "Openbare URL gegenereerd", "DeadlineExtendedSuccessfully": "Deadline succesvol verlengd", "BusinessUnitUpdated": "Bedrijfseenheid bijgewerkt", "ActionAddedSuccessfully": "<PERSON><PERSON> succesvol toegevoegd", "MitigationPlanSubmittedSuccessfully": "Mitigatieplan succesvol ingediend", "PlanSavedSuccessfully": "Plan succesvol opgeslagen", "RegulationAddedSuccessfully": "Regelgeving succesvol toegevoegd", "RiskCreatedSuccessfully": "Risico succesvol aangemaakt", "ImportedSuccessfully": "Succesvol geïmporteerd", "RecordUpdatedSuccessfully": "Record succesvol bijgewerkt", "PrivacyNoticeSavedSuccessfully": "Privacyverklaring succesvol opgeslagen", "ProcessingCategoryUpdatedSuccessfully": "Verwerkingscategorie succesvol bijgewerkt", "ProcessingCategoryDeletedSuccessfully": "Verwerkingscategorie succesvol verwijderd", "PIILabelUpdatedSuccessfully": "PII-label succesvol bijgewerkt", "PIILabelDeletedSuccessfully": "PII-label succesvol verwijderd", "TemplateActivatedSuccessfully": "Sjabloon succesvol geactiveerd", "TemplateInactivatedSuccessfully": "Sjabloon succesvol g<PERSON>erd", "TranslatedDataFetched": "Vertaalde gegevens opgehaald", "FormTranslated": "<PERSON><PERSON><PERSON> vertaald", "GTMSettingsAppliedToAll": "GTM-instellingen toegepast op alle", "MappingDeletedSuccessfully": "Mapping succesvol verwijderd", "BreachEvaluationUpdatedSuccessfully": "Inbreukevaluatie succesvol bijgewerkt", "BreachAddedSuccessfully": "Inbreuk succesvol toegevoegd", "BreachResolutionDoneSuccessfully": "Inbreukoplossing succesvol voltooid", "PreventiveActionAddedSuccessfully": "Preventieve actie succesvol toegevoegd", "NotificationSavedSuccessfully": "Melding succesvol opgeslagen", "CustomerAddedSuccessfully": "<PERSON><PERSON> succesvol toegevoegd", "CustomerUpdatedSuccessfully": "Klant succesvol bijgewerkt", "ResourceAddedSuccessfully": "Resource succesvol toegevoegd", "CollaboratorAddedSuccessfully": "Medewerker succesvol toegevoegd", "PreferencesChangedSuccessfully": "Voorkeuren succesvol gewijzigd", "AddedSuccessfully": "{{type}} succesvol toegevoegd", "AssessmentStartedSuccessfully": "Beoordeling succesvol gestart"}}}