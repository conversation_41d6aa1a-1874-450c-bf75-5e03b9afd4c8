import { ColumnDef } from '@tanstack/react-table';
import { debounce } from 'lodash';
import { ArrowUpDown, Download, EllipsisVertical, Eye, FileScan, Trash } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import { Input } from '../../../@/components/ui/Common/Elements/Input/Input';

import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '../../../@/components/ui/dropdown-menu';
import plusSign from '../../../assets/plusSign.svg';
import search from '../../../assets/Search.svg';
import { cookieConsentManagementActions } from '../../../redux/reducers/CookieConsentManagement/cookie-consent-management-slice';
import { RootState } from '../../../redux/store';
import { downloadFileFromUrl } from '../../../utils/helpers/downloadFileFromUrl.helper';
import { COOKIE_CONFIGURATION } from '../../../utils/routeConstant';
import { DOWNLOAD_COOKIE_REPORT } from '../../common/api';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import {
  auto_scan_cookie,
  delete_cookie,
  get_cookie_consent_domain,
} from '../../common/services/cookie-consent-management';
import ShadcnDialog from '../../common/shadcn-dialog';
import DynamicTable from '../../common/ShadcnDynamicTable/dynamic-table';
import BannerPreview from './BannerPreview';
interface CookieConsentDomainDataProperties {
  auto_scan: boolean;
  banner_code: string;
  banner_configurations: string | null;
  banner_description: string;
  banner_published: boolean;
  banner_published_at: string | null;
  banner_title: string;
  compliance_policy_link: string;
  created_at: string;
  customer_id: number;
  domain_group_id: number;
  domain_registration_step: string;
  domain_url: string;
  id: number;
  legal_framework_id: number;
  name: string;
  next_scan_at: string;
  owner_email: string;
  owner_name: string;
  scan_frequency: string;
  total_cookies: number | null;
  updated_at: string;
  version: number;
  versioned_at: string;
}

const data = [
  {
    id: 32,
    customer_id: 496,
    domain_group_id: 161,
    legal_framework_id: 2,
    name: 'Cisco',
    domain_url: 'https://cisco.com/',
    owner_name: 'Henk',
    owner_email: '<EMAIL>',
    banner_title: 'CISCO PRIVATE LIMITED',
    banner_description: 'WE ARE CISCO ',
    auto_scan: true,
    total_cookies: 12,
    compliance_policy_link: 'https://www.cisco.com/',
    banner_code: 'HTML',
    banner_configurations: 'Unknown',
    version: 1,
    scan_frequency: 'daily',
    next_scan_at: '2024-09-24T05:53:35.372016',
    domain_registration_step: '3',
    banner_published: false,
    banner_published_at: '2024-09-20T06:29:13.147204+00:00',
    versioned_at: '2024-09-20T06:29:13.147204+00:00',
    created_at: '2024-09-20T06:29:13.148036+00:00',
    updated_at: '2024-09-23T05:53:35.370394+00:00',
  },
];

const CookieConsentDomain = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isDialogueOpen, setIsDialogueOpen] = useState(false);
  const [isDeleteDialogueOpen, setIsDeleteDialogueOpen] = useState(false);
  const [domainId, setDomainId] = useState<number>(0);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [count, setCount] = useState<number>(0);
  const [refetchList, setRefetchList] = useState<boolean>(false);

  //! STATES

  const [cookieConsentDomainData, setCookieConsentDomainData] = useState<
    CookieConsentDomainDataProperties[]
  >([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasScanStarted, setHasScanStarted] = useState(false);

  //! VARIABLES
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );

  const { mutate: handleScanNow, isPending } = useMutation({
    mutationFn: () => {
      return auto_scan_cookie(domainId);
    },
    onSuccess: () => {
      setHasScanStarted(true);
    },
    onError: (error) => {
      toast.dismiss();
      toast.error(t('Cookies.ScanFailed'));
    },
  });
  const columns: ColumnDef<CookieConsentDomainDataProperties>[] = [
    {
      accessorKey: 'domain_url',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Domain/Subdomain')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('domain_url')}</div>,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Domain Group')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('name')}</div>,
    },
    // {
    //   accessorKey: 'version',
    //   header: ({ column }) => {
    //     return (
    //       <Button
    //         variant="ghost"
    //         className="p-0"
    //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    //       >
    //         Action Version
    //         <ArrowUpDown className="ml-2 size-4" />
    //       </Button>
    //     );
    //   },
    //   cell: ({ row }) => <div className="">{`v${row.getValue('version')}`}</div>,
    // },
    {
      accessorKey: 'updated_at',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Last Scanned')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const updated_at: string = row.getValue('updated_at');
        return <div>{updated_at ? convertDateToHumanView(updated_at) : '-'}</div>;
      },
    },
    {
      accessorKey: 'scan_frequency',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Scan Frequency')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('scan_frequency')}</div>,
    },
    {
      accessorKey: 'next_scan_at',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Next Scan')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const next_scan_at: string = row.getValue('next_scan_at');
        return <div>{next_scan_at ? convertDateToHumanView(next_scan_at) : '-'}</div>;
      },
    },
    {
      accessorKey: 'version',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Version')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const version: string = row.getValue('version');
        return <div>{version ? version : '-'}</div>;
      },
    },
    {
      accessorKey: 'total_cookies',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Total Cookies')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const total_cookies: string = row.getValue('total_cookies');
        return <div>{total_cookies ? total_cookies : '-'}</div>;
      },
    },
    {
      accessorKey: 'banner_published',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Banner Published')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const banner_published: string = row.getValue('banner_published');
        return <div>{banner_published ? 'Published' : 'Draft'}</div>;
      },
    },
    // {
    //   accessorKey: 'banner_published_at',
    //   header: ({ column }) => {
    //     return (
    //       <Button
    //         variant="ghost"
    //         className="p-0"
    //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    //       >
    //         Consent Banner
    //         <ArrowUpDown className="ml-2 size-4" />
    //       </Button>
    //     );
    //   },
    //   cell: ({ row }) => {
    //     const banner_published_at: string = row.getValue('banner_published_at');
    //     return <div>{banner_published_at ? banner_published_at : '-'}</div>;
    //   },
    // },
    {
      accessorKey: 'compliance_policy_link',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Consent Policy')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('compliance_policy_link')}</div>,
    },
    {
      accessorKey: 'owner_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Cookies.Owner')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('owner_name')}</div>,
    },
    {
      accessorKey: 'download_link',
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="p-0">
            Actions
          </Button>
        );
      },
      cell: ({ row }) => {
        const totalCookies = row.getValue('total_cookies');
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={() => setDomainId(row?.original?.id)}
              >
                <EllipsisVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="align=start w-56">
              <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setDomainId(row?.original?.id);
                    downloadFileFromUrl(`${DOWNLOAD_COOKIE_REPORT}${row?.original?.id}`);
                  }}
                  disabled={!totalCookies}
                >
                  <span className="flex gap-2">
                    <span>
                      <Download className="size-4" />
                    </span>
                    <span>{t('Cookies.DownloadReport')}</span>
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    setDomainId(row?.original?.id);
                    dispatch(cookieConsentManagementActions.toInitialState());
                    dispatch(cookieConsentManagementActions.setDomainId(row?.original?.id));
                    e.preventDefault();
                    e.stopPropagation();
                    setIsDialogueOpen(true);
                  }}
                >
                  <span className="flex gap-2">
                    <span>
                      <Eye className="size-4" />
                    </span>
                    <span>{t('Cookies.View')}</span>
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  disabled={isPending}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setDomainId(row?.original?.id);
                    toast.success('Scan started successfully');
                    handleScanNow();
                  }}
                >
                  <span className="flex gap-2">
                    <span>
                      <FileScan className="size-4" />
                    </span>

                    <span>{t('Cookies.ScanNow')}</span>
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    setDomainId(row?.original?.id);
                    // dispatch(cookieConsentManagementActions.toInitialState());
                    // dispatch(cookieConsentManagementActions.setDomainId(row?.original?.id));
                    e.preventDefault();
                    e.stopPropagation();
                    setIsDeleteDialogueOpen(true);
                  }}
                >
                  <span className="flex gap-2">
                    <span>
                      {/* <img src={DeleteIcon} alt="delete" className='size-4' /> */}
                      <Trash className="size-4" />
                    </span>
                    <span>{t('Cookies.DeleteDomain')}</span>
                  </span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          // <div className="flex flex-row gap-2">
          //   <>
          //     <Button
          //       disabled={!totalCookies}
          //       onClick={(e) => {
          //         setDomainId(row?.original?.id);
          //         e.preventDefault();
          //         e.stopPropagation();
          //         downloadFileFromUrl(`${DOWNLOAD_COOKIE_REPORT}${row.original?.id}`);
          //       }}
          //     >
          //       <Download className="size-5" />
          //     </Button>

          //     <p>{t('Cookies.DownloadReport')}</p>
          //   </>
          //   <Button
          //     onClick={(e) => {
          //       setDomainId(row?.original?.id);
          //       dispatch(cookieConsentManagementActions.toInitialState());
          //       dispatch(cookieConsentManagementActions.setDomainId(row?.original?.id));
          //       e.preventDefault();
          //       e.stopPropagation();
          //       setIsDialogueOpen(true);
          //     }}
          //   >
          //     <Eye className="size-5" />
          //   </Button>
          //   <p>{t('Cookies.View')}</p>
          // </div>
        );
      },
    },
  ];

  const handlePageSizeChange = (value: number) => {
    setPageSize(value);
  };

  const handlePageNumberChange = (value: number) => {
    setPage(value);
  };

  //! Handler Functions

  // Debounced search function using lodash debounce
  const debouncedUpdateSearchTerm = useCallback(
    debounce((value: string) => {
      setDebouncedSearchTerm(value);
    }, 500), // delay in ms
    []
  );

  // Handle input change - update input value immediately and trigger debounced search
  const handleSearchInputChange = useCallback(
    (value: string) => {
      setInputValue(value); // Update input field immediately
      debouncedUpdateSearchTerm(value); // Update the debounced search term after 500ms
    },
    [debouncedUpdateSearchTerm]
  );

  const handleRowClick = (rowData: CookieConsentDomainDataProperties) => {
    dispatch(cookieConsentManagementActions?.toInitialState());
    dispatch(cookieConsentManagementActions.setDomainId(Number(rowData?.id)));
    dispatch(
      cookieConsentManagementActions.setCurrentStep(Number(rowData?.domain_registration_step))
    );
    dispatch(cookieConsentManagementActions.setCookieScanStatus(rowData?.auto_scan ? false : true));
    dispatch(cookieConsentManagementActions.setAPIMethod('put'));
    navigate(COOKIE_CONFIGURATION);
  };

  const handleCreateDomain = () => {
    dispatch(cookieConsentManagementActions.toInitialState());
    dispatch(cookieConsentManagementActions.setAPIMethod('post'));
    dispatch(cookieConsentManagementActions.setCookieScanStatus(true));
    navigate(COOKIE_CONFIGURATION);
  };

  const handleDeleteCookie = async () => {
    try {
      const response = await delete_cookie(domainId);

      if (response?.status_code === 200) {
        setIsDeleteDialogueOpen(false);
        setRefetchList(true);
        toast.dismiss();
        toast.success('Domain deleted successfully');
      }
    } catch (e) {
      console.log(e);
    }
  };

  //! USE EFFECTS

  // fetching cookie consent domain data
  useEffect(() => {
    const fetchCookieCategoryAndStatus = async () => {
      setIsLoading(true);
      try {
        const responseData = await get_cookie_consent_domain(
          customer_id,
          debouncedSearchTerm,
          page,
          pageSize
        );
        setCookieConsentDomainData(responseData?.result?.data);
        setCount(responseData?.result?.count);
      } catch (error) {
        console.error(error);
        setCookieConsentDomainData(data);
      } finally {
        setIsLoading(false);
      }
    };

    if (customer_id) {
      fetchCookieCategoryAndStatus();
    }
  }, [customer_id, debouncedSearchTerm, page, pageSize, refetchList]);
  const { t } = useTranslation();

  return (
    <>
      <ShadcnDialog
        open={isDeleteDialogueOpen}
        onOpenChange={setIsDeleteDialogueOpen}
        title="Delete Confirmation"
        description="Are you sure you want to delete this domain?"
        footer={
          <div className="flex w-full flex-row justify-end gap-2">
            <Button variant="outline" className="text-primary" onClick={handleDeleteCookie}>
              Yes
            </Button>
            <Button
              variant="default"
              className="bg-custom-primary text-primary-background hover:bg-custom-primary hover:text-primary-background"
              onClick={() => setIsDeleteDialogueOpen(false)}
            >
              No
            </Button>
          </div>
        }
      ></ShadcnDialog>

      <div className="size-full font-primary-text">
        <div className="flex flex-row flex-wrap items-center justify-between">
          <p className="text-xl font-semibold">{t('Cookies.Cookie Consent Domain')}</p>
          <div className="flex flex-row gap-2.5">
            <Button
              className="theme-hover-effect custom-primary bg-custom-primary p-2 text-primary-background hover:bg-custom-primary"
              onClick={() => handleCreateDomain()}
            >
              <img src={plusSign} alt="create icon" />
              <p>{t('Common.Create')}</p>
            </Button>
            <div className="relative">
              <Input
                placeholder={t('Common.Search')}
                secondIcon={search}
                value={inputValue}
                className={`h-[2.3rem] w-[250px] ${isLoading && inputValue ? 'pr-12' : ''}`}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                InputLabelProps={{ shrink: false }}
              />
              {/* Loading indicator in search input */}
              {isLoading && inputValue && (
                <div className="absolute right-10 top-1/2 -translate-y-1/2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div
          className="mt-2.5 w-full rounded-lg border border-primary-border bg-primary-background"
          style={{
            height: 'calc(100% - 50px)',
          }}
        >
          <div className="size-full p-4">
            <DynamicTable<CookieConsentDomainDataProperties>
              data={cookieConsentDomainData}
              columns={columns}
              loading={isLoading}
              enableSorting
              onRowClick={handleRowClick}
              searchTerm={debouncedSearchTerm}
              loadingMessage={debouncedSearchTerm ? 'Searching domains...' : 'Loading domains...'}
              loadingSubMessage={
                debouncedSearchTerm
                  ? `Looking for "${debouncedSearchTerm}"`
                  : 'Please wait while we fetch your data'
              }
              ServerSidePaginationDetails={{
                totalRecords: count,
                currentPage: page,
                currentSize: pageSize,
                handlePageSizeChange: handlePageSizeChange,
                handlePageNumberChange: handlePageNumberChange,
              }}
            />
          </div>
        </div>
        {isDialogueOpen && (
          <BannerPreview
            domainId={domainId}
            isDialogueOpen={isDialogueOpen}
            setIsDialogueOpen={setIsDialogueOpen}
          />
        )}
      </div>
    </>
  );
};

export default CookieConsentDomain;
