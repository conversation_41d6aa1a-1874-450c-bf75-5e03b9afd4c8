import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { z } from 'zod';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import { Calendar } from '../../../../../@/components/ui/Common/Elements/Calendar/Calendar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../../../@/components/ui/Common/Elements/Form/Form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../../../../@/components/ui/Common/Elements/Popover/Popover';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../../../../@/components/ui/Common/Elements/Select/Select';
import { cn } from '../../../../../@/lib/utils';
import {
  setSelectedEntityId as setEntity,
  setReloadTaskOverviewData,
} from '../../../../../redux/reducers/Assessment/AssessmentTaskOverviewSlice';
import { RootState } from '../../../../../redux/store';
import { convertString } from '../../../../common/CommonHelperFunctions';
import { Fetch_Task_Overview_Assign_Data } from '../../../../common/services/allPolicy';
import {
  createNewAssessment,
  fetchAssessments,
  fetchAssessmentTemplates,
  fetchDepartments,
  fetchProcesses,
} from '../../../../common/services/assessment';
import { StartNewAssessmentSchema } from './start-new-assessment-schema';

interface Assessments {
  id: number;
  assessment_name: string;
  key: string;
  type: string;
}

interface Department {
  id: number;
  name: string;
}

interface SubmitDataProperties {
  department_id?: number;
  tentative_date: string;
  assessment_id: number;
  process_id?: number;
  assigned_to: number;
  reviewer_id: number;
  entity_id: number;
  status: string;
  template_id?: number | null;
}

interface StartNewAssessmentFormProperties {
  setIsDialogBoxOpen: (value: boolean) => void;
  loading: boolean;
  setloading: (value: boolean) => void;
}

interface Template {
  id: number;
  type: string;
  region_id: number | null;
  assessment_name: string;
  key: string;
  status: boolean;
  customer_id: number | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  AssessmentTemplates: {
    id: number;
    name: string;
    key: string;
    published: string | null;
    url: string | null;
    assessment_id: number;
    customer_id: number;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  }[];
}

const StartNewAssessmentForm: React.FC<StartNewAssessmentFormProperties> = ({
  setIsDialogBoxOpen,
  loading,
  setloading,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  //! USE SELECTOR
  const { entities, reloadTaskOverviewData } = useSelector(
    (state: RootState) => state.assessmentTaskOverview
  );
  const loginData = useSelector((state: RootState) => state.auth.login.login_details);

  const assessmentForm = useForm({
    resolver: zodResolver(StartNewAssessmentSchema),
    defaultValues: {
      assessment_id: '-1',
      entity_id: '-1',
      assigned_to: '-1',
      reviewer_id: '-1',
      department_id: '-1',
      process_id: '-1',
      tentative_date: '',
      template_id: undefined,
    },
  });

  const [usersData, setUsersData] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [assessments, setAssessments] = useState<Assessments[]>([]);

  const [isUsersLoading, setIsUsersLoading] = useState(false);
  const [isProcessLoading, setIsProcessLoading] = useState(false);
  const [isAssessmentLoading, setIsAssessmentLoading] = useState(false);
  const [isDepartmentLoading, setIsDepartmentLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);

  const [processId, setProcessId] = useState<string>('-1');
  const [assigneeId, setAssigneeId] = useState<string>('-1');
  const [reviewerId, setReviewerId] = useState<string>('-1');
  const [departmentId, setDepartmentId] = useState<string>('-1');
  const [selectedEntityId, setSelectedEntityId] = useState<string>('-1');

  //! VARIABLE DECLARATIONS

  let customerId = 0;
  if (loginData !== null) {
    customerId = loginData.customer_id || -1;
  }

  const groupedAssessmentData = new Map<string, Assessments[]>();
  if (assessments.length > 0) {
    for (const assessment of assessments) {
      if (groupedAssessmentData?.has(assessment.type)) {
        groupedAssessmentData?.get(assessment.type)?.push(assessment);
      } else {
        groupedAssessmentData?.set(assessment.type, [assessment]);
      }
    }
  }

  //! HANDLER Function

  const handleSubmit = async (submittedData: z.infer<typeof StartNewAssessmentSchema>) => {
    setloading(true);
    let finalData: SubmitDataProperties = {
      assessment_id: Number(submittedData?.assessment_id),
      assigned_to: Number(submittedData?.assigned_to),
      reviewer_id: Number(submittedData?.reviewer_id),
      tentative_date: submittedData?.tentative_date,
      entity_id: Number(submittedData?.entity_id),
      status: 'Yet to Start',
      template_id: submittedData.template_id ? Number(submittedData.template_id) : null,
    };

    if (departmentId !== '-1') {
      finalData = {
        ...finalData,
        ['department_id']: Number(submittedData?.department_id),
      };
    }

    if (processId !== '-1') {
      finalData = {
        ...finalData,
        ['process_id']: Number(submittedData?.process_id),
      };
    }

    try {
      const response = await createNewAssessment(finalData);
      if (response) {
        setSelectedEntityId('-1');
        setDepartmentId('-1');
        setProcessId('-1');
        setReviewerId('-1');
        setAssigneeId('-1');
        dispatch(setEntity(Number(selectedEntityId)));
        dispatch(setReloadTaskOverviewData(!reloadTaskOverviewData));
      }
    } catch (error) {
      console.error('Failed to create new assessment:', error);
    } finally {
      setloading(false);
      setIsDialogBoxOpen(false);
    }

    console.log('finalData', finalData);
  };

  //! USE EFFECT

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsAssessmentLoading(true);
        const responseData = await fetchAssessments();
        console.log(responseData);
        setAssessments(responseData);
      } catch (error) {
        if (error instanceof Error) {
          console.log('Fetching user data failed!', error.message);
        } else {
          console.log('An unknown error occurred:', error);
        }
      } finally {
        setIsAssessmentLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsUsersLoading(true);
        const response = await Fetch_Task_Overview_Assign_Data(
          '',
          customerId,
          Number(selectedEntityId)
        );
        // if (!response.ok) {
        //   throw new Error('Failed to fetch data.');
        // }
        const responseData = response.data;
        setUsersData(responseData?.result?.rows);
      } catch (error) {
        if (error instanceof Error) {
          console.log('Fetching user data failed!');
        } else {
          console.log('An unknown error occurred:', error);
        }
      } finally {
        setIsUsersLoading(false);
      }
    };

    if (selectedEntityId !== '-1' && customerId !== -1) fetchData();
  }, [selectedEntityId, customerId]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsDepartmentLoading(true);
        const response = await fetchDepartments(Number(selectedEntityId));
        if (response.status !== 200) {
          throw new Error('Failed to fetch data.');
        }

        console.log(response);
        setDepartments(response?.data?.result?.rows);
      } catch (error) {
        if (error instanceof Error) {
          console.log('Fetching department data failed!');
        } else {
          console.log('An unknown error occurred:', error);
        }
      } finally {
        setIsDepartmentLoading(false);
      }
    };

    if (selectedEntityId !== '-1') fetchData();
  }, [selectedEntityId]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsProcessLoading(true);
        const response = await fetchProcesses(Number(departmentId));
        if (response.status !== 200) {
          throw new Error('Failed to fetch data.');
        }

        setProcesses(response?.data?.result?.rows);
      } catch (error) {
        if (error instanceof Error) {
          console.log('Fetching process data failed!');
        } else {
          console.log('An unknown error occurred:', error);
        }
      } finally {
        setIsProcessLoading(false);
      }
    };

    if (departmentId !== '-1') fetchData();
  }, [departmentId]);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await fetchAssessmentTemplates(searchTerm);
        setTemplates(response.data.result.rows);
      } catch (error) {
        console.error('Failed to fetch templates:', error);
      }
    };

    fetchTemplates();
  }, []);

  console.log('assessmentForm', assessmentForm.getValues());
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return (
    <div className="flex flex-col gap-4 font-primary-text">
      <Form {...assessmentForm}>
        <form
          onSubmit={assessmentForm.handleSubmit(handleSubmit)}
          id="newAssessmentForm"
          className="flex flex-col gap-4"
        >
          <FormField
            control={assessmentForm.control}
            name="assessment_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <span>Assessment Name</span>
                  <span className="text-red-500">*</span>
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    const filtered = templates.filter((template) => template.id === Number(value));
                    setFilteredTemplates(filtered);
                  }}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t('Common.Select')} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="-1" disabled>
                      {t('Common.Select')}
                    </SelectItem>
                    {Array?.from(groupedAssessmentData?.entries())?.map(([type, assessments]) => (
                      <SelectGroup key={type}>
                        <SelectLabel>{convertString(type)}</SelectLabel>
                        {assessments.map((assessment) => (
                          <SelectItem key={assessment.id} value={assessment.id.toString()}>
                            {assessment.assessment_name}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={assessmentForm.control}
            name="template_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <span>Template</span>
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value === 'null' ? undefined : value);
                  }}
                  value={field.value || 'null'}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a template" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="null">Default template</SelectItem>
                    {filteredTemplates.map((assessment) =>
                      assessment.AssessmentTemplates.map((template) => (
                        <SelectItem key={template.id} value={template.id.toString()}>
                          {template.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={assessmentForm.control}
            name="entity_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <span>Entity</span>
                  <span className="text-red-500">*</span>
                </FormLabel>
                <Select
                  value={selectedEntityId}
                  defaultValue={'-1'}
                  onValueChange={(value) => {
                    setSelectedEntityId(value);
                    field.onChange(value);
                    setDepartmentId('-1');
                    setReviewerId('-1');
                    setAssigneeId('-1');
                    setProcessId('-1');
                  }}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t('Common.Select')} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="-1" disabled>
                      {t('Common.Select')}
                    </SelectItem>
                    {entities?.map((entity) => (
                      <SelectItem key={entity.id} value={entity.id.toString()} id={entity.name}>
                        {entity.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {selectedEntityId === '-1' ? (
            <></>
          ) : (
            <FormField
              control={assessmentForm.control}
              name="department_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department</FormLabel>
                  <Select
                    value={departmentId}
                    defaultValue={'-1'}
                    onValueChange={(value) => {
                      setDepartmentId(value);
                      field.onChange(value);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('Common.Select')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="-1" disabled>
                        {t('Common.Select')}
                      </SelectItem>
                      {departments.length > 0 ? (
                        departments?.map((item) => (
                          <SelectItem key={item.id} value={item.id.toString()} id={item.name}>
                            {item.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="-2" disabled>
                          No Data
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {departmentId === '-1' ? (
            <></>
          ) : (
            <FormField
              control={assessmentForm.control}
              name="process_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Process</FormLabel>
                  <Select
                    value={processId}
                    defaultValue={'-1'}
                    onValueChange={(value) => {
                      setProcessId(value);
                      field.onChange(value);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('Common.Select')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="-1" disabled>
                        {t('Common.Select')}
                      </SelectItem>
                      {processes.length > 0 ? (
                        processes?.map((item: Department) => (
                          <SelectItem key={item.id} value={item.id.toString()} id={item.name}>
                            {item.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="-2" disabled>
                          No Data
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {selectedEntityId === '-1' ? (
            <></>
          ) : (
            <FormField
              control={assessmentForm.control}
              name="assigned_to"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span>Assignee</span>
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    value={assigneeId}
                    defaultValue={'-1'}
                    onValueChange={(value) => {
                      setAssigneeId(value);
                      field.onChange(value);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('Common.Select')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="-1" disabled>
                        {t('Common.Select')}
                      </SelectItem>
                      {usersData.length > 0 ? (
                        usersData?.map((item: any) => (
                          <SelectItem key={item.id} value={item.id.toString()}>
                            {/* <AvatarFrame
                          value={`${item?.firstName} ${item?.lastName}`}
                          getInitials={getInitialsByName}
                        /> */}
                            {`${item?.firstName} ${item?.lastName}`}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="-2" disabled>
                          No Data
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {selectedEntityId === '-1' ? (
            <></>
          ) : (
            <FormField
              control={assessmentForm.control}
              name="reviewer_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span>Reviewer</span>
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    value={reviewerId}
                    defaultValue={'-1'}
                    onValueChange={(value) => {
                      setReviewerId(value);
                      field.onChange(value);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('Common.Select')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="-1" disabled>
                        {t('Common.Select')}
                      </SelectItem>
                      {usersData.length > 0 ? (
                        usersData?.map((item: any) => (
                          <SelectItem key={item.id} value={item.id.toString()}>
                            {`${item?.firstName} ${item?.lastName}`}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="-2" disabled>
                          No Data
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          <FormField
            control={assessmentForm.control}
            name="tentative_date"
            render={({ field }) => {
              const selectedDate = field.value ? new Date(field.value) : undefined;

              return (
                <FormItem>
                  <FormLabel>
                    <span>Tentative Completion Date</span>
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={'outline'}
                          className={cn(
                            'h-12 w-full pl-3 text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {selectedDate ? format(selectedDate, 'PPP') : <span>Pick a date</span>}
                          <CalendarIcon className="ml-auto size-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={(date) => field.onChange(date ? date.toISOString() : '')}
                        disabled={(date) => date < today}
                        // disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </form>
      </Form>
      {(isUsersLoading || isProcessLoading || isAssessmentLoading || isDepartmentLoading) && (
        <div className="flex items-center justify-center">
          <svg
            aria-hidden="true"
            className="size-6 animate-spin fill-blue-600 text-gray-200 dark:text-gray-600"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default StartNewAssessmentForm;
