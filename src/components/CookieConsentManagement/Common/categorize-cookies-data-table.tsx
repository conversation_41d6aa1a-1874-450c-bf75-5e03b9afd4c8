import { zodResolver } from '@hookform/resolvers/zod';
import {
  ColumnDef,
  // ColumnFiltersState,
  SortingState,
  // VisibilityState,
  flexRender,
  getCoreRowModel,
  // getFilteredRowModel,
  // getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { t } from 'i18next';
import { ArrowUpDown, PencilLineIcon } from 'lucide-react';
import * as React from 'react';
import { z } from 'zod';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import { Label } from '../../../@/components/ui/Common/Elements/Label/Label';
import { SkeletonCard } from '../../../@/components/ui/Common/Elements/Skeleton/Skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../@/components/ui/Common/Table/Table';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../@/components/ui/dialog';
import { Input } from '../../../@/components/ui/Input';
import { Switch } from '../../../@/components/ui/switch';
import { Textarea } from '../../../@/components/ui/textarea';

import axios from 'axios';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useSelector } from 'react-redux';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../../@/components/ui/tooltip';
import pageIcon from '../../../assets/page-svgrepo-com.svg';
import { RootState } from '../../../redux/store';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import { put_cookie } from '../../common/services/cookie-consent-management';

interface TableHeading {
  id: string;
  label: string;
  sort: boolean;
  width: string;
}

interface CookieCategoryDataProperties {
  default_opt_out: boolean;
  description: string;
  domain_id?: number;
  id?: number;
  is_necessary: boolean;
  is_unclassified: boolean;
  name: string;
  action?: boolean;
}

interface CookieServiceDataProperties {
  id?: number;
  service_Name: string;
  category: string;
  // cookie_key: string;
  // customer_id: number;
  // domain_id: number;
  // vendor_name: string;
  // path: string;
  // cookie_type: string;
  // expiration: string; // Alternatively, you can use Date if you plan to parse it
  description: string;
  link: string;
  cookie_category_id?: string;
  // is_necessary_cookie?: boolean;
  // status?: boolean;
  // show_status?: boolean;
  // status_changed_at: string;
  // show_status_changed_at: string;
  // domain_url: string;
  // created_at: string;
  // updated_at: string;
  action?: string;
}

export type CategorizeCookieTableData = {
  loading: boolean;
  reloadData: boolean;
  tableHeadings?: TableHeading[];
  data: ResponseDataProperties[];
  setReloadData: React.Dispatch<React.SetStateAction<boolean>>;
  cookieCategoryList: CookieCategoryDataProperties[];
  cookieServiceList: CookieServiceDataProperties[];
  isNecessaryCookie: boolean;
  setIsNecessaryCookie: (value: boolean) => void;
  isHttpOnlyCookie: boolean;
  isSecureCookie: boolean;
  setIsSecureCookie: (value: boolean) => void;
  setIsHTTPOnlyCookie: (value: boolean) => void;
};

export type RowData = {
  status: string;
  domain: string;
  domain_group: string;
  action_version: string;
  last_scanned: string;
  total_cookies: string;
};

interface ResponseDataProperties {
  id: number;
  service: string;
  vendor_name: string;
  category: string;
  expiration: string; // Alternatively, you can use Date if you plan to parse it
  cookie_key: string;
  path: string;
  cookie_type: string;
  description: string;
  vendor_privacy_policy_link: string;
  secure: boolean;
  status_changed_at: string;
  category_id: number;
  show_status_changed_at: string;
  created_at: string;
  updated_at: string;
  httpOnly: boolean;
  status?: boolean;
  domain_id: number;
  show_status?: boolean;
  subpages: { id: number; name: string }[] | [];
  is_necessary_cookie?: boolean;
  observation: string;
  //   customer_id: number;
  cookie_service_id: number;
  action?: string;
}

/// Utility function to convert snake_case to Human Readable format
const toHumanReadable = (value: string): string => {
  return value
    .replaceAll('_', ' ') // Replace underscores with spaces
    .replaceAll(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word
};

const convertToDateInputFormat = (value: string | null | undefined): string => {
  if (!value || typeof value !== 'string') return '';
  const parsed = new Date(value);
  if (isNaN(parsed.getTime())) return '';
  return parsed.toISOString().split('T')[0];
};

// Function to generate dynamic columns

const CategorizeCookiesDataTable: React.FC<CategorizeCookieTableData> = ({
  data,
  // tableHeadings,
  cookieCategoryList,
  cookieServiceList,
  loading,
  reloadData,
  isNecessaryCookie,
  isHttpOnlyCookie,
  isSecureCookie,
  setIsSecureCookie,
  setIsNecessaryCookie,
  setIsHTTPOnlyCookie,
  setReloadData,
}) => {
  const [statusValue, setStatusValue] = React.useState(false);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [currentCookieId, setCurrentCookieId] = React.useState(0);
  const [editDailog, setEditDailog] = React.useState<boolean>(false);
  const [showStatusValue, setShowStatusValue] = React.useState(false);
  const [addCategory, setAddCategory] = React.useState<boolean>(false);
  const [currentCookieServiceId, setCurrentCookieServiceId] = React.useState(0);
  const [currentCookieCategoryId, setCurrentCookieCategoryId] = React.useState(0);

  const today = new Date().toISOString().split('T')[0];

  // Store original toggle values for reset on cancel
  const [originalToggleValues, setOriginalToggleValues] = React.useState({
    isNecessaryCookie: false,
    isHttpOnlyCookie: false,
    isSecureCookie: false,
  });
  const [tableData, setTableData] = React.useState({
    cookie_key: '',
    id: 0,
    path: '',
    service: '',
    category: '',
    expiration: '',
    cookie_type: '',
    vendor_name: '',
    description: '',
    vendor_privacy_policy_link: '',
    secure: false,
    httpOnly: false,
    category_id: 0,
    cookie_service_id: 0,
    is_necessary_cookie: false,
  });

  const domain_id: number = useSelector(
    (state: RootState) =>
      state?.cookieConsentManagement?.CookieConsentDomain?.cookieConfiguration?.domain_id
  );
  const loginData = useSelector((state: RootState) => state.auth.login.login_details);

  let customer_id = 0;

  if (loginData !== null) {
    customer_id = loginData.customer_id ?? 0;
  }

  const cookieSchema = z.object({
    cookie_key: z.string().min(1, 'Cookie Key is required'),
    description: z.string().optional(),
    path: z.string().min(1, 'Path is required'),
    cookie_type: z.string().min(1, 'Cookie Type is required'),
    expiration: z.string().optional(),
    vendor_name: z.string().optional(),
    cookie_service_id: z.string().min(1, 'Cookie Service is required'),
    category_id: z.string().min(1, 'Cookie Category is required'),
    is_necessary_cookie: z.boolean().optional(),
    status: z.boolean().optional(),
    show_status: z.boolean().optional(),
    httpOnly: z.boolean().optional(),
    secure: z.boolean().optional(),
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(cookieSchema),
  });

  const onSubmit = async (data: any) => {
    if (customer_id !== 0 && currentCookieServiceId !== 0 && currentCookieCategoryId !== 0) {
      try {
        toast.loading(t('CommonErrorMessages.ProcessingEllipsis'));

        const responseData = await put_cookie({
          domain_cookie_id: currentCookieId,
          customer_id,
          domain_id,
          category_id: Number(data.category_id),
          cookie_key: data.cookie_key,
          cookie_service_id: Number(data?.cookie_service_id),
          vendor_name: data.vendor_name,
          path: data.path,
          cookie_type: data.cookie_type,
          expiration: data.expiration,
          description: data.description,
          is_necessary_cookie: data.is_necessary_cookie,
          httpOnly: data.httpOnly,
          secure: data.secure,
          // status: data.status,
          // show_status: data.show_status,
        });

        if (responseData?.status_code === 200) {
          setEditDailog(false);
          setAddCategory(false);

          reset({
            cookie_key: '',
            id: 0,
            path: '',
            service: '',
            category: '',
            expiration: '',
            cookie_type: '',
            vendor_name: '',
            description: '',
            vendor_privacy_policy_link: '',
            secure: false,
            httpOnly: false,
            category_id: '0',
            cookie_service_id: '0',
            is_necessary_cookie: false,
          });
          setReloadData((prev: boolean) => !prev);

          toast.dismiss();
          toast.success(responseData?.message);
        }
      } catch (error) {
        // eslint-disable-next-line unicorn/prefer-ternary
        if (axios.isAxiosError(error)) {
          // Axios specific error handling
          toast.dismiss(); // Clear any existing toasts
          // const status = error?.response?.data?.status_code;
          // const statusText = error?.response?.data?.message;
          const errorMessage = error?.response?.data?.result?.error || error.message;
          toast.error(`${errorMessage}`);
          console.error('Axios Error:', error);
        } else {
          // Generic error handling
          toast.dismiss();
          toast.error(t('FrontEndErrorMessage.ApiErrors.UnexpectedErrorOccurred'));
          console.error('Unexpected Error:', error);
        }
      }
    }
  };

  const handleAction = (data: any) => {
    setEditDailog(true);

    // Store original toggle values for reset on cancel
    const originalValues = {
      isNecessaryCookie: data.is_necessary_cookie ?? false,
      isHttpOnlyCookie: data.httpOnly ?? false,
      isSecureCookie: data.secure ?? false,
    };
    setOriginalToggleValues(originalValues);

    setTableData({
      cookie_key: data.cookie_key ?? '',
      id: data.id ?? 0,
      path: data.path ?? '',
      service: data.service ?? '',
      category: data.category ?? '',
      expiration: data.expiration,
      cookie_type: data.cookie_type ?? '',
      vendor_name: data.vendor_name ?? '',
      description: data.description ?? '',
      vendor_privacy_policy_link: data.vendor_privacy_policy_link ?? '',
      secure: data.secure ?? false,
      httpOnly: data.httpOnly ?? false,
      category_id: data.category_id?.toString() ?? '0',
      cookie_service_id: data.cookie_service_id?.toString() ?? '0',
      is_necessary_cookie: data.is_necessary_cookie ?? false,
    });

    setCurrentCookieId(data?.id ?? 0);
    setCurrentCookieCategoryId(data?.category_id ?? 0);
    setCurrentCookieServiceId(data?.cookie_service_id ?? 0);
  };

  const generateColumns = (data: ResponseDataProperties[]): ColumnDef<ResponseDataProperties>[] => {
    if (data.length === 0) return [];

    const keys = Object.keys(data[0]) as (keyof ResponseDataProperties)[]; // Ensure keys match ResponseData keys

    const filteredKeys = keys?.filter(
      (item) =>
        item !== 'cookie_service_id' &&
        item !== 'id' &&
        item !== 'category_id' &&
        item !== 'vendor_privacy_policy_link' &&
        item !== 'domain_id'
    );

    // Dynamically mapped columns from data + Country
    const dynamicColumns: ColumnDef<ResponseDataProperties>[] = filteredKeys.map((key) => ({
      accessorKey: key,
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {key === 'cookie_type' ? '1st / 3rd Party' : toHumanReadable(key as string)}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        console.log('Key', key);
        return key === 'action' ? (
          <PencilLineIcon
            className="size-4 cursor-pointer"
            onClick={() => handleAction(row.original)}
          />
        ) : key === 'is_necessary_cookie' ||
          key === 'status' ||
          key === 'show_status' ||
          key === 'secure' ||
          key === 'httpOnly' ? (
          row.getValue(key) ? (
            <span style={{ color: '#059669', fontWeight: '600' }}>✓</span>
          ) : (
            <span style={{ color: '#d93434', fontWeight: '600' }}>✕</span>
          )
        ) : key === 'created_at' ||
          key === 'updated_at' ||
          key === 'show_status_changed_at' ||
          key === 'status_changed_at' ? (
          <div className="w-fit">
            {row.getValue(key) ? convertDateToHumanView(row.getValue(key)) : '-'}
          </div>
        ) : key === 'subpages' ? (
          <div className="flex w-full flex-row gap-2">
            {Array.isArray(row.getValue(key)) &&
            (row.getValue(key) as { id: number; name: string }[]).length > 0 ? (
              <>
                {(row.getValue(key) as { id: number; name: string }[])
                  .slice(0, 2)
                  .map((subpage: { id: number; name: string }) => (
                    <div key={subpage?.id} className="w-fit hover:cursor-pointer">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <img src={pageIcon} alt="page" className="w-6 object-contain" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-sm text-white">{subpage?.name}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  ))}

                {(row.getValue(key) as { id: number; name: string }[]).length > 2 && (
                  // <div className="w-fit hover:cursor-pointer">
                  //   <TooltipProvider>
                  //     <Tooltip>
                  //       <TooltipTrigger asChild>
                  //         <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 text-xs font-medium">
                  //           +{row.getValue(key).length - 2}
                  //         </div>
                  //       </TooltipTrigger>
                  //       <TooltipContent className="max-w-[200px]">
                  //         <p className="text-sm text-white">
                  //           {row
                  //             .getValue(key)
                  //             .slice(2)
                  //             .map((subpage: { name: string }) => subpage.name)
                  //             .join(', ')}
                  //         </p>
                  //       </TooltipContent>
                  //     </Tooltip>
                  //   </TooltipProvider>
                  // </div>
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 text-xs font-medium">
                    +{(row.getValue(key) as { id: number; name: string }[]).length - 2}
                  </div>
                )}
              </>
            ) : (
              '-'
            )}
          </div>
        ) : (
          <div>
            {row.getValue(key)
              ? row.getValue(key) === 'third-party'
                ? '3rd Party'
                : row.getValue(key) === 'first-party'
                  ? '1st Party'
                  : row.getValue(key)
              : '-'}
          </div>
        );
      },
    }));

    // Append the static Country column to the dynamic ones
    return [...dynamicColumns];
  };

  const columns: ColumnDef<ResponseDataProperties>[] = generateColumns(data);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    // onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    // getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // getFilteredRowModel: getFilteredRowModel(),
    // onColumnVisibilityChange: setColumnVisibility,
    // onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      // columnFilters,
      // columnVisibility,
      // rowSelection,
    },
  });

  React.useEffect(() => {
    reset({
      ...tableData,
      expiration: convertToDateInputFormat(tableData.expiration),
      cookie_service_id: tableData.cookie_service_id?.toString(), // match zod type
      category_id: tableData.category_id?.toString(),
    });
    setIsNecessaryCookie(tableData.is_necessary_cookie);
    setIsHTTPOnlyCookie(tableData.httpOnly);
    setIsSecureCookie(tableData.secure);
    // setShowStatusValue(tableData.show_status);
    // setStatusValue(tableData.status);
    // setCurrentCookieCategoryId(tableData.);
    // setCurrentCookieServiceId(tableData.cookie_service_id);
  }, [tableData, reset, setIsNecessaryCookie, setIsHTTPOnlyCookie, setIsSecureCookie]);

  // const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
  //   []
  // );
  // const [columnVisibility, setColumnVisibility] =
  //   React.useState<VisibilityState>({});
  // const [rowSelection, setRowSelection] = React.useState({});

  return (
    <>
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? undefined
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && 'selected'}
                // className={`${row?.getValue('Secure') && row?.getValue('HttpOnly') ? 'bg-green-500' : 'bg-red-100'}`}
              >
                {row.getVisibleCells().map((cell) => {
                  // const fill =
                  //   row?.original?.status === 'active'
                  //     ? '#00CBA0'
                  //     : (row?.original?.status === 'inactive'
                  //       ? '#FF9950'
                  //       : '#FA1464');

                  return (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))
          ) : loading ? (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
              </TableCell>
            </TableRow>
          ) : (
            !table.getRowModel().rows?.length && (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No result.
                </TableCell>
              </TableRow>
            )
          )}
        </TableBody>
      </Table>

      <Dialog
        open={editDailog}
        onOpenChange={(open) => {
          setEditDailog(open);
          if (!open) {
            // Reset toggle states to original values when dialog closes without saving
            setIsNecessaryCookie(originalToggleValues.isNecessaryCookie);
            setIsHTTPOnlyCookie(originalToggleValues.isHttpOnlyCookie);
            setIsSecureCookie(originalToggleValues.isSecureCookie);
          }
        }}
      >
        <DialogContent className="h-auto max-h-[80vh] overflow-y-auto sm:max-w-[50%]">
          <DialogHeader>
            <DialogTitle className="text-base font-semibold text-[#64748B]">
              Edit Cookie
            </DialogTitle>
          </DialogHeader>

          {/* Cookie Key */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="cookie_key" className="font-medium text-[#09090B]">
              Cookie Key
              <span className="text-red-500">*</span>
            </Label>
            <Input id="cookie_key" disabled placeholder="Write" {...register('cookie_key')} />
            {errors.cookie_key && <p className="text-[red]">This field is required</p>}
          </div>

          {/* Description */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="description" className="font-medium text-[#09090B]">
              Description
            </Label>
            <Textarea id="description" placeholder="Write" {...register('description')} />
          </div>

          {/* Path */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="path" className="font-medium text-[#09090B]">
              Path
              <span className="text-red-500">*</span>
            </Label>
            <Input id="path" placeholder="Write" {...register('path')} />
            {errors.path && <p className="text-[red]">This field is required</p>}
          </div>

          {/* Cookie Category */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="category_id" className="font-medium text-[#09090B]">
              Cookie Category
              <span className="text-red-500">*</span>
            </Label>
            <select
              id="category_id"
              {...register('category_id', { required: 'This field is required' })}
              className="rounded border-[#64748B] p-3 font-[Poppins] text-sm text-[#64748B] hover:cursor-pointer"
            >
              <option value="" disabled>
                Select Cookie Category
              </option>
              {cookieCategoryList?.map((category) => (
                <option value={category?.id?.toString()} key={category?.id}>
                  {category?.name}
                </option>
              ))}
            </select>
            {errors.category_id && <p className="text-red-500">This field is required</p>}
          </div>

          {/* Cookie Service */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="cookie_service_id" className="font-medium text-[#09090B]">
              Cookie Service
              <span className="text-red-500">*</span>
            </Label>
            <select
              id="cookie_service_id"
              {...register('cookie_service_id', { required: 'This field is required' })}
              className="rounded border-[#64748B] p-3 text-sm text-[#64748B] hover:cursor-pointer"
            >
              <option value="" disabled>
                Select Cookie Service
              </option>
              {cookieServiceList?.map((service) => {
                if (Number(service?.cookie_category_id) === Number(watch('category_id'))) {
                  return (
                    <option value={service?.id?.toString()} key={service?.id}>
                      {service?.service_Name}
                    </option>
                  );
                }
              })}
            </select>
            {errors.cookie_service_id && <p className="text-red-500">This field is required</p>}
          </div>

          {/* Cookie Type */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="cookie_type" className="font-medium text-[#09090B]">
              Cookie Type
              <span className="text-red-500">*</span>
            </Label>
            <select
              id="cookie_type"
              {...register('cookie_type', { required: 'This field is required' })}
              className="rounded border-[#64748B] p-3 text-sm text-[#64748B]"
            >
              <option value="">Select your Party</option>
              <option value="first-party">First Party</option>
              <option value="third-party">Third Party</option>
            </select>
            {errors.cookie_type && <p className="text-red-500">This field is required</p>}
          </div>

          {/* Expiration */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="expiration" className="font-medium text-[#09090B]">
              Expiration
            </Label>
            <Input
              type="date"
              min={today}
              id="expiration"
              placeholder="Write"
              {...register('expiration')}
            />
          </div>
          {/* Vendor Name */}
          <div className="flex flex-col gap-2">
            <Label htmlFor="vendor_name" className="font-medium text-[#09090B]">
              Vendor Name
            </Label>
            <Input id="vendor_name" placeholder="Write" {...register('vendor_name')} />
          </div>

          <div className="mt-5 flex w-full flex-row flex-wrap items-center gap-10 whitespace-nowrap text-sm font-medium">
            {/* Necessary */}
            <div className="flex w-full flex-row flex-wrap gap-2">
              <div className="flex flex-row items-center gap-2">
                <Label>Necessary</Label>
                <Switch
                  checked={isNecessaryCookie}
                  onCheckedChange={(checked) => {
                    setIsNecessaryCookie(checked);
                    setValue('is_necessary_cookie', checked);
                  }}
                />
              </div>
              <div className="flex flex-row items-center gap-2">
                <Label>HTTP Only</Label>
                <Switch
                  checked={isHttpOnlyCookie}
                  onCheckedChange={(checked) => {
                    setIsHTTPOnlyCookie(checked);
                    setValue('httpOnly', checked);
                  }}
                />
              </div>
              <div className="flex flex-row items-center gap-2">
                <Label>Secure</Label>
                <Switch
                  checked={isSecureCookie}
                  onCheckedChange={(checked) => {
                    setIsSecureCookie(checked);
                    setValue('secure', checked);
                  }}
                />
              </div>
            </div>

            {/* Status */}
            {/* <div className="flex flex-col gap-2">
          <div className="flex flex-row items-center gap-2">
            <Label>Status</Label>
            <Switch
              checked={statusValue}
              onCheckedChange={(checked) => {
                setStatusValue(checked);
                setValue('status', checked); // Set boolean value
              }}
            />
          </div>
        </div> */}

            {/* Show Status */}
            {/* <div className="flex flex-col gap-2">
          <div className="flex flex-row items-center gap-2">
            <Label>Show Status</Label>
            <Switch
              checked={showStatusValue}
              onCheckedChange={(checked) => {
                setShowStatusValue(checked);
                setValue('show_status', checked); // Set boolean value
              }}
            />
          </div>
        </div> */}
          </div>

          <DialogFooter className="mt-2">
            <Button
              size="sm"
              variant="secondary"
              type="button"
              onClick={() => {
                setEditDailog(false);
                // Reset toggle states to original values when canceling
                setIsNecessaryCookie(originalToggleValues.isNecessaryCookie);
                setIsHTTPOnlyCookie(originalToggleValues.isHttpOnlyCookie);
                setIsSecureCookie(originalToggleValues.isSecureCookie);
              }}
            >
              {t('Common.Cancel')}
            </Button>
            <Button
              size="sm"
              type="submit"
              onClick={handleSubmit(onSubmit)}
              className="bg-custom-primary text-white hover:bg-custom-primary"
            >
              {t('Common.Save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Category Dailog */}

      <Dialog open={addCategory} onOpenChange={setAddCategory}>
        <DialogContent className="h-auto overflow-auto sm:max-w-[30%]">
          <DialogHeader>
            <DialogTitle className="text-base font-semibold text-[#64748B]">
              Add Category
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-2">
            <Label htmlFor="name" className="font-medium text-[#09090B]">
              Category
            </Label>
            {/* <Input id="key" placeholder="Write" />
             */}
            <select className="flex h-10 rounded-md border border-solid border-[#CACACA] bg-white p-1 px-2">
              {['Uncategories', 'Essential']?.map((item, index) => (
                <option key={index}>{item}</option>
              ))}
            </select>
          </div>
          <DialogFooter className="mt-2">
            <Button
              size="sm"
              type="submit"
              className="w-full bg-custom-primary text-white hover:bg-custom-primary"
            >
              {t('Common.Add')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CategorizeCookiesDataTable;
