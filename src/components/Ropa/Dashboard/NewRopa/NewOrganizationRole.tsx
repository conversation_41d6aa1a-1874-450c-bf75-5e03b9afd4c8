import { useQuery } from '@tanstack/react-query';
import { Building2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Badge } from '../../../../@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/card';
import { Progress } from '../../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import { Fetch_Ropa_Dashboard_Org_Role } from '../../../common/services/ropaDashboard';

interface OrgRoleData {
  Controller: number;
  'Joint-Controller': number;
  Processor: number;
  'Sub-Processor': number;
  total_count: number;
}

export default function NewOrganizationalRole() {
  const { t } = useTranslation();

  const {
    data: orgRoleData,
    isLoading: loading,
    error,
  } = useQuery<OrgRoleData>({
    queryKey: ['ropaDashboardOrgRole'],
    queryFn: async () => {
      const response = await Fetch_Ropa_Dashboard_Org_Role();
      if (response?.data?.result) {
        return response.data.result;
      }
      throw new Error('Failed to fetch organization role data');
    },
  });

  if (error) {
    console.error('Error fetching organization role data:', error);
  }

  const getOrgRoleDistribution = () => {
    if (!orgRoleData) return [];

    const {
      Controller,
      'Joint-Controller': JointController,
      Processor,
      'Sub-Processor': SubProcessor,
      total_count,
    } = orgRoleData;

    return [
      {
        level: t('Ropa.Dashboard.ROPAV3.Controller'),
        count: Controller,
        percentage: total_count > 0 ? Math.round((Controller / total_count) * 100) : 0,
        color: 'outline',
      },
      {
        level: t('Ropa.Dashboard.ROPAV3.Joint-Controller'),
        count: JointController,
        percentage: total_count > 0 ? Math.round((JointController / total_count) * 100) : 0,
        color: 'outline',
      },
      {
        level: t('Ropa.Dashboard.ROPAV3.Processor'),
        count: Processor,
        percentage: total_count > 0 ? Math.round((Processor / total_count) * 100) : 0,
        color: 'outline',
      },
      {
        level: t('Ropa.Dashboard.ROPAV3.Sub-Processor'),
        count: SubProcessor,
        percentage: total_count > 0 ? Math.round((SubProcessor / total_count) * 100) : 0,
        color: 'outline',
      },
    ];
  };

  const orgRoleDistribution = getOrgRoleDistribution();

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1">
        <Card className="flex h-[450px] flex-col overflow-auto shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-1">
              <Building2 className="h-5 w-5 text-blue-500" />
              {t('Ropa.Dashboard.ROPAV3.Processing Activities by Organization Role')}{' '}
            </CardTitle>
            <CardDescription>
              {t('Ropa.Dashboard.ROPAV3.Total count')} {orgRoleData?.total_count}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-1 flex-col space-y-4 p-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-sm text-gray-500">
                  {t('Ropa.Dashboard.ROPAV3.Loading organization role data...')}
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-sm text-red-500">
                  {t('Ropa.Dashboard.ROPAV3.Failed to load organization role data')}
                </div>
              </div>
            ) : (
              <div className="flex-1 space-y-4 overflow-y-auto">
                {orgRoleDistribution.map((role, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{role.level}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">
                          {role.count} {t('Ropa.Dashboard.ROPAV3.ROPAs')}
                        </span>
                        <Badge variant={role.color as any}>{role.percentage}%</Badge>
                      </div>
                    </div>
                    <Progress value={role.percentage} className="h-2" color="bg-custom-primary" />
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
