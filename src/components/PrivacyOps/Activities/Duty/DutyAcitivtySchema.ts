import * as z from 'zod';
import { DocumentSchema } from '../Improvements/UpdateImprovementSchema';
const validIdRegex = /^[a-zA-Z0-9\s]+$/;

export const AddDutySchema = z.object({
  title: z.string().nonempty({ message: 'This field is required!' }),
  assignee_id: z.array(z.number()).nonempty({ message: 'At least one assignee is required!' }),
  due_date: z
    .string()
    .nonempty({ message: 'This field is required!' })
    .refine((date) => !isNaN(Date.parse(date)), { message: 'Invalid date format!' }),
  standard: z.string().nonempty({ message: 'This field is required!' }),
  comment: z.string().nonempty({ message: 'This field is required!' }),
  entity_id: z.string().nonempty({ message: 'This field is required!' }),
  document: z.array(DocumentSchema).optional(),
});
