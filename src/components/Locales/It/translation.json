{"Common": {"CreateNew": "Crea nuovo", "ClearFilters": "<PERSON><PERSON><PERSON> filtri", "Search": "Cerca", "RequiredField": "Questo campo è obbligatorio!", "Select": "Seleziona", "Submit": "Invia", "Cancel": "<PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON>", "Saving": "Salvataggio in corso...", "SaveAndContinue": "Salva e continua", "Delete": "Elimina", "Edit": "Modifica", "Next": "<PERSON><PERSON>", "Prev": "Precedente", "Create": "<PERSON><PERSON>", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddNew": "Aggiungi nuovo", "Import": "importare", "All": "<PERSON><PERSON>", "Submitting": "Invio in corso...", "Deleting": "Eliminazione in corso...", "Adding": "Aggiunta in corso...", "Updating": "Aggiornamento in corso...", "Reset": "Reimposta", "True": "Vero", "False": "<PERSON><PERSON><PERSON>", "Low": "<PERSON><PERSON>", "Medium": "Medio", "High": "Alto", "Apply": "Applica", "Write": "<PERSON><PERSON><PERSON>", "Action": "Azione", "Name": "Nome", "NoResult": " <PERSON><PERSON><PERSON> r<PERSON>.", "Update": "Aggiornare", "No": "No", "YesProceed": "Sì, Procedi", "Copy": "Copia", "Copied": "Copiato", "BackToList": "Torna alla lista"}, "ErrorPage": {"Page": "<PERSON><PERSON><PERSON>", "NotFound": "Non trovato", "NotExist": "<PERSON><PERSON><PERSON><PERSON>, la pagina che stai cercando non esiste.", "GoBack": "Torna indietro", "ReportError": "<PERSON><PERSON><PERSON>"}, "Breadcrumbs": {"ControlRoom": "Sala di Controllo", "Policy Management": "Gestione delle Politiche", "All Policies": "<PERSON><PERSON> le Politiche", "Privacy Notice": "Informativa sulla Privacy", "Audit Log": "Registro di Audit", "Data Subject Rights Management": "Gestione dei Diritti del Soggetto dei Dati", "Pending Requests": "Richieste in Attesa", "View Request": "Visualizza Richiesta", "Approved Requests": "<PERSON><PERSON> Approvate", "Reject In Progress Requests": "Richieste Rifiutate in Corso", "Rejected Requests": "Richieste Rifiutate", "Completed Requests": "Richieste Completate", "Profile": "<PERSON>ilo", "Change Password": "Cambia Password", "Blogs": "Blog", "View Blog": "Visualizza Blog", "About GoTrust": "Informazioni su GoTrust", "Company Structure": "Struttura A<PERSON>le", "Group Detail": "Dettaglio del Gruppo", "Customer Management": "Gestione Clienti", "Role Management": "Gestione dei Ruoli", "Role Details": "Dettagli del Ruolo", "Add Role": "Aggiungi Ruolo", "Edit Role": "Modifica Ruolo", "Edit Role Details": "Modifica Dettagli del Ruolo", "User Management": "Gestione Utenti", "Home": "Home", "Insights into Processing Activities": "Approfondimenti sulle attività di elaborazione", "Task Overview": "Panoramica delle attività", "ROPA": "ROPA", "ROPA Review": "Revisione ROPA", "Basic Information": "Informazioni di base", "Add Control": "Aggiungi controllo", "PII Inventory": "Inventario PII", "Unstructured Data Inventory": "Inventario dati non strutturati", "Data Catalogue": "Catalogo dati", "Structured": "Strutturato", "Unstructured": "Non strutturato", "Services": "<PERSON><PERSON><PERSON>", "Ingestion": "Ingestione", "Data Catalogue Dashboard": "Dashboard del catalogo dati", "PII List": "Elenco PII", "File Classification": "Classificazione dei file", "DSR Form Repository": "Archivio moduli DSR", "DSR Form Builder": "Generatore di moduli DSR", "Create Form": "Crea modulo", "Form Review": "Revisione modulo", "Add Question": "Agg<PERSON><PERSON><PERSON> domanda", "Schema Entity": "Entità dello schema", "Table Entity": "Entità della tabella", "Column Entity": "Entità della colonna", "Profile Entity": "Entità del profilo", "Template": "<PERSON><PERSON>", "Assessment Management": "Gestione della valutazione", "Dashboard": "Dashboard", "Templates": "<PERSON><PERSON>", "Vendor Risk Management": "Gestione del Rischio del Fornitore", "Vendor List": "Elenco dei Fornitori", "Details": "<PERSON><PERSON><PERSON>", "Internal Assessment": "Valutazione Interna", "Vendor Assessment": "Valutazione del Fornitore", "Mitigation": "Mitigazione", "Cookie Consent Management": "Gestione del Consenso dei Cookie", "Cookie Consent Domain": "Dominio del Consenso dei Cookie", "Cookie Configuration": "Configurazione dei Cookie", "Universal Consent Management": "Gestione Universale del Consenso", "Preference Center": "Centro Preferenze", "Add Preference Center": "Aggiungi Centro Preferenze", "Update Preference Center": "Aggiorna Centro Preferenze", "Consent Collection": "Raccolta del Consenso", "Consent Upload": "Caricamento del Consenso", "Custom Parameters": "<PERSON><PERSON><PERSON>", "Subject Consent Types": "Tipi di Consenso del Soggetto", "Subject Consent Detail": "Dettagli del Consenso del Soggetto", "Form": "<PERSON><PERSON><PERSON>", "Consent Collection Templates": "Modelli di Raccolta del Consenso", "Create Consent Collection Template": "Crea Modello di Raccolta del Consenso", "Processing Category": "Categoria di Elaborazione", "Processing Purpose": "Finalità del Trattamento", "Consent Purpose": "Finalità del Consenso", "PII Label": "Etichetta PII", "Privacy Notice Details": "Dettagli dell'Informativa sulla Privacy", "View Privacy Notice": "Visualizza Informativa sulla Privacy", "Subject Consent List": "Elenco dei Consensi del Soggetto", "Customization": "Personalizzazione", "Requests": "<PERSON><PERSON>", "View Requests": "Visualizza Richieste", "Tasks": "Attività", "View Tasks": "Visualizza Attività", "Reject in Progress Requests": "Rifiuta <PERSON> in Corso", "Create Request": "<PERSON><PERSON>", "Email Templates": "<PERSON><PERSON> di Email", "Create Email Template": "<PERSON><PERSON>ail", "Retention Schedule": "Programma di Conservazione", "Workflow": "Flusso di Lavoro", "Add Workflow": "Aggiungi Flusso di Lavoro", "Edit Workflow": "Modifica Flusso di Lavoro", "My Request": "La Mia Richie<PERSON>", "My Request View": "Visualizza La Mia Richiesta", "View Workflow": "Visualizza Flusso di Lavoro", "Support": "Supporto", "Create Ticket": "<PERSON><PERSON>", "View Ticket": "Visualizza Ticket", "Edit Ticket": "Modifica Ticket", "Finance": "Finanza", "Universal Control Framework": "Quadro di Controllo Universale", "Improvements": "Miglioramenti", "Risk Dashboard": "Dashboard dei Rischi", "Compliance Dashboard": "Dashboard di Conformità", "Privacy Ops": "Operazioni sulla Privacy", "Document Repository": "Archivio <PERSON>", "Assessment Repository": "Archivio delle Valutazioni", "Processing Activities": "Attività di Elaborazione", "Regulations": "Regolamenti", "Risk Register": "Registro dei Rischi", "Duty": "<PERSON><PERSON>", "Action": "Azione", "Improvement Actions": "Azioni di Miglioramento", "Breaches": "Violazioni", "Breach Details": "Dettagli della Violazione", "Subject Consent Manager": "Gestore del Consenso del Soggetto"}, "SideBar": {"GroupLabel": {"Account Setup": "Configurazione account", "Theme": "<PERSON><PERSON>", "Data Mapping": "Mappatura dati", "Policy Management": "Gestione delle politiche", "Data Subject Rights Management": "Gestione dei diritti dell'interessato", "Assessment Management": "Gestione delle valutazioni", "Universal Consent Management": "Gestione del consenso universale", "Universal Control Framework": "Framework di controllo universale", "Cookie Consent Management": "Gestione del consenso ai cookie", "Vendor Risk Management": "Gestione del rischio fornitori", "Awareness Program": "Programma di sensibilizzazione", "Blogs": "Blog", "Support": "Supporto", "Finance/Commercials": "Finanza/Commerciali", "Workflow Automation": "Automazione del flusso di lavoro", "Data Discovery": "Scoperta dei dati", "DPO Runbook": "Manuale operativo DPO", "Data Breach Management": "Gestione delle violazioni dei dati", "Configurations": "Configurazioni", "Policy & Notice Management": "Gestione di Politiche e Avvisi"}, "SideBarData": {"Profile Configuration": "Configurazione del profilo", "On-boarding Questionnaire": "Questionario di onboarding", "Company Structure": "Struttura aziendale", "Access Management": "Gestione degli accessi", "Role Management": "Gestione dei ruoli", "User Management": "Gestione degli utenti", "Vendor Management": "Gestione dei fornitori", "About GoTrust": "Informazioni su GoTrust", "Customization": "Personalizzazione", "Dashboard": "Dashboard", "Task Overview": "Panoramica delle attività", "Content Profiles": "Profili di contenuto", "Data Catalogue": "Catalogo dei dati", "Structure": "Strutturato", "Unstructure": "Non strutturato", "Data Catalogue V0": "Catalogo dati V0", "File Classification": "Classificazione dei file", "DSR Lab": "Laboratorio DSR", "DSR Form Builder": "Costruttore di moduli DSR", "DSR Report": "Rapporto DSR", "DSR Form Repository": "Archivio di moduli DSR", "DSR Email Templates": "Modelli di email DSR", "DSR Retention Schedule": "Piano di conservazione DSR", "Workflow": "Flusso di lavoro", "Impact Assessment": "Valutazione d'impatto", "Privacy Impact Assessment": "Valutazione d'impatto sulla privacy", "Privacy by Design Assessment": "Valutazione Privacy by Design", "Legitimate Interests Assessment": "Valutazione degli interessi legittimi", "Transfer Impact Assessment": "Valutazione dell'impatto del trasferimento", "EU AI Assessment": "Valutazione dell'IA dell'UE", "Assessment Lab": "Laboratorio di valutazione", "Assessment Templates": "Modelli di valutazione", "Subject Consent Types": "Tipi di consenso del soggetto", "Subject Consent List": "Elenco dei consensi del soggetto", "Privacy Notice": "Informativa sulla privacy", "Source": "Fonte", "Consent Upload": "Caricamento dei consensi", "UCM Lab": "Laboratorio UCM", "Processing Category": "Categoria di elaborazione", "Processing Purpose": "Scopo del trattamento", "Consent POC": "Punto di contatto per il consenso", "Consent Purpose": "Scopo del consenso", "PII Label": "Etichetta PII", "Consent Collection Builder": "Costruttore di raccolta consensi", "Preference Form": "Modulo delle preferenze", "Cookie Consent Domain": "Dominio del consenso ai cookie", "VRM Lab": "Laboratorio VRM", "Vendor Assessment Templates": "Modelli di valutazione dei fornitori", "Course Management": "Gestione dei corsi", "Registration": "Registrazione", "Enrollment": "Iscrizione", "Project View": "Vista del progetto", "Blogs": "Blog", "Billing & Invoice": "Fatturazione e fatture", "Workflow Automation": "Automazione del flusso di lavoro", "Data Discovery": "Scoperta dei dati", "Risk Dashboard": "Cruscotto dei rischi", "Compliance Dashboard": "Cruscotto della conformità", "Regulations": "Regolamenti", "Risk Register": "Registro dei rischi", "Activities": "Attività", "Duties": "<PERSON><PERSON>", "Actions": "Azioni", "Improvements": "Miglioramenti", "Repository": "Archivio", "Document Repository": "Archivio documenti", "Assessment Repository": "Archivio delle valutazioni", "Record of Processing Activities Repository": "Archivio del registro delle attività di trattamento", "User Guide": "Guida utente", "Breach List": "Elenco delle violazioni", "Subject Consent Manager": "Gestore del Consenso del Soggetto", "Theme Customization": "Personalizzazione del Tema", "Data Visualization": "Visualizzazione dei Dati", "Data Insights": "Approfondimenti sui Dati", "Data Flow Diagram": "Diagramma di Flusso dei Dati", "Data Vizualization": "Visualizzazione dei Dati"}}, "Home": {"Account Setup": "Impostazione dell'account", "Theme": "<PERSON><PERSON>", "Data Mapping": "Mappatura dei dati", "Policy & Notice Management": "Gestione delle politiche e delle notifiche", "Data Subject Rights Management": "Gestione dei diritti dell'interessato", "Assessment Management": "Gestione delle valutazioni", "Universal Consent Management": "Gestione universale del consenso", "Universal Control Framewok": "Quadro di controllo universale", "Cookie Consent Management": "Gestione del consenso dei cookie", "Vendor Risk Management": "Gestione del rischio dei fornitori", "Awareness Program": "Programma di sensibilizzazione", "Blogs": "Blog", "Support": "Supporto", "Finance/Commercials": "Finanza/Commerciali", "Workflow Automation": "Automazione del flusso di lavoro", "Data Discovery": "Scoperta dei dati", "DPO Runbook": "Manuale DPO", "Data Breach Management": "Gestione delle violazioni dei dati", "Data Retention": "Conservazione dei dati", "Universal Control Framework": "Framework di Controllo Universale"}, "CompanyStructure": {"GroupDetails": {"Name": "Nome", "Parent": "Superiore", "NoOfUsers": "Numero di utenti", "CreatedOn": "Creato il", "UpdatedOn": "Aggiornato il", "FirstName": "Nome", "LastName": "Cognome"}, "CompanyTree": {"BusinessUnit": "Unità aziendale", "DepartmentUnit": "Dipartimento", "ProcessUnit": "Unità di processo"}, "CompanyView": {"Name": "Nome", "Added On": "Aggiunto il", "Last Updated": "Ultimo <PERSON>", "No. Of Users": "Numero di Utenti", "Actions": "Azioni"}}, "RoleManagement": {"AddRole": {"AddRole": "Aggiungi Ruolo", "Heading": "Dettagli del ruolo", "RoleName": "Nome del ruolo", "EnterRoleName": "Inserisci nome del ruolo", "GivenAccess": "Accesso concesso all'utente"}, "RoleTable": {"RoleName": "Nome del Ruolo", "CreatedBy": "<PERSON><PERSON><PERSON> <PERSON>", "Totaluser": "Totale Utenti", "CreatedOn": "Creato il", "UpdatedOn": "Aggiornato il", "Action": "Azione"}, "ActiveStatus": {"Active": "Attivo", "Inactive": "Inattivo", "Archived": "Archiviato"}, "ViewRole": {"Heading": "Dettagli del ruolo", "RoleName": "Nome del ruolo", "EnterRoleName": "Inserisci nome del ruolo", "CreatedBy": "<PERSON><PERSON><PERSON> <PERSON>", "Status": "Stato", "CreatedDate": "Data di creazione", "UpdatedDate": "Data di aggiornamento", "GivenAccess": "Accesso concesso all'utente", "EditRole": "Modifica ruolo"}, "EditRole": {"Heading": "Dettagli del ruolo", "RoleName": "Nome del ruolo", "Status": "Stato", "GivenAccess": "Accesso concesso all'utente"}}, "UserManagement": {"AddUser": {"Heading": "Dettagli utente", "FirstName": "Nome", "EnterFirstName": "Inserisci nome", "LastName": "Cognome", "EnterLastName": "Inserisci cognome", "Email": "E-mail", "EnterEmail": "Inserisci e-mail", "Phone": "Telefono", "EnterPhoneNumber": "Inserisci numero di telefono", "SelectRole": "Seleziona ruolo", "GroupAssigned": "Gruppo assegnato", "AccessesUser": "Accesso concesso all'utente", "SeeAccesses": "Seleziona ruolo per vedere gli accessi"}, "EditUser": {"Heading": "Dettagli utente", "FirstName": "Nome", "EnterFirstName": "Inserisci nome", "LastName": "Cognome", "EnterLastName": "Inserisci cognome", "Email": "E-mail", "EnterEmail": "Inserisci e-mail", "Phone": "Telefono", "EnterPhoneNumber": "Inserisci numero di telefono", "SelectRole": "Seleziona ruolo", "Status": "Stato", "GroupAssigned": "Gruppo assegnato", "AccessesUser": "Accesso concesso all'utente", "SeeAccesses": "Seleziona ruolo per vedere gli accessi"}, "ViewUser": {"Heading": "Dettagli utente", "FirstName": "Nome", "EnterFirstName": "Inserisci nome", "LastName": "Cognome", "EnterLastName": "Inserisci cognome", "Email": "E-mail", "EnterEmail": "Inserisci e-mail", "Phone": "Telefono", "EnterPhoneNumber": "Inserisci numero di telefono", "SelectRole": "Seleziona ruolo", "Status": "Stato", "CreatedDate": "Data di creazione", "UpdatedDate": "Data di aggiornamento", "GroupAssigned": "Gruppo assegnato", "AccessesUser": "Accesso concesso all'utente", "SeeAccesses": "Seleziona ruolo per vedere gli accessi", "EditUser": "Modifica utente"}, "UserTable": {"AddUser": "Aggiungi Utente", "FirstName": "Nome", "LastName": "Cognome", "Email": "Email", "Phone": "Telefono", "AddedDate": "Data di Aggiunta", "UpdatedDate": "Data di Aggiornamento", "Group": "Gruppo", "RoleName": "Nome del Ruolo", "Action": "Azione"}}, "CustomerManagement": {"AddCustomer": {"Heading": "Dettagli cliente", "Email": "E-mail", "EnterEmail": "Inserisci e-mail", "Address": "<PERSON><PERSON><PERSON><PERSON>", "EnterAddress": "Inserisci indirizzo", "AdminDetails": "Dettagli amministratore", "AdminName": "Nome amministratore", "EnterAdminName": "Inserisci nome amministratore", "Phone": "Telefono", "EnterPhoneNumber": "Inserisci numero di telefono", "AccessesUser": "Accesso concesso all'utente", "SeeAccesses": "Seleziona ruolo per vedere gli accessi"}}, "CookieConsentManagement": {"ServiceSavedSuccessfully": "<PERSON><PERSON><PERSON> sal<PERSON> con <PERSON>o", "DomainDetails": {"BusinessUnit_Tooltip": "L'unità aziendale a cui appartiene questo dominio. Aiuta a mappare i banner di consenso ai cookie al team o alla funzione appropriata all'interno dell'organizzazione.", "DomainGroup_Tooltip": "Il nome del dominio dove verrà eseguito lo scan dei cookie.", "DomainURL_Tooltip": "L'URL completo del sito web dove verrà eseguito lo scan dei cookie. Esempio: https://www.example.com.", "Owner_Tooltip": "La persona o il dipartimento responsabile della gestione del consenso ai cookie per questo dominio. Sarà notificato di qualsiasi cambiamento o problema.", "OwnerEmail_Tooltip": "L'indirizzo di posta elettronica dove verranno inviate le notifiche e le aggiornamenti relativi al consenso ai cookie. Può essere un indirizzo di posta elettronica di team o un indirizzo individuale.", "CookiePolicyLink_Tooltip": "Collegamento diretto alla pagina della politica dei cookie del dominio. Questo verrà mostrato nel banner di consenso per informare gli utenti sulle pratiche dei dati.", "Entity_Tooltip": "Le normative di protezione dei dati che sono applicabili a questa URL di dominio specifica.", "ConsentFramework_Tooltip": "Le normative di protezione dei dati applicabili a questa URL di dominio specifica.", "BusinessUnit": "Unità aziendale", "CookiePolicyLink": "Link alla Politica dei Cookie", "GroupDomainName": "Nome del Gruppo di Dominio", "Heading": "Dettagli del Dominio", "DomainGroup": "Gruppo di Dominio", "DomainGroup_Placeholder": "Seleziona", "DomainName": "Nome Dominio", "DomainName_Placeholder": "Inserisci Nome Dominio", "DomainURL": "URL del Dominio", "URL": "URL", "URL_Placeholder": "Inserisci URL", "Owner": "Proprietario", "Owner_Placeholder": "Inserisci Nome Proprietario", "OwnerEmail": "Email Proprietario", "OwnerEmail_Placeholder": "Inserisci Email Proprietario", "Entity": "Entità", "Entity_Placeholder": "Seleziona", "CreatedOn": "Creato il", "ConsentFramework": "Quadro del Consenso", "CreatedOn_Placeholder": "Scegli una data", "CompliancePolicyLink": "Link alla Politica di Conformità", "CompliancePolicyLink_Placeholder": "<PERSON><PERSON><PERSON>"}}, "VendorRiskManagement": {"CreateNewVendor": {"name": "Fornitore", "EnterName": "Inserisci nome fornitore", "SelectName": "Seleziona nome fornitore", "entity": "Entità", "SelectEntity": "Seleziona nome entità", "department": "Dipartimento", "SelectDepartment": "Seleziona nome dipartimento", "assigned_to": "Assegnato a", "SelectAssignee": "Seleziona nome assegnatario", "reviewer": "<PERSON><PERSON><PERSON>", "SelectReviewer": "Seleziona nome revisore", "template": "<PERSON><PERSON>", "SelectTemplate": "Seleziona nome modello", "DefaultTemplate": "Quadro di Valutazione del Rischio Fornitore (predefinito)"}, "Assessment": {"next": "<PERSON><PERSON>", "previous": "Precedente", "controls": "<PERSON><PERSON>", "collaborator": "Collaboratore", "upload": "<PERSON><PERSON>"}, "Lab": {"upload": "<PERSON><PERSON>", "DropHere": "Trascina qui per allegare o", "Upload": "carica", "FileType": "File CSV o XLSX | Dimensione max: 10MB", "name": "<PERSON><PERSON>", "PasteURL": "Incolla URL"}, "ViewDetails": {"CollaboratorsProgressInVRM": "Categoria Progressi nella valutazione interna"}}, "AboutUs": {"OurMission": "La Nostra Missione", "KeyFeature": "<PERSON><PERSON><PERSON><PERSON>", "WhyChoose": "<PERSON><PERSON><PERSON> GoTrust?"}, "Ropa": {"PiiHandbook": {"PIIHandBook": "Manuale PII", "AddPii": "Aggiungi PII", "PiiName": "Tipo di PII", "Description": "Descrizione", "Tags": "Tag", "TypeTag": "Inserisci un tag", "Status": "Stato", "Cancel": "<PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON>", "AddingPii": "Aggiunta PII...", "PiiType": "Tipo PII", "PiiCategory": "Categoria PII", "PiiValue": "Valore PII", "PiiDescription": "Descrizione PII", "PiiTags": "Tag PII", "PiiTypeTag": "Inserisci un tag e premi Enter", "PiiStatus": "Stato", "PiiCancel": "<PERSON><PERSON><PERSON>", "PiiSave": "<PERSON><PERSON>", "PiiNamePlaceholder": "Inserisci nome PII", "PiiDescriptionPlaceholder": "Inserisci descrizione", "PiiTagsPlaceholder": "Inserisci un tag e premi Enter", "PiiStatusPlaceholder": "Seleziona Stato", "PiiTypePlaceholder": "Seleziona Tipo PII", "PiiCategoryPlaceholder": "Seleziona Categoria PII", "PiiValuePlaceholder": "Inserisci valore PII", "PiiSaving": "Salvataggio...", "PiiEditing": "Modifica...", "PiiAdding": "Aggiunta...", "PiiDeleting": "Cancellazione...", "PiiDeletingConfirmation": "Sei sicuro di voler cancellare questa PII?", "PiiEditPii": "Modifica PII", "PiiPiiType": "Tipo PII", "PiiPiiTypePlaceholder": "Seleziona Tipo PII", "PiiPiiDescription": "Descrizione PII", "PiiPiiDescriptionPlaceholder": "Inserisci descrizione", "PiiPiiTags": "Tag PII", "PiiPiiTagsPlaceholder": "Inserisci un tag e premi Enter", "PiiPiiStatus": "Stato", "PiiPiiStatusPlaceholder": "Seleziona Stato", "PiiPiiSource": "Fonte", "PiiPiiSourcePlaceholder": "Inserisci Fonte", "PiiPiiAddedSuccessfully": "PII aggiunto con successo", "PiiPiiAddedFailed": "Impossibile aggiungere PII", "PiiPiiUpdatedSuccessfully": "PII aggiornato con successo", "PiiPiiUpdatedFailed": "Impossibile aggiornare PII", "PiiPiiDeletedSuccessfully": "PII eliminato con successo", "PiiPiiDeletedFailed": "Impossibile eliminare PII", "PiiPiiAlreadyExists": "PII già esistente", "PiiPiiDeletePii": "Elimina PII", "PiiPiiDeletePiiDescription": "Sei sicuro di voler eliminare questa PII ", "withTag": "con tag", "PiiPiiDelete": "Elimina", "PiiPiiCancel": "<PERSON><PERSON><PERSON>", "PiiPiiDeleting": "Eliminazione...", "PiiPiiAddFailed": "Aggiunta PII fallita", "PiiPiiDeleteFailed": "Eliminazione PII fallita", "PiiPiiUpdateFailed": "Aggiornamento PII fallito"}, "Dashboard": {"ByDepartment": {"Heading": "Attività di Elaborazione per Dipartimento", "Count": "Conteggio Totale"}, "ByOrganization": {"Heading": "Attività di Elaborazione per Ruolo Organizzativo", "Count": "Conteggio Totale"}, "ByLawfullBasis": {"Heading": "Attività di Elaborazione per Base Legale", "y-axis": "Base Legale Applicata", "x-axis": "Numero di Attività di Elaborazione"}, "ByPersonalData": {"Heading": "Dati Personali e Sensibili per Dipartimento", "y-axis": "<PERSON><PERSON><PERSON><PERSON>", "x-axis": "Numero di Elementi di Dati Personali"}, "ThirdPartiesList": {"Heading": "Elenco di Terze Parti", "TableHeading": {"Vendor": "Fornitore", "Services": "<PERSON><PERSON><PERSON>", "Department": "Dipartimento", "Location": "Posizione", "PersonalData": "Dati Personali Coinvolti"}}, "DataSystemList": {"Heading": "Elenco di Sistemi Dati/Applicazioni", "Count": "Conteggio Totale", "TableHeading": {"Purpose": "Scopo", "InHouse": "Interno/Terza Parte", "ThirdPartyName": "Nome della Terza Parte", "Location": "Posizione"}}, "DPIA": {"Heading": "Requisiti DPIA", "y-axis": "Numero di Attività di Elaborazione ad Alto Rischio", "x-axis": "Dipartimento"}, "ROPAV3": {"ROPA Management": "Gestione ROPA", "ROPA Dashboard": "Dashboard ROPA", "ROPA Assessments": "Valutazioni ROPA", "Overview of all processing activity assessments": "Panoramica di tutte le valutazioni delle attività di elaborazione", "Comprehensive Records of Processing Activities management and compliance tracking": "Gestione completa dei Registri delle Attività di Trattamento e monitoraggio della conformità", "ROPA Management Dashboard": "Dashboard di Gestione ROPA", "Records of Processing Activities (Article 30 GDPR Compliance)": "Registri delle Attività di Trattamento (Conformità Articolo 30 GDPR)", "Select Entity": "Seleziona Entità", "Active assessments": "Valutazioni attive", "Total ROPAs": "ROPA totali", "Yet to Start": "Da iniziare", "In Progress": "In corso", "Completed": "Completato", "Processing Activities by Organization Role": "Attività di Trattamento per Ruolo Organizzativo", "Total count": "Conteggio totale", "Loading organization role data...": "Caricamento dati ruolo organizzativo...", "Failed to load organization role data": "Caricamento dati ruolo organizzativo fallito", "Controller": "Titolare del Trattamento", "Joint-Controller": "Contitolare del Trattamento", "Processor": "Responsabile del Trattamento", "Sub-Processor": "Sotto-responsabile del Trattamento", "ROPAs": "ROPAs", "Processing Activities by Department": "Attività di Trattamento per Dipartimento", "Total processes": "Processi totali", "Departments": "<PERSON><PERSON><PERSON><PERSON>", "Error loading department data": "Errore nel caricamento dati dipartimento", "processes": "processi", "ROPA Progress": "Progresso ROPA", "Total": "Totale", "Completed Department": "Completato", "In Progress Department": "In corso", "No department data available": "<PERSON><PERSON><PERSON> dato dipartimento disponibile", "List of Third Parties by Department": "Elenco delle terze parti per dipartimento", "Loading...": "Caricamento...", "Failed to load data.": "Caricamento dati fallito.", "Vendor": "Fornitore", "Services": "<PERSON><PERSON><PERSON>", "Department": "Dipartimento", "Location": "Posizione", "Personal Data Involved": "Dati Personali Coinvolti", "No data available": "<PERSON><PERSON><PERSON> dato disponibile", "No records found.": "Nessun record trovato.", "Active Collaborators": "Collaboratori Attivi", "Team members working on ROPA assessments": "Membri del team che lavorano su valutazioni ROPA", "Loading collaborators...": "Caricamento collaboratori...", "Failed to fetch collaborator data": "<PERSON><PERSON><PERSON> dati <PERSON> fallito", "Failed to load collaborator data": "Caricamento dati collaboratori fallito", "No active collaborators found": "Nessun collaboratore attivo trovato", "ROPAs assigned": "ROPA assegnati", "completed": "completati", "Complete": "Completo", "Progress": "Progresso", "more": "altri", "total ROPAs": "ROPA totali", "Recent ROPA Activities": "Attività ROPA Recenti", "Latest updates on processing activity assessments": "Ultimi aggiornamenti sulle valutazioni delle attività di trattamento", "View All": "<PERSON><PERSON><PERSON>", "Loading recent activities...": "Caricamento attività recenti...", "No recent ROPA activities found": "Nessuna attività ROPA recente trovata", "Assignee": "Assegnatar<PERSON>", "Importing a new document will result in the loss of all previous ROPA performed. Are you sure you want to continue?": "L'importazione di un nuovo documento comporterà la perdita di tutti i ROPA precedenti eseguiti. Sei sicuro di voler continuare?", "Manage Records of Processing Activities assessments": "Gestisci valutazioni dei Registri delle Attività di Trattamento", "Department Level": "<PERSON><PERSON>", "Process Level": "Livello Processo"}}, "Activity": {"TableHeading": {"Department": "Dipartimento", "Process": "Unità di Processo", "SPOC": "Punto di Contatto", "StartDate": "Data di Inizio", "UpdatedDate": "Data Aggiornata", "AssignedTo": "Assegnato A", "is_assigned": "Assegnato a <PERSON>", "Reviewer": "<PERSON><PERSON><PERSON>", "progress": "Progresso", "Review": "Revisione", "Risks": "<PERSON><PERSON><PERSON>", "Status": "Stato", "Action": "Azione", "Import": "Importa", "ImportNewFile": "Importa Nuovo File", "DownloadSampleFile": "Scarica File di Esempio", "Filter": "Filtra", "ClearFilter": "Cancella Filtro", "Start": "Inizia", "Re-Start": "Ricomincia", "View": "Visualizza", "Assign": "Assegna", "ROPA Details": "Dettagli ROPA", "Type": "Tipo", "Assignment": "Compito"}}, "ViewDetails": {"Reviewer": "<PERSON><PERSON><PERSON>", "AssignedTo": "Assegnato a", "Department": "Dipartimento", "Entity": "Entità", "UpdatedDate": "Data Aggiornata", "SPOC": "SPOC", "Review": "Revisione", "Start": "Inizia", "Assign": "Assegna", "CollaboratorsProgressInRopa": "Progresso del Category in ROPA", "RopaDetails": "Dettagli di ROPA", "TentativeCompletionDate": "Data di Completamento Provvisoria", "PickaDate": "Seleziona un intervallo di date", "Category": "Categoria", "Collaborators": "Collaboratori", "ROPADetails": "Dettagli di ROPA"}, "DataCatalogue": {"DashBoard": {"PIIHandBook": "Manuale PII", "TabList": {"Structured": "Strutturato", "Unstructured": "Non strutturato"}, "Structured": {"ServiceOwner": "Proprietario del Servizio", "DataManagement": "Gestione dei Dati", "DataDetails": "Dettagli dei Dati", "TotalPIIs": "Totale PII", "TotalPIICategories": "Totale Categorie PII", "DistinctPIIsDetected": "PII distinti rilevati", "DataLocations": {"DataLocations": "Posizioni dei Dati", "DataLocation": "Posizione dei Dati", "DataElements": "Elementi di Dati", "Databases": "<PERSON><PERSON>", "Schemas": "<PERSON><PERSON><PERSON>", "Tables": "<PERSON><PERSON><PERSON>", "DataSystems": "Sistemi di Dati"}, "SankeyGraph": {"DisplayAllData": "Visualizzazione di tutti gli elementi di dati", "NoDataAvailable": "<PERSON><PERSON><PERSON> dato disponibile"}, "DataSensitivityCard": {"Data Sensitivity": "Sensibilità dei Dati", "NoDataAvailable": "<PERSON><PERSON><PERSON> dato disponibile"}, "PIICategories": "Categorie PII", "PIIDistributions": {"PII": "PII", "PIICount": "Conteggio PII", "PIIDistribution": "Distribuzione PII", "Columns": "Colonne", "Schemas": "<PERSON><PERSON><PERSON>", "Database": "Database", "DataSystems": "Sistemi di Dati", "Location": "Posizione"}}, "Unstructured": {"DataService": "<PERSON><PERSON><PERSON>", "DataLocations": "Posizioni dei Dati", "DataManagement": "Gestione dei Dati", "ScannedDocuments": "Documenti Scansionati", "ScannedVolume": "Volume Scansionato", "ScannedFileFormat": "Formato del File Scansionato", "DataDetails": "Dettagli dei Dati", "TotalPIICategories": "Totale Categorie PII", "DistinctPIIsDetected": "PII distinti rilevati", "TotalDetectedPIIs": "Totale PII rilevati", "SankeyGraph": {"DisplayAllElements": "Visualizzazione di tutti gli elementi di dati", "NoDataAvailable": "<PERSON><PERSON><PERSON> dato disponibile"}, "DocumentCards": {"Documents": "Documenti", "DocumentName": "Nome del Documento", "DocumentType": "Tipo di Documento", "PIIsDetected": "PII rilevati", "DocumentSize": "Dimensione del Documento", "Location": "Posizione", "DataService": "<PERSON><PERSON><PERSON>", "SubService": "Sottoservizio"}, "DataLocationCard": {"DataLocation": "Posizione dei Dati", "DataServiceLoc": "Posizione del Servizio Dati", "Document": "Documento", "DocumentType": "Tipo di Documento", "DataService": "<PERSON><PERSON><PERSON>"}, "DataSensitivityCard": {"DataSensitivity": "Sensibilità dei Dati", "NoDataAvailable": "<PERSON><PERSON><PERSON> dato disponibile"}, "FileFormats": "Formati di File", "PIICard": {"PII": "PII", "PIICount": "Conteggio PII", "Documents": "Documenti", "Location": "Posizione", "DataService": "<PERSON><PERSON><PERSON>"}, "Services": "<PERSON><PERSON><PERSON>", "ID": "ID", "ServiceName": "Nome del servizio", "ServiceType": "Tipo di servizio", "Status": "Stato", "Running": "In esecuzione", "Done": "Completato", "Failed": "Fallito", "NA": "N/D", "Action": "Azione", "AddNewService": "Aggiungi nuovo servizio", "SelectIngestion": "Seleziona un'ingestione", "Microsoft365": "Microsoft 365", "Aws": "AWS", "GoogleWorkspace": "Google Workspace", "AzureDataLake": "Azure Data Lake"}, "PIIList": {"AddNew": "Aggiungi nuovo", "PIICategory": "Categoria di PII", "PIIValue": "Valore di PII", "PIIDescription": "Descrizione di PII", "Applicability": "Applicabilità", "Sensitivity": "Sensibilità", "Actions": "Azioni", "EnterDescription": "Inserisci la tua descrizione qui"}}, "TotalColumns": "Totale Colonne", "TotalTables": "Totale Tabelle", "TotalDatabases": "Totale Basi di Dati", "DistinctPIIsCount": "Conteggio Distinto di PII", "ColumnName": "Nome della Colonna", "TableName": "Nome <PERSON>", "ServiceName": "Nome del Servizio", "DataCategory": "Categoria dei Dati", "PIIDetected": "PII Rilevato", "Sensitivity": "Sensibilità", "DataSystem": "<PERSON><PERSON><PERSON>", "DataSystemOwner": "Proprietario del Sistema Dati", "DataServiceCount": "Conteggio dei Servizi Dati", "ScannedDocumentCount": "Conteggio dei Documenti Scansionati", "TotalVolume": "Volume Totale", "FileFormatCount": "Conteggio dei Formati di File", "DocumentName": "Nome del Documento", "DocumentType": "Tipo di Documento", "PIIsDetected": "PII Rilevati", "Size": "Dimensione", "Location": "Posizione", "DataService": "<PERSON><PERSON><PERSON>", "SubService": "Sottoservizio", "FileLocation": "Posizione del file", "DropHere": "Trascina qui per allegare o", "Upload": "Carica", "SelectedFiles": "File Selezionati", "MaxSize": "Dimensione Massima: 10MB", "AcceptedFormats": "Formati Accettati", "Datatype": "Tipo di Dato", "Tags": "Tag", "PIIType": "Tipo di PII", "Version": "Versione", "TagDetails": "Dettagli del Tag", "DataSystemCount": "Conteggio dei Sistemi Dati", "DetectedData": "<PERSON><PERSON>", "ConfidenceScore": "Punteggio di Affidabilità"}, "ChartData": {"Controller": "Tito<PERSON><PERSON> del trattamento", "Joint-Controller": "Contitolare del trattamento", "Processor": "Responsabile del trattamento", "Sub-Processor": "Sub-responsabile del trattamento", "Finance": "Finanza", "AI": "IA", "Cloud": "Cloud", "Frontend": "Interfaccia", "ML": "Apprendimento automatico", "Consultancy": "Consulenza", "Backend": "Backend", "HR": "Risorse umane", "IT": "IT", "DevOps": "DevOps", "Sales": "Vendite", "Consent": "Consenso", "PII": "Informazioni di identificazione personale", "SPI": "Informazioni personali sensibili", "Performance of a contract": "Esecuzione di un contratto", "A legitimate interest": "Un interesse legittimo", "A vital interest": "Un interesse vitale", "A legal requirement": "Un requisito legale"}}, "Policy": {"Dashboard": {"Total": "Numero totale di politiche", "InUse": "Numero totale di politiche in uso", "Expiring": "Numero totale di politiche in scadenza in 45 giorni", "Policies": "Politiche", "NoOfPolicies": "Numero di politiche", "WorkflowHeading": "Politiche per fase del flusso di lavoro", "Entities": "Entità", "EntitiesHeading": "Politiche per entità", "DepartmentsHeading": "<PERSON><PERSON><PERSON>"}, "AllPolicies": {"PolicyName": "Nome della politica", "PolicyCategory": "Categoria della politica", "Entity": "Entità", "Recurrence": "Ricorrenza", "RenewalDate": "Data di rinnovo", "WorkflowStage": "Fase del flusso di lavoro", "Department": "Dipartimento", "Action": "Azione", "CreationofPolicy": "Creazione della politica", "ReviewofPolicy": "Revisione della politica", "ApprovalofPolicy": "Approvazione della politica", "PolicyinUse": "Politica in uso"}, "NewPolicyModal": {"Heading": "Crea nuova politica", "PolicyName": "Inserisci il nome della politica", "PolicyDescription": "Descrizione della politica", "PolicyCategory": "Categoria della politica", "EntityName": "Nome dell'entità", "Language": "<PERSON><PERSON>", "PolicyAuthor": "Autore della politica", "PolicyReviewer": "Revisore della politica", "PolicyApprover": "Approvante della politica", "EffectiveDate": "Data di entrata in vigore", "RecurrencePeriod": "Periodo di ricorrenza", "Department": "Dipartimento", "VersionNumber": "Numero di versione", "PolicyID": "ID della politica", "RelevantStandard/Law": "<PERSON>/legge pertinente", "Create": "CREA"}, "PolicyRequirement": {"Heading": "Requisito della politica", "Reviewer": "<PERSON><PERSON><PERSON>", "Approver": "Approvante", "Department": "Dipartimento", "Entity": "Entità", "ReviewDate": "Data di revisione", "Recurrence": "Ricorrenza", "Collaborator": "Collaboratore"}, "PolicyAttachment": {"Heading": "Allegato", "CreateWithAI": "Crea politica con AI", "UpdateWithAI": "Aggiorna politica con AI", "UploadedAttachment": "<PERSON>egato caricato"}, "AddAttachment": {"Heading": "Crea nuova versione", "DropHere": "Trascina qui per allegare o", "Upload": "carica", "FileType": "File PDF o Word | Dimensione massima: 5MB", "URL": "URL", "PasteURL": "Incolla URL"}}, "AssessmentManagement": {"Dashboard": {"TotalAssessments": "Totaal aantal beoordelingen", "ByReadiness": "Verdeling op basis van g<PERSON>", "ByRegulation": "Per regelgevingstype", "ByOwners": "Beoordeling door eigenaren", "RecentAssessments": "<PERSON><PERSON> be<PERSON>", "ViewAll": "<PERSON><PERSON> be<PERSON>en", "AssessmentName": "<PERSON><PERSON>", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Entity": "Entiteit", "Department": "Afdeling", "ProcessUnit": "Procesunit", "StartDate": "Startdatum", "UpdatedDate": "Bijgewerkte datum", "Reviewer": "<PERSON><PERSON><PERSON><PERSON>", "AssignedTo": "Toegewezen aan", "Risks": "Risico's", "Progress": "Voortgang", "Status": "Status"}, "ViewDetails": {"CollaboratorsProgressInRopa": "Categoria Progressi nella valutazione"}}, "DSR": {"Dashboard": {"Total": "Numero totale di richieste", "Request": "<PERSON><PERSON>", "RequestStats": "Statistiche richieste", "Approved Requests": "<PERSON><PERSON> approvate", "Pending Requests": "<PERSON><PERSON> in sospeso", "Rejected Requests": "Richieste rifiutate", "Completed Requests": "Richieste completate", "Reject in Progress": "Rifiuto in corso", "Extended": "<PERSON><PERSON> estese", "RequestsByRights": "Richieste per diritti", "TotalCount": "Conteggio totale", "RequestStatistics": "Statistiche richieste", "MoreThan": "<PERSON><PERSON> di", "newRequests": "nuove richieste", "RecentRequests": "<PERSON><PERSON>", "Monthly": "<PERSON><PERSON><PERSON>", "Annually": "Annuale", "NumberOfApprovedRequest": "Numero di richieste approvate", "RequestsByStages": "Richieste per fasi", "Verify": "Verifica", "Stages": "Fasi", "days": "<PERSON>ior<PERSON>", "NoData": "<PERSON><PERSON><PERSON> dato", "ApproachingDeadline": "Scadenza imminente", "Acknowledgement": "Conferma", "Count": "Conteggio", "RequestByResidency": "Richie<PERSON> per residenza", "RequestStatusPerOwner": "Stato della richiesta per proprietario", "Completed": "Completato", "Pending": "<PERSON><PERSON> in sospeso", "Last7days": "Ultimi 7 giorni", "Last14days": "Ultimi 14 giorni", "Last30days": "Ultimi 30 giorni", "Last60days": "Ultimi 60 giorni", "All": "<PERSON><PERSON>", "RequestTypes": "Tipi di richiesta"}, "TaskOverView": {"CreateRequest": "<PERSON><PERSON> rich<PERSON>a", "CreateForm": "Crea modulo", "DueDays": "<PERSON><PERSON><PERSON> fino alla scadenza del compito", "TaskType": "Tipo di attività", "Automation Workflow": "Flusso di lavoro di automazione", "UpdateTask": "Aggiorna compito", "UpdateNote": "Aggiorna nota", "Approve": "<PERSON><PERSON><PERSON><PERSON>", "Reject": "<PERSON><PERSON><PERSON><PERSON>", "EnterTaskTitle": "Inserisci il titolo dell'attività", "DataSubjectRequestDetailsID": "ID Dettagli Richie<PERSON> Soggetto Dati", "RequestID": "ID Richie<PERSON>", "NoResult": "<PERSON><PERSON><PERSON> r<PERSON>", "Assign": "Assegna", "View": "Visualizza", "EmailVerified": "Email <PERSON>", "Region": "Regione", "RequestDate": "<PERSON>", "RequestedBy": "Richie<PERSON>", "RejectRequest": "Sei sicuro di voler rifiutare questa richiesta?", "ConfirmApprove": "Sei sicuro di voler approvare questa richiesta?", "Attachments": "Allegati", "NoAttachmentsAvailable": "<PERSON><PERSON>un allegato disponibile", "PostalCode": "Codice Postale", "Location": "Località", "Email": "Email", "FirstName": "Nome", "LastName": "Cognome", "Phone": "Telefono", "UserDetails": "Dettagli Utente", "RequestDescription": "Descrizione Richiesta", "Status": "Stato", "RelationshipWithUs": "Relazione con noi", "RequestType": "Tipo di <PERSON>sta", "CreatedOn": "Creato il", "RequestInformation": "Informazioni Richiesta", "AddNote": "<PERSON><PERSON><PERSON><PERSON><PERSON> Nota", "Approved": "A<PERSON>rovato", "COMPLETED": "COMPLETATO", "APPROVED": "APPROVATO", "Rejected": "Rifiutato", "RejectionInProgress": "Rifiuto in Corso", "Extended": "<PERSON><PERSON><PERSON>", "Confirm": "Conferma", "ConfirmExtend": "Sei sicuro di voler estendere la scadenza?", "Approver": "Approvante", "Deadline": "Scadenza", "BusinessUnit": "Unità Aziendale", "WebForm": "Modulo Web", "DSAR": "<PERSON><PERSON>lo DSAR (V12)", "Form Preview": "Anteprima Modulo", "PreferredLanguage": "Lingua Preferita", "DSRID": "ID DSR", "SubjectType": "Tipo di Soggetto", "FullName": "Nome <PERSON>to", "StepsStatus": "Stato delle Fasi", "Assigned": "<PERSON><PERSON><PERSON><PERSON>", "WantToApprove": "Sei sicuro di voler approvare?", "WantToReject": "Sei sicuro di voler rifiutare?", "ReasonOfRejection": "Motivo del Rifiuto", "Action": "Azione", "UploadCompletionDocuments": "Carica documenti di completamento", "ClickToUploadDocuments": "Clicca per caricare documenti", "FormPreview": "Anteprima del modulo", "TasksTitle": "<PERSON><PERSON>", "NotStarted": "Non iniziato", "InProgress": "In corso", "RejectTask": "<PERSON><PERSON><PERSON><PERSON> attivi<PERSON>", "Steps": "Passaggi", "Attachment": "Allegato", "DownloadAuditLog": "Scarica", "Automate": "Automatizzare"}, "EmailTemplate": {"ConfirmDelete": "Conferma eliminazione", "CreateTemplate": "<PERSON><PERSON>", "ConfirmDeleteQuestion": "Sei sicuro di voler eliminare questo modello?", "CancelBtn": "<PERSON><PERSON><PERSON>", "DeleteBtn": "Elimina", "EmailTemplate": "<PERSON><PERSON> email", "Modified": "Modificato", "CreatedAt": "Creato il", "Email": "Email", "Name": "Nome", "Action": "Azione", "Preview": "Anteprima", "TemplateName": "Oggetto / Nome del modello", "CreateEmailTemplate": "<PERSON><PERSON>lo email", "EmailTemplatePreview": "Anteprima del modello email"}, "FormBuilder": {"FormBuilder": "Generatore di moduli", "FormName": "Nome modulo", "Regulations": "Regolamenti", "URLNotAvailableForDraftForms": "URL non disponibile per i moduli in bozza", "VerificationMethod": "Metodo di verifica", "ConfirmDeleteForm": "Sei sicuro di voler eliminare questo", "Form": "modulo", "ActionVersion": "Versione azione", "URL": "URL", "Action": "Azione", "Entity": "Entità", "LastUpdated": "Ultimo aggiornamento", "Email": "Email", "Published": "Pubblicato", "UploadingLogo": "Caricamento del logo in corso...", "LogoUploadedSuccessfully": "Logo caricato con successo", "FailedToUploadLogo": "Caricamento del logo non riuscito", "SaveFormBeforeCreatingRules": "Salva il modulo prima di creare le regole", "FillAllRuleFields": "Si prega di compilare tutti i campi della regola", "SelectValidFieldsForRule": "Seleziona campi validi per la regola", "ViewCodeSnippets": "Visualizza frammenti di codice", "ViewForm": "Visualizza modulo", "ViewFormDescription": "Visualizza i dettagli del modulo e i frammenti di codice di integrazione", "FormDetails": "Dettagli del modulo", "BusinessUnit": "Unità aziendale", "Status": "Stato", "Draft": "<PERSON><PERSON>", "FormID": "ID modulo", "CodeSnippets": "Frammenti di codice", "CodeSnippetsDescription": "Utilizza i seguenti frammenti di codice per integrare questo modulo DSR nella tua applicazione mobile", "MobileSDKCode": "Codice SDK mobile", "WebLink": "Link URL Web", "FormPreview": "Anteprima del modulo", "CodeSnippet": "Frammento di codice", "RulesAppliedSuccessfully": "Regole applicate con successo", "OptionRemovedSuccessfully": "Opzione rimossa con successo!", "FailedToRemoveOption": "Impossibile rimuovere l'opzione", "CustomerIDAndFormIDRequired": "ID cliente e ID modulo richiesti", "FailedToSaveFormContent": "Salvataggio del contenuto del modulo non riuscito", "URLCopiedToClipboard": "URL copiato negli appunti", "PublicURLGenerated": "URL pubblica generata", "FailedToGeneratePublicURL": "Generazione dell'URL pubblica non riuscita", "FormSubmissionFailed": "Invio del modulo non riuscito", "TranslateForm": "Traduci modulo", "DoYouWantToTranslateThisForm": "Vuoi tradurre questo modulo?", "NoPublishNow": "No, pubblica ora", "YesTranslateForm": "<PERSON><PERSON>, traduci modulo", "SelectLanguage": "Seleziona lingua", "ChooseLanguage": "Scegli una lingua", "FormURL": "URL del modulo", "Cancel": "<PERSON><PERSON><PERSON>", "Translating": "Traduzione in corso...", "StartTranslation": "Inizia traduzione"}, "WorkFlow": {"WorkFlow": "Flusso di lavoro", "EnterFlowType": "Inserisci tipo di flusso", "AddWorkflow": "Aggiungi flusso di lavoro", "TaskTitle": "<PERSON><PERSON> del compito", "Department": "Dipartimento", "AddTask": "Aggiungi compito", "StartDate": "Data di inizio", "DueDate": "Data di scadenza", "AddAssignee": "Aggiungi assegnatario", "GuidanceText": "Testo di orientamento", "AddFiles": "Aggiungi file", "TaskChecklist": "Lista di controllo attività", "UploadedDocuments": "Documenti caricati", "ConfirmDeleteFile": "Sei sicuro di voler eliminare questo file?", "DeleteFile": "Elimina file", "Yes": "Sì", "SelectEntity": "Seleziona entità", "SelectRegulations": "Seleziona regolamenti"}, "AssigneeModal": {"Assignee": "Assegnatar<PERSON>", "FlowType": "<PERSON><PERSON><PERSON> di flusso", "DSRForm": "Modulo di richiesta del soggetto dei dati", "PublishForm": "Pubblica modulo"}}, "Cookies": {"Interaction Count": "Conteggio interazioni", "DownloadReport": "Scarica rapporto", "View": "Visualizza banner", "Source": "Fonte", "ScanFinished": "Scansione completata", "IsThisTheDefaultBanner": "Questo è il banner predefinito?", "SelectCategory": "Seleziona categoria", "IfThisIsTheDefaultBanner": "Se questo è il banner predefinito, verrà mostrato a tutti gli utenti che visitano il dominio quando la regione non è specificata.", "DefaultBanner": "<PERSON> predefinito", "PolicyUpdatedSuccessfully": "Politica aggiornata con successo", "FailedToAddPolicy": "Errore nell'aggiunta della politica", "FailedToUpdatePolicy": "Errore nell'aggiornamento della politica", "PolicyAddedSuccessfully": "Politica aggiunta con successo", "Category": "Categoria", "AllCategories": "<PERSON><PERSON> le categorie", "No Interaction Count": "Conteggio senza interazioni", "Last Interaction": "Ultima interazione", "Cookie Name": "Nome del cookie", "Cookie Value": "<PERSON>ore del <PERSON>", "Cookie Path": "Percorso del cookie", "Consent Count": "Conteggio consensi", "Declined Count": "Conteggio rifiuti", "SubjectIdentity": "Identità del soggetto", "GeoLocation": "Geolocalizzazione", "CookieCategory": "Categoria di cookie", "Consent Status": "Stato del consenso", "Consent Date": "Data del consenso", "DomainURL": "URL del dominio", "Domain/Subdomain": "Dominio/Sottodominio", "Domain Group": "Gruppo di domini", "Last Scanned": "Ultima scansione", "Scan Frequency": "Frequenza di scansione", "Next Scan": "Prossima scansione", "Total Cookies": "Totale cookie", "NoResults": "<PERSON><PERSON><PERSON> r<PERSON>.", "Cookie Policy": "Politica sui cookie", "Cookie Consent Domain": "Dominio di consenso cookie", "Banner Published": "Banner pubblicato", "Consent Policy": "Politica del consenso", "Owner": "Proprietario", "NoData": "<PERSON><PERSON><PERSON> dato", "Cookie Configuration": "Configurazione cookie", "All Domains": "<PERSON><PERSON> i domini", "Basic Information": "Informazioni di base", "Website Scan": "Scansione del sito web", "Categorize Cookie": "Categorie cookie", "Customize Banner": "Personalizza banner", "Language Support": "Supporto linguistico", "Consent Code": "Codice di consenso", "AutoScan": "Scansione automatica", "ScanDescription": "Scansiona il tuo sito web per ottenere un rapporto dettagliato sui cookie.", "ScanNow": "Scansiona ora", "Scanning": "Scansione in corso...", "ScanStarted": "Scansione avviata con successo", "ScanFailed": "Scansione fallita", "SelectFrequency": "Seleziona frequenza", "Weekly": "<PERSON><PERSON><PERSON><PERSON>", "Monthly": "<PERSON><PERSON><PERSON>", "Yearly": "Annuale", "Daily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Service": "<PERSON><PERSON><PERSON>", "Cookie": "<PERSON><PERSON>", "AddCategory": "Aggiungi categoria", "AddServices": "Aggiungi servizi", "AddCookies": "Aggiungi cookie", "ErrorOccurred": "Si è verificato un errore imprevisto.", "Consent": "Consenso", "Details": "<PERSON><PERSON><PERSON>", "About": "Informazioni", "ThisWebsiteUsesCookies": "Questo sito web utilizza i cookie", "BannerDescription": "Utilizziamo i cookie per personalizzare contenuti e annunci, per fornire funzionalità dei social media e per analizzare il nostro traffico. Condividiamo anche informazioni sul tuo utilizzo del nostro sito con i nostri partner di social media, pubblicità e analisi che potrebbero combinarle con altre informazioni che hai fornito loro o che hanno raccolto dal tuo utilizzo dei loro servizi.", "OnlyNecessary": "Solo necessari", "AllowSelection": "Consenti selezione", "AllowAll": "<PERSON><PERSON>i tutti", "AboutSectionContent": "I cookie sono piccoli file di testo che possono essere utilizzati dai siti web per rendere più efficiente l'esperienza dell'utente. La legge afferma che possiamo memorizzare i cookie sul tuo dispositivo se sono strettamente necessari per il funzionamento di questo sito. Per tutti gli altri tipi di cookie abbiamo bisogno del tuo permesso. Questo sito utilizza diversi tipi di cookie. Alcuni cookie sono inseriti da servizi di terze parti che compaiono sulle nostre pagine. Puoi in qualsiasi momento modificare o revocare il tuo consenso dalla Dichiarazione sui cookie sul nostro sito web. Scopri di più su chi siamo, come puoi contattarci e come trattiamo i dati personali nella nostra Informativa sulla privacy. Si prega di indicare l'ID del consenso e la data quando ci contatti in merito al tuo consenso.", "DefaultBannerError": "Deve esserci esattamente un banner predefinito. Si prega di regolare la selezione.", "ChooseRegulation": "Scegli regolamento", "CreateOrUseExisting": "Vuoi creare una nuova", "Existing": "E<PERSON><PERSON>", "New": "Nuova", "SelectOrCreatePolicy": "Seleziona una politica sui cookie esistente o scegli di crearne una nuova.", "SelectVersion": "Seleziona una versione", "SelectCookiePolicy": "Seleziona una politica sui cookie", "Version": "Versione", "TranslatedLanguages": "<PERSON>ue tradotte", "SavingTranslation": "Salvataggio della traduzione...", "TranslationSaved": "Traduzione salvata con successo", "SaveTranslation": "Salva traduzione", "UnableToTranslate": "Impossibile tradurre i dati", "DeviceInfo": "Informazioni sul dispositivo", "UnableToFetchData": "Impossibile recuperare i dati"}, "DataBreach": {"Dashboard": {"TotalBreaches": "Violazioni totali", "OpenBreaches": "Violazioni aperte", "ClosedBreaches": "Violazioni chiuse"}}, "CookiePolicy": {"CookiePolicyName": "Politica sui Cookie", "Version": "Versione", "UpdatedOn": "Aggiornato il", "Action": "Azione", "CopiedToClipboard": "URL copiato negli appunti!", "Search": "Cerca", "Create": "<PERSON><PERSON>", "Cookie Policy": "Politica sui Cookie", "View Policy": "Visualizza Politica", "Edit Policy": "Modifica Politica", "Create Policy": "Crea Politica", "SelectEntity": "Seleziona entità"}, "CookieCenter": {"Sections": "Sezioni", "NoSectionsAvailable": "Nessuna sezione disponibile", "NewSectionTitle": "Titolo della Nuova Sezione", "AddSection": "Aggiungi Sezione", "SelectSectionFromList": "Seleziona una sezione dall'elenco per modificarne il contenuto", "NoContentYet": "Nessun contenuto ancora. Clicca sul pulsante modifica per aggiungere contenuto.", "SaveChanges": "<PERSON><PERSON>", "UpdateChanges": "Aggiorna Modifiche", "Save": "<PERSON><PERSON>", "ErrorSelectEntity": "Seleziona un'entità per continuare.", "ErrorFillTitle": "Per favore, inserisci il titolo.", "ErrorAddSection": "Aggiungi almeno una sezione.", "UpdatingPolicy": "Aggiornamento della politica...", "AddingPolicy": "Aggiunta della politica...", "DoYouWantNewVersion": "Vuoi creare una nuova versione di questa politica?", "No": "No", "Yes": "Si", "DiscardChanges": "<PERSON><PERSON><PERSON> le modifiche"}, "CookiePolicyEditor": {"EnterCookiePolicyTitle": "Inserisci il titolo della politica dei cookie", "SelectEntity": "Seleziona entità"}, "ConsentCode": {"TestingCode": "Codice di Test", "TestCodeDescription": "Questo codice di test è progettato per essere utilizzato nel tuo sito web di staging prima di distribuirlo nel tuo sito web di produzione.", "Copied": "Copiato", "TestCodeAddCode": "Aggiungi il seguente codice all'inizio del tag <head>:", "DeployedCode": "Codice Distribuito", "WhenShouldICopyThis": "Quando dovrei copiare questo?", "CookiePolicyURL": "URL della Politica sui Cookie", "CookiePolicyURLDescription": "Per integrare la politica sui cookie nel tuo sito web, copia questo URL e incollalo nella sezione del piè di pagina del tuo sito web.", "IntegrateCookiePolicy": "Per integrare la politica sui cookie nel tuo sito web, copia questo URL e incollalo nella sezione del piè di pagina del tuo sito web."}, "CommonErrorMessages": {"Sending": "Invio in corso", "AddingWorkflowNewStep": "Aggiunta nuovo passaggio al flusso di lavoro...", "UpdatingTaskAutomation": "Aggiornamento automazione attività...", "RemovingAutomation": "Rimozione automazione...", "UpdatingTask": "Aggiornamento attività...", "DownloadingAuditLog": "Download del registro di controllo...", "SendingConsentLink": "Invio link di consenso...", "Saving": "Salvataggio in corso...", "VerifyingEmail": "Verifica dell'email...", "AccessExpired": "L'accesso è scaduto. Si prega di rinnovare l'abbonamento.", "AccountLocked": "L'account è stato bloccato. Si prega di contattare il supporto.", "AccountNotVerified": "L'account non è verificato. Si prega di controllare la propria email.", "AnErrorOccurred": "Si è verificato un errore", "AnUnexpectedErrorOccurred": "Si è verificato un errore imprevisto.", "AnswerSavedSuccessfully": "Risposta salvata con successo", "AssessmentCreatedSuccessfully": "Valutazione creata con successo", "AssessmentReviewedSuccessfully": "Valutazione rivista con successo!", "AssigningUserFailed": "Assegnazione utente fallita!", "AuthenticationRequired": "È richiesta l'autenticazione.", "AuthorizationFailed": "Autorizzazione fallita.", "BadRequest": "Richiesta errata. Si prega di controllare l'input.", "BandwidthExceeded": "Limite di larghezza di banda superato.", "CPULimitExceeded": "Limite CPU superato.", "CacheError": "Errore operazione cache.", "CalculationError": "Errore di calcolo.", "CertificateError": "Errore di validazione certificato.", "CheckConstraintViolation": "Violazione vincolo di controllo.", "ChooseAtLeastOneCollaborator": "scegli almeno un collaboratore", "CircuitBreakerOpen": "Il circuit breaker è aperto.", "CompressionError": "Errore di compressione dati.", "ConcurrentModification": "La risorsa è stata modificata da un altro utente.", "ConfirmDelete": "Sei sicuro di voler eliminare questo elemento?", "ConfirmSave": "Sei sicuro di voler salvare le modifiche?", "ConfirmUpdate": "Sei sicuro di voler aggiornare questo elemento?", "Conflict": "Si è verificato un conflitto. La risorsa potrebbe essere stata modificata.", "ConnectionLost": "Connessione persa. Si prega di controllare la connessione internet.", "ConnectionRefused": "Connessione rifiutata dal server.", "ConversionError": "Errore di conversione dati.", "CouldntAddVendor": "Impossibile aggiungere il fornitore", "CouldntSaveAnswers": "Impossibile salvare le risposte", "CouldntSaveReviews": "Impossibile salvare le recensioni", "CouldntStartAssessment": "Impossibile avviare la valutazione", "CouldntDownloadAssessment": "Impossibile scaricare la valutazione", "CouldntUpdateVendor": "Impossibile aggiornare il fornitore", "DNSError": "Errore di risoluzione DNS.", "DataDeletedSuccessfully": "Dati eliminati con successo", "DataSavedSuccessfully": "<PERSON>ti salvati con successo", "DataTruncation": "Errore di troncamento dati.", "DataUpdatedSuccessfully": "<PERSON>ti aggiornati con successo", "DatabaseError": "Si è verificato un errore del database.", "DateTimeError": "Errore formato data/ora.", "DecryptionError": "Errore di decrittazione dati.", "DeleteFailed": "Eliminazione fallita", "DependencyError": "Impossibile completare l'operazione a causa di dipendenze.", "DeserializationError": "Errore di deserializzazione dati.", "DivisionByZero": "Errore di divisione per zero.", "DomainError": "Errore di dominio matematico.", "DuplicateKey": "Errore chiave duplicata.", "EmailAlreadyExists": "L'indirizzo email esiste già.", "EmailNotFound": "Indirizzo email non trovato.", "EncodingError": "Errore di codifica caratteri.", "EncryptionError": "Errore di crittografia dati.", "EntityIdRequiredForDeletion": "ID entità richiesto per l'eliminazione", "Error": "Errore", "ErrorDot": "Errore.", "ErrorSubmittingForm": "Errore nell'invio del modulo", "ExecutionTimeout": "Timeout di esecuzione.", "ExternalServiceError": "Si è verificato un errore del servizio esterno.", "FailedToCreateAssessment": "Creazione valutazione fallita", "FailedToDeleteData": "Eliminazione dati fallita", "FailedToFetch": "<PERSON><PERSON><PERSON> fallito", "FailedToFetchData": "<PERSON><PERSON><PERSON> dati fallito.", "FailedToSaveData": "Salvatag<PERSON> dati fallito", "FailedToUpdateData": "Aggiornamento dati fallito", "FeatureNotAvailable": "Questa funzionalità non è disponibile nel tuo piano attuale.", "FileSizeExceeded": "La dimensione del file supera il limite massimo", "FileUploadFailed": "Caricamento file fallito", "FileUploadSuccessful": "File caricato con successo", "FirewallBlocked": "Richie<PERSON> bloccata dal firewall.", "ForeignKeyConstraint": "Violazione vincolo chiave esterna.", "FormSubmittedSuccessfully": "<PERSON><PERSON><PERSON> inviato con successo", "FormatError": "Errore formato dati.", "HashingError": "Errore di hashing dati.", "HostUnreachable": "Host irraggiungibile.", "IntegrityConstraintViolation": "Violazione vincolo integ<PERSON> da<PERSON>.", "InvalidConfiguration": "Configurazione non valida rilevata.", "InvalidCredentials": "Nome utente o password non validi.", "InvalidFileFormat": "Formato file non valido", "InvalidInput": "Input non valido fornito", "InvalidOperation": "Operazione matematica non valida.", "InvalidState": "Stato non valido per questa operazione.", "InvalidToken": "Token non valido o scaduto.", "LoadBalancerError": "Errore load balancer.", "LoadingData": "Caricamento...", "LoadingTranslations": "Caricamento traduzioni...", "MaintenanceMode": "Il sistema è in manutenzione. Si prega di riprovare più tardi.", "MappingDeletedSuccessfully": "Mappatura eliminata con successo", "MappingError": "Errore di mappatura dati.", "MemoryLimitExceeded": "Limite memoria superato.", "MessageExpired": "Il messaggio è scaduto.", "NetworkTimeout": "Errore timeout di rete.", "NotFound": "La risorsa richiesta non è stata trovata.", "NumericOverflow": "Errore overflow numerico.", "OperationFailed": "Operazione fallita", "OperationNotAllowed": "Questa operazione non è consentita.", "OperationSuccessful": "Operazione completata con successo", "OverflowError": "Errore di overflow.", "ParsingError": "Errore di parsing dati.", "PartialFailure": "Operazione completata con fallimenti parziali.", "PasswordMismatch": "Le password non corrispondono.", "PermissionDenied": "Non hai il permesso di eseguire questa azione", "PleaseAgreeToAllRequiredConsents": "Si prega di accettare tutti i consensi richiesti prima di inviare.", "PleaseEnterCorrectURL": "Si prega di inserire l'URL corretto", "PleaseFilTheInput": "Si prega di compilare l'input", "PleaseReviewAllQuestions": "Si prega di rivedere tutte le domande!", "PortClosed": "La porta è chiusa o bloccata.", "PrecisionLoss": "Errore di perdita di precisione.", "ProcessCompleted": "Processo completato con successo!", "Processing": "Elaborazione", "ProcessingEllipsis": "Elaborazione...", "ProcessingIn": "Elaborazione in...", "ProgressBarError": "Errore barra di progresso!", "ProtocolError": "Errore di protocollo.", "ProxyError": "Errore server proxy.", "QuestionDeletedSuccessfully": "La domanda è stata eliminata con successo", "QueueFull": "La coda messaggi è piena.", "QuotaExceeded": "Quota superata. Si prega di aggiornare il piano.", "RangeError": "Errore di intervallo matematico.", "RateLimited": "Limite di velocità richieste.", "RequiredFieldsMissing": "Si prega di compilare tutti i campi obbligatori", "ResourceExhausted": "Risorse di sistema esaurite.", "ResourceLocked": "La risorsa è attualmente bloccata da un altro utente.", "RetryLimitExceeded": "Limite tentativi superato.", "ReviewSavedSuccessfully": "Recensione salvata con successo", "RoundingError": "Errore di arrotondamento.", "SSLError": "Errore connessione SSL/TLS.", "SavedSuccessfully": "Salvato con successo!", "SendingOnboardingDataFailed": "<PERSON>vio dati onboarding fallito!", "SerializationError": "Errore di serializzazione dati.", "ServerError": "Si è verificato un errore del server. Si prega di riprovare più tardi.", "ServiceDegraded": "Il servizio è in esecuzione in modalità degradata.", "ServiceUnavailable": "Il servizio è temporaneamente non disponibile.", "SessionTimeout": "La tua sessione è scaduta. Si prega di effettuare nuovamente l'accesso.", "SignatureError": "Errore firma digitale.", "SomethingWentWrong": "Qualcosa è andato storto", "SpecialCharacterNotAllowed": "carattere speciale non consentito", "StorageQuotaExceeded": "Quota di archiviazione superata.", "Success": "Successo!", "TextFieldAdded": "Campo di testo aggiunto", "TimeoutError": "Timeout richiesta. Si prega di riprovare.", "TooManyRequests": "Troppe richieste. Si prega di riprovare più tardi.", "TransformationError": "Errore di trasformazione dati.", "UnderflowError": "Errore di underflow.", "UnsavedChanges": "Hai modifiche non salvate. Sei sicuro di voler uscire?", "UserAssignedSuccessfully": "Utente assegnato con successo!", "UserNotFound": "Utente non trovato.", "ValidationError": "Si è verificato un errore di validazione.", "VendorAddedSuccessfully": "Fornitore aggiunto con successo", "VendorUpdatedSuccessfully": "Fornitore aggiornato con successo", "VersionMismatch": "Errore di discrepanza versione.", "WeakPassword": "La password è troppo debole. Si prega di scegliere una password più forte."}, "FrontEndErrorMessage": {"AccountSetup": {"CompanyStructure": {"BusinessUnitCreatedSuccessfully": "Unità aziendale creata con successo", "DepartmentCreatedSuccessfully": "Dipartimento creato con successo", "DepartmentsCreatedSuccessfully": "Dipartimenti creati con successo.", "FailedToCreateBusinessUnit": "Creazione unità aziendale fallita", "FailedToCreateDepartment": "Creazione dipartimento fallita", "FailedToCreateProcess": "Creazione processo fallita", "GroupIdRequired": "ID gruppo richiesto ma non determinabile", "NameIsRequired": "Il nome è obbligatorio", "PleaseFillInField": "Si prega di compilare il {field}", "PleaseSelectRegionField": "Si prega di selezionare il campo regione", "PleaseUploadCSVFile": "Si prega di caricare un file CSV", "ProcessCreatedSuccessfully": "Processo creato con successo"}, "RoleManagement": {"FailedToAddRole": "Aggiunta ruolo fallita", "FailedToDeleteRole": "Eliminazione ruolo fallita", "FailedToUpdateRole": "Aggiornamento ruolo fallito", "RoleAddedSuccessfully": "<PERSON><PERSON>lo aggiunto con successo!", "RoleDeletedSuccessfully": "Ruolo eliminato con successo!", "RoleUpdatedSuccessfully": "<PERSON><PERSON>lo aggiornato con successo!"}, "UserManagement": {"FailedToAddUser": "Aggiunta utente fallita", "FailedToDeleteUser": "Eliminazione utente fallita", "FailedToUpdateUser": "Aggiornamento utente fallito", "Processing": "Elaborazione...", "ProcessingIn": "Elaborazione in...", "UserAddedSuccessfully": "Utente aggiunto con successo!", "UserDeletedSuccessfully": "Utente eliminato con successo!", "UserUpdatedSuccessfully": "Utente aggiornato con successo!"}}, "ApiErrors": {"AccessDenied": "Accesso negato. Potresti non avere il permesso di accedere a questa risorsa.", "AnErrorOccurred": "Si è verificato un errore", "BadRequest": "<PERSON><PERSON> errata", "ErrorReportSentSuccessfully": "Rapporto errore inviato con successo.", "ForbiddenError": "Errore 403 Vietato", "InternalServerError": "Errore interno del server", "NetworkError": "Errore di rete.", "NotFound": "Risorsa non trovata", "ServiceUnavailable": "Servizio non disponibile", "SomethingWentWrong": "Qualcosa è andato storto.", "UnexpectedErrorOccurred": "Si è verificato un errore imprevisto.", "SomeUpdatesFailed": "Alcuni aggiornamenti sono falliti"}, "AssessmentManagement": {"AssessmentCreatedSuccessfully": "Valutazione creata con successo", "AssessmentDeletedSuccessfully": "Valutazione eliminata con successo", "AssessmentUpdatedSuccessfully": "Valutazione aggiornata con successo", "ErrorInCreatingQuestion": "Errore nella creazione della domanda", "FailedToCreateAssessment": "Creazione valutazione fallita", "FailedToDeleteAssessment": "Eliminazione valutazione fallita", "FailedToUpdateAssessment": "Aggiornamento valutazione fallito"}, "Authentication": {"CodeSentSuccessfully": "Codice inviato con successo!", "FailedToResendOTP": "<PERSON><PERSON><PERSON>", "FailedToVerifyOTP": "Verifica OTP fallita. Si prega di riprovare.", "InvalidCredentials": "Credenziali non valide", "InvalidOTPPleaseTryAgain": "OTP non valido. Si prega di riprovare.", "LoggingIn": "Accesso in corso...", "LoginSuccessful": "<PERSON><PERSON> r<PERSON>", "LogoutSuccessful": "Disconnessione riuscita", "OTPVerifiedSuccessfully": "OTP verificato con successo!", "PasswordUpdatedSuccessfully": "Password aggiornata con successo!", "PasswordsDoNotMatch": "Le password non corrispondono!", "PleaseAgreeTermsConditions": "Si prega di accettare i nostri termini e condizioni", "PleaseEnterValidOTP": "Si prega di inserire un OTP valido", "PleaseProvidePassword": "Si prega di fornire una password!", "SendingCode": "Invio codice...", "SessionExpired": "Sessione scaduta", "UnauthorizedAccess": "Accesso non autorizzato", "VerificationCodeResentSuccessfully": "Codice di verifica reinviato con successo!"}, "CookieManagement": {"DefaultBannerError": "Deve esserci esattamente un banner predefinito. Si prega di regolare la selezione.", "FailedToAddPolicy": "Aggiunta politica fallita", "FailedToUpdatePolicy": "Aggiornamento politica fallito", "PolicyAddedSuccessfully": "Politica aggiunta con successo", "PolicyUpdatedSuccessfully": "Politica aggiornata con successo", "SavingTranslation": "Salvataggio traduzione...", "ScanFailed": "Scansione fallita", "ScanStartedSuccessfully": "Scansione avviata con successo", "TranslationSavedSuccessfully": "Traduzione salvata con successo", "UnableToFetchData": "Impossibile recuperare i dati", "UnableToTranslateData": "Impossibile tradurre i dati", "AutoScanRequired": "È richiesta la scansione automatica.", "NoGTMConfigurationToApply": "Nessuna configurazione GTM da applicare", "AdobeLaunchSettingsAppliedToAll": "Impostazioni Adobe Launch applicate a tutte le regole", "NoAdobeLaunchConfigurationToApply": "Nessuna configurazione Adobe Launch da applicare"}, "DSR": {"FailedToCreateRequest": "Creazione richiesta fallita", "FailedToDeleteRequest": "Eliminazione richiesta fallita", "FailedToPublishForm": "Pubblicazione modulo fallita", "FailedToUpdateRequest": "Aggiornamento richiesta fallito", "FormPublishedSuccessfully": "<PERSON><PERSON><PERSON> pubblicato con successo", "RequestCreatedSuccessfully": "<PERSON><PERSON> creata con successo", "RequestDeletedSuccessfully": "Richiesta eliminata con successo", "RequestUpdatedSuccessfully": "Richiesta aggiornata con successo", "DocumentsAddedSuccessfully": "Documenten succesvol toegevoegd!", "DocumentDeletedSuccessfully": "Document succesvol verwijderd!", "FailedToDeleteDocument": "Verwijderen van document mislukt. Probeer het opnieuw.", "FailedToDownloadFile": "Download<PERSON> van bestand mislukt. Probeer het opnieuw.", "SubjectIsRequired": "Onderwerp is verplicht", "EmailContentIsRequired": "E-mailinhoud is verplicht", "SubmittingForm": "Formulier verzenden...", "FormSubmittedSuccessfully": "Formulier succesvol verzonden", "FailedToSubmitForm": "<PERSON><PERSON><PERSON><PERSON> van formulier mislukt", "UploadingLogo": "Logo uploaden...", "LogoUploadedSuccessfully": "Logo succesvol geüpload", "FailedToUploadLogo": "Uploaden van logo mislukt", "TemplateDeletedSuccessfully": "Sjabloon succesvol verwijderd!", "DocumentUploadedSuccessfully": "Document succesvol geüpload!", "FailedToUploadDocument": "Uploaden van document mislukt", "DocumentsDeletedSuccessfully": "Documenten succesvol verwijderd!", "RequestApprovedSuccessfully": "Aanvraag succesvol goedgekeurd!", "RequestRejectedSuccessfully": "Aanvraag succesvol afgewezen!", "DeadlineExtendedSuccessfully": "Deadline succesvol verlengd!", "AuditLogDownloading": "Auditlogboek downloaden...", "ProcessingEllipsis": "Elaborazione in corso...", "UpdatingTask": "Aggiornamento attività...", "TaskUpdatedSuccessfully": "Attività aggiornata con successo", "ErrorUpdatingTask": "Errore nell'aggiornamento dell'attività", "ErrorLoadingData": "Errore nel caricamento dei dati", "MessageCannotBeEmpty": "Il messaggio non può essere vuoto.", "RequestUnderVerification": "La tua richiesta è in fase di verifica", "ErrorFetchingAssignees": "Errore durante il recupero degli assegnatari", "PleaseSelectAssignee": "Seleziona un assegnatario", "FailedToAssignUser": "Assegnazione dell'utente non riuscita", "ErrorAssigningUser": "Errore durante l'assegnazione dell'utente", "PleaseEnterEmail": "Inser<PERSON>ci un'email.", "VerificationCodeSent": "Codice di verifica inviato con successo!", "SomethingWrong": "Qualcosa è andato storto!", "FailedToResendVerificationCode": "Impossibile reinviare il codice di verifica.", "FailedToResendVerificationCodeTryAgain": "Impossibile reinviare il codice. Riprova.", "InvalidOTPTryAgain": "OTP non valido. Riprova.", "BusinessUnitUpdated": "Unità aziendale aggiornata con successo", "Success": "Successo", "FailedToUpdateTask": "Aggiornamento dell'attività non riuscito", "CannotDeleteLastStep": "Impossibile eliminare l'ultimo passaggio del workflow", "WorkflowStepDeletedSuccessfully": "Passaggio del workflow eliminato con successo", "FailedToDeleteWorkflowStep": "Eliminazione del passaggio del workflow non riuscita", "ErrorDeletingWorkflowStep": "Errore durante l'eliminazione del passaggio del workflow", "StepTitleCannotBeEmpty": "Il titolo del passaggio non può essere vuoto", "WorkflowIdMissing": "ID del workflow mancante", "Processing": "Elaborazione in corso...", "WorkflowUpdatedSuccessfully": "Workflow aggiornato con successo", "InvalidAPIRoute": "Percorso API non valido.", "FailedToUpdateWorkflow": "Aggiornamento del workflow non riuscito. Riprova più tardi.", "TitleCannotBeEmpty": "Il titolo non può essere vuoto", "ControlUpdatedSuccessfully": "<PERSON>lo aggiornato con successo", "FailedToUpdateControl": "Aggiornamento del controllo non riuscito", "TitleRequired": "Il titolo è obbligatorio", "FailedToAddQuestion": "Aggiunta della domanda non riuscita", "QuestionAddedSuccessfully": "Domanda aggiunta con successo", "FailedToAddWorkflow": "Aggiunta del workflow non riuscita", "WorkflowAddedSuccessfully": "Workflow aggiunto con successo", "AddingWorkflow": "Aggiunta del workflow", "DuplicateWorkflow": "Workflow duplicato", "PleaseEnterWorkflowType": "Inserisci il tipo di workflow", "WorkflowCreatedSuccessfully": "Workflow creato con successo", "FailedToCreateWorkflow": "Creazione del workflow non riuscita", "ErrorCreatingWorkflow": "Errore durante la creazione del workflow", "SubmittedSuccessfully": "Inviato con successo", "ProcessingRequest": "Elaborazione della richiesta", "FiltersAppliedSuccessfully": "Filtri applicati con successo", "AddingTask": "Aggiunta attività", "TaskAddedSuccessfully": "Attività aggiunta con successo", "FailedToAddTask": "Aggiunta dell'attività non riuscita", "UserUnauthaorized": "User unauthorized"}, "DataMapping": {"FailedToAddPii": "Aggiunta PII fallita", "FailedToDeletePii": "Eliminazione PII fallita", "FailedToUpdatePii": "Aggiornamento PII fallito", "PiiAddedSuccessfully": "PII aggiunto con successo", "PiiAlreadyExists": "PII già esistente", "PiiDeletedSuccessfully": "PII eliminato con successo", "PiiUpdatedSuccessfully": "PII aggiornato con successo"}, "DataValidation": {"DataValidationFailed": "Validazione dati fallita", "DuplicateEntryFound": "Voce duplicata trovata", "InvalidDataFormat": "Formato dati non valido", "InvalidDateFormat": "Formato data non valido", "InvalidNumberFormat": "Formato numero non valido", "RequiredDataMissing": "<PERSON><PERSON> obbligatori mancanti", "ValueOutOfRange": "<PERSON><PERSON> fuori intervallo"}, "ErrorBoundary": {"ErrorCaughtByErrorBoundary": "Errore catturato da ErrorBoundary", "ErrorDetails": "<PERSON><PERSON><PERSON> errore", "PageCrashed": "Questa pagina è andata in crash", "RefreshPage": "Aggiorna Pagina", "ReportIssue": "<PERSON><PERSON><PERSON>", "SomethingWentWrong": "Qualcosa è andato storto", "UnexpectedError": "Si è verificato un errore imprevisto"}, "FileUpload": {"FailedToUploadFile": "Caricamento file fallito", "FileSizeTooLarge": "La dimensione del file è troppo grande", "FileUploadFailed": "Caricamento file fallito", "FileUploadedSuccessfully": "File caricato con successo", "InvalidFileType": "Tipo di file non valido", "MaxFileSizeExceeded": "Dimensione massima file superata", "PleaseSelectFile": "Si prega di selezionare un file", "UploadInProgress": "Caricamento in corso...", "UploadingFile": "Caricamento file...", "FailedToDownloadSampleFile": "Impossibile scaricare il file di esempio", "FailedToImportFile": "Impossibile importare il file"}, "FormValidation": {"AddressRequired": "L'indirizzo è obbligatorio", "ChooseCategory": "Seleziona almeno una categoria", "QuestionScope": "Por favor, seleccione el alcance de la pregunta", "AdminNameRequired": "Il nome dell'amministratore è obbligatorio", "AuthenticationTypeRequired": "Il tipo di autenticazione è obbligatorio quando la verifica è abilitata", "BusinessUnitRequired": "Questo campo è obbligatorio", "CountryRequired": "Il paese è obbligatorio", "CustomerNameRequired": "Il nome del cliente è obbligatorio", "ExpiryIsRequired": "La scadenza è obbligatoria!", "FormNameRequired": "Questo campo è obbligatorio", "FrequencyValueRequired": "Il valore di frequenza è obbligatorio quando la frequenza è abilitata!", "IndustryRequired": "Il settore è obbligatorio", "InvalidAdminEmail": "Indirizzo email amministratore non valido", "InvalidEmailAddress": "Indirizzo email non valido", "InvalidPhoneNumber": "Numero di telefono non valido", "MustAgreeToTerms": "Devi accettare i termini e le condizioni", "PhoneNumberIsRequired": "Il numero di telefono è obbligatorio", "PleaseEnterValidEmailAddress": "Si prega di inserire un indirizzo email valido", "PleaseProvideEmailAndPassword": "Si prega di fornire sia email che password.", "PleaseProvidePassword": "Si prega di fornire una password.", "RegulationRequired": "Deve essere selezionata almeno una regolamentazione", "ResourceRequired": "Deve essere selezionata almeno una risorsa", "TenDigitsRequired": "Sono richieste 10 cifre", "ThisFieldIsRequired": "Questo campo è obbligatorio!", "UsernameMinLength": "Il nome utente deve essere di almeno 2 caratteri.", "EnterName": "Inserisci Nome", "EnterDescription": "Inserisci Descrizione", "Error": "Errore", "ErrorDot": "Errore.", "PleaseAgreeToAllRequiredConsents": "Si prega di accettare tutti i consensi richiesti.", "ErrorSubmittingForm": "Errore nell'invio del modulo", "PleaseProvideYourConsentFirst": "Si prega di fornire prima il consenso.", "PleaseInputYourEmail": "Si prega di inserire la propria email.", "PleaseEnterValidEmail": "Si prega di inserire un indirizzo email valido.", "PleaseEnterValidPhoneNumber": "Si prega di inserire un numero di telefono valido di 10 cifre.", "InvalidInputTypeSpecified": "Tipo di input non valido specificato.", "PleaseEnterValidOTP": "Si prega di inserire un OTP valido di 6 cifre", "FailedToChangePreferences": "Impossibile modificare le preferenze", "InvalidOTP": "OTP non valido", "PreferenceDataNotAvailable": "Dati delle preferenze non disponibili.", "PleaseSaveChanges": "Si prega di salvare le modifiche!"}, "HttpClient": {"ForbiddenError": "Errore 403 Vietato", "NoAccessTokenAvailable": "Nessun token di accesso disponibile per la richiesta", "RefreshTokenExpired": "Token di aggiornamento scaduto", "RequestFailed": "<PERSON><PERSON> fallita", "ResponseError": "Errore di risposta", "TokenRefreshFailed": "Aggiornamento token fallito"}, "Onboarding": {"OnboardingFailed": "Onboarding fallito", "OnboardingSuccessful": "Onboarding completato con successo", "ProcessCompletedSuccessfully": "Processo completato con successo!", "SendingOnboardingDataFailed": "<PERSON>vio dati onboarding fallito!"}, "PrivacyOps": {"FailedToAddRegulation": "Aggiunta regolamentazione fallita", "FailedToCreateRisk": "Creazione rischio fallita", "FailedToDeleteRisk": "Eliminazione rischio fallita", "FailedToUpdateRisk": "Aggiornamento rischio fallito", "RegulationAddedSuccessfully": "Regolamentazione aggiunta con successo", "RiskCreatedSuccessfully": "<PERSON><PERSON><PERSON> creato con successo", "RiskDeletedSuccessfully": "<PERSON><PERSON><PERSON> eliminato con successo", "RiskUpdatedSuccessfully": "Rischio aggiornato con successo"}, "Profile": {"FailedToSaveChanges": "Salvataggio modifiche fallito.", "PasswordUpdateFailed": "Aggiornamento password fallito!"}, "ROPA": {"PleaseAnswerAllQuestions": "Si prega di rispondere a tutte le domande prima di inviare.", "ProgressBarError": "Errore barra di progresso!", "RopaSubmittedSuccessfully": "ROPA è stato inviato con successo", "TentativeDateUpdatedSuccessfully": "Data tentativa aggiornata con successo", "RecurrenceDateUpdatedSuccessfully": "Data di ricorrenza aggiornata con successo"}, "Unauthorized": {"AccessDenied": "Accesso negato", "ContactAdministrator": "Si prega di contattare l'amministratore", "InsufficientPermissions": "Non hai permessi sufficienti", "LoginRequired": "Accesso richiesto per accedere a questa pagina", "RoadBlockAhead": "Blocco stradale avanti", "Whoops": "Ops!"}, "VRM": {"AccurateInformationRequired": "Sono richieste informazioni accurate.", "AssessmentReviewedSuccessfully": "Valutazione rivista con successo!", "AssessmentSubmittedSuccessfully": "Valutazione inviata con successo!", "CommentRequired": "Il commento è obbligatorio.", "CouldntSaveReview": "Impossibile salvare la recensione", "PleaseAnswerAllQuestions": "Si prega di rispondere a tutte le domande!", "PleaseReviewAllQuestions": "Si prega di rivedere tutte le domande!", "ReviewSavedSuccessfully": "Recensione salvata con successo", "RiskScoreRequired": "Il punteggio di rischio è obbligatorio.", "ProcessingIn": "Elaborazione in corso...", "CouldntStartAssessment": "Impossibile avviare la valutazione", "CouldntAddVendor": "Impossibile aggiungere il fornitore", "CouldntUpdateVendor": "Impossibile aggiornare il fornitore", "VendorAddedSuccessfully": "Fornitore aggiunto con successo", "VendorUpdatedSuccessfully": "Fornitore aggiornato con successo", "PlanSavedSuccessfully": "Piano salvato con successo", "CouldntSaveMitigationPlan": "Impossibile salvare il piano di mitigazione.", "AnErrorOccurred": "Si è verificato un errore", "DeletedSuccessfully": "Eliminato con successo", "UploadedSuccessfully": "Caricato con successo", "DownloadLinkSent": "Il link per il download è stato inviato al tuo indirizzo email. Controlla la tua posta in arrivo", "Success": "Successo", "NewVendor": "+ Nuovo fornitore", "UserAssignedSuccessfully": "Utente assegnato con successo!", "AssigningUserFailed": "Assegnazione utente fallita!", "MitigationPlanSubmittedSuccessfully": "Piano di mitigazione inviato con successo.", "QuestionUpdatedSuccessfully": "Domanda aggiornata con successo.", "QuestionDeletedSuccessfully": "La domanda è stata eliminata con successo", "Error": "Errore", "ErrorExclamation": "Errore!", "UploadValidFile": "Carica file valido", "ControlAndDescriptionRequired": "Controllo e descrizione sono richiesti.", "UploadFilesOrEnterURL": "Carica file o inserisci URL", "EnterValidURL": "Inserisci URL valido", "FailedToOpenWindow": "Impossibile aprire la finestra. Potrebbe essere stata bloccata dal browser.", "EnterTemplateName": "Inserisci nome del modello"}, "VendorManagement": {"CouldntAddVendor": "Impossibile aggiungere il fornitore", "FailedToDeleteVendor": "Eliminazione fornitore fallita", "FailedToUpdateVendor": "Aggiornamento fornitore fallito", "VendorAddedSuccessfully": "Fornitore aggiunto con successo", "VendorDeletedSuccessfully": "Forni<PERSON> eliminato con successo", "VendorUpdatedSuccessfully": "Fornitore aggiornato con successo"}, "UniversalConsentManagement": {"downloadFailed": "Download fallito", "AddedSuccessfully": "aggiunto con successo", "CouldntCreate": "Impossibile creare", "FillRequiredFields": "Compila i campi obbligatori.", "UploadValidFile": "Carica file valido", "Processing": "Elaborazione...", "UploadedSuccessfully": "Caricato con successo", "UnexpectedError": "Si è verificato un errore imprevisto", "ProcessingIn": "Elaborazione in...", "NameIsRequired": "Il nome è obbligatorio", "DescriptionIsRequired": "La descrizione è obbligatoria", "CategoryIsRequired": "La categoria è obbligatoria", "DataRetentionIsRequired": "La conservazione dei dati è obbligatoria", "LawfulBasisIsRequired": "La base legale è obbligatoria", "PurposeIsRequired": "Lo scopo è obbligatorio", "TypeIsRequired": "Il tipo è obbligatorio", "ComplianceOfficerIsRequired": "Il responsabile della conformità è obbligatorio", "DataSubjectIsRequired": "Il soggetto dei dati è obbligatorio", "SourceIsRequired": "La fonte è obbligatoria", "DataImporterIsRequired": "L'importatore di dati è obbligatorio", "DataExporterIsRequired": "L'esportatore di dati è obbligatorio", "PleaseSelectSource": "Si prega di selezionare una fonte.", "TitleDescriptionRequired": "Si prega di inserire un titolo e una descrizione per il modulo", "FormDescriptionRequired": "Si prega di inserire una descrizione per il modulo", "FormTitleRequired": "Si prega di inserire un titolo per il modulo", "PleaseSelectConsentPurpose": "Si prega di selezionare uno scopo del consenso", "FillAllRequiredFields": "Compila tutti i campi obbligatori.", "NameRequired": "Il nome è obbligatorio.", "ConsentDescriptionRequired": "La descrizione è obbligatoria.", "EnterName": "Inserisci Nome", "EnterDescription": "Inserisci Descrizione", "PleaseEnterAllFields": "Si prega di inserire tutti i campi", "ConsentPurposeUpdatedSuccessfully": "Scopo del consenso aggiornato con successo", "ConsentPurposeDeletedSuccessfully": "Scopo del consenso eliminato con successo", "ProcessingPurposeUpdatedSuccessfully": "Scopo del trattamento aggiornato con successo", "ProcessingPurposeDeletedSuccessfully": "Scopo del trattamento eliminato con successo", "PiiLabelUpdatedSuccessfully": "Etichetta Pii aggiornata con successo", "PiiLabelDeletedSuccessfully": "Etichetta Pii eliminata con successo", "RecordUpdatedSuccessfully": "Record aggiornato con successo", "PrivacyNoticeSavedSuccessfully": "Informativa sulla privacy salvata con successo", "TemplateDownloadedSuccessfully": "<PERSON><PERSON> scaricato con <PERSON>o", "TemplateActivatedSuccessfully": "<PERSON><PERSON> atti<PERSON>o con <PERSON>o", "TemplateInactivatedSuccessfully": "<PERSON><PERSON> disattivato con successo", "CouldntUpdateConsentPurpose": "Impossibile aggiornare lo scopo del consenso", "CouldntUpdatePiiLabel": "Impossibile aggiornare l'etichetta pii", "CouldntUpdateProcessingPurpose": "Impossibile aggiornare lo scopo del trattamento", "CouldntUpdateRecordData": "Impossibile aggiornare i dati del record"}}, "ucm": {"PII Label": "Etichetta PII", "Consent Purpose": "Finalità del Consenso", "Processing Purpose": "Finalità del Trattamento", "Processing Category": "Categoria di Trattamento", "Dashboard": {"ConsentStatus": "Stato del Consenso", "PurposeConsentStatus": "Stato del Consenso per Finalità", "ConsentByResidency": "Consenso per Residenza", "TotalUsers": "Utenti Totali", "TotalGrantedConsent": "Consensi Concessi", "TotalDeclinedConsent": "Consensi <PERSON>", "WithdrawnConsent": "Consenso Revocato"}, "PrivacyNotice": {"PrivacyNoticeName": "Nome dell'Informativa sulla Privacy", "Version": "Versione", "UpdatedOn": "Aggiornato il", "Action": "Azione", "Create": "<PERSON><PERSON>", "SaveDetails": "<PERSON><PERSON>", "EnterPrivacyNoticeTitle": "Inserisci il Titolo dell'Informativa sulla Privacy", "AddSection": "Aggiungi Sezione", "NoSectionsAvailable": "Nessuna sezione disponibile", "AddContent": "<PERSON>gg<PERSON><PERSON><PERSON>", "SelectToViewContent": "Seleziona una sezione per visualizzarne il contenuto", "NewContent": "Nuovo Contenuto", "NewSectionTitle": "Titolo della Nuova Sezione", "Sections": "Sezioni"}, "ProcessingCategory": {"ProcessingCategoryName": "Nome della Categoria di Trattamento", "Description": "Descrizione", "Action": "Azione", "UpdateCategory": "Aggiorna Categoria", "Name": "Nome", "AddCategory": "Aggiungi Categoria", "ProcessingCategory": "Categoria di Trattamento", "AddProcessingCategory": "Aggiungi Categoria di Trattamento", "ProcessingPurpose": "Finalità del Trattamento", "DeleteCategory": "Elimina Categoria di Trattamento", "SureToDelete": "Sei sicuro di voler eliminare questa categoria di trattamento?", "No": "No", "YesProceed": "Sì, Procedi", "EnterCategoryName": "Inserisci il Nome della Categoria", "EnterDescription": "Inserisci la Descrizione della Categoria"}, "ProcessingPurpose": {"AddProcessingPurpose": "Aggiungi Finalità del Trattamento", "ProcessingPurpose": "Finalità del Trattamento", "Description": "Descrizione", "ProcessingCategory": "Categoria di Trattamento", "Action": "Azione", "UpdateProcessingPurpose": "Aggiorna Finalità del Trattamento", "Name": "Nome", "DeleteProcessingPurpose": "Elimina Finalità del Trattamento", "SureToDelete": "Sei sicuro di voler eliminare questa finalità del trattamento?", "EnterPurposeName": "Inserisci il Nome della Finalità", "EnterDescription": "Inserisci la Descrizione della Finalità", "EnterCategoryName": "Inserisci il Nome della Categoria", "EnterCategoryDescription": "Inserisci la Descrizione della Categoria"}, "ConsentPurpose": {"AddConsentPurpose": "Aggiungi Finalità del Consenso", "ConsentPurpose": "Finalità del Consenso", "EnterConsentName": "Inserisci il Nome del Consenso", "EnterDescription": "Inserisci la Descrizione del Consenso", "Description": "Descrizione", "ProcessingPurpose": "Finalità del Trattamento", "Action": "Azione", "UpdateConsentPurpose": "Aggiorna Finalità del Consenso", "Name": "Nome", "EnterCategoryDescription": "Inserisci la Descrizione della Categoria", "EnterCategoryName": "Inserisci il Nome della Categoria", "DeleteConsent": "Elimina Finalità del Consenso", "SureToDelete": "Sei sicuro di voler eliminare questa finalità del consenso?", "RetentionType": "Tipo di conservazione", "RetentionPeriod": "Periodo di conservazione", "ExpiryPeriod": "Periodo di scadenza"}, "PII": {"AddPII": "Aggiungi PII", "PIILabel": "Etichetta PII", "EnterPIIName": "Inserisci il Nome dell'Etichetta PII", "EnterDescription": "Inserisci la Descrizione dell'Etichetta PII", "Description": "Descrizione", "UniversalIdentifier": "Identificatore Universale", "Action": "Azione", "DeletePIILabel": "Elimina Etichetta PII", "SureToDelete": "Sei sicuro di voler eliminare questa etichetta PII?", "UpdatePIILabel": "Aggiorna Etichetta PII", "Name": "Nome", "EnterPIILabelName": "Inserisci il Nome dell'Etichetta PII", "EnterPIIDescription": "Inserisci la descrizione dell'etichetta PII", "Yes": "Sì", "No": "No"}, "CollectionBuilder": {"TemplateName": "Nome del Modello", "DataIdentifierType": "Tipo di Identificatore Dati", "EntityName": "Nome dell'Entità", "Owner": "Proprietario", "CreatedDate": "Data di Creazione", "ModifiedDate": "Data di Modifica", "FormURL": "URL del Modulo di Consenso", "CenterURL": "URL del Centro Preferenze", "Status": "Stato", "Action": "Azione", "CreateTemplate": "<PERSON>rea <PERSON> di Raccolta Consensi"}, "PreferenceCenter": {"AddPreferenceCenter": "Aggiungi Centro Preferenze", "PreferenceCenterName": "Nome del Centro Preferenze", "DateandTime": "Data e Ora del Centro Preferenze", "URL": "URL", "Owner": "Proprietario", "Status": "Stato", "Actions": "Azioni", "BasicInfo": "Informazioni di Base", "Customize": "Personalizza", "Code": "Codice", "TemplateName": "Nome del Modello", "Description": "Descrizione", "SubjectIdentityType": "Tipo di Identità del Soggetto", "Template": "<PERSON><PERSON>", "OwnerEmail": "Email del Proprietario", "OwnerName": "Nome del Proprietario", "PrivacyPolicyLink": "Link alla Politica sulla Privacy"}, "SubjectConsentManager": {"Templates": "<PERSON><PERSON>", "Lists": "<PERSON><PERSON><PERSON>", "ConsentDistributionChartbyTemplate": "Distribuzione dei Consensi per Nome del Modello di Raccolta", "ConsentSourceDistribution": "Distribuzione delle Fonti di Consenso", "CollectionTemplateName": "Nome del Modello di Raccolta", "TotalUserConsents": "Totale Consensi Utente", "Status": "Stato", "Action": "Azione", "WebForm": "Modulo Web", "Form": "<PERSON><PERSON><PERSON>", "WebPreferenceCenter": "Centro Preferenze Web", "Manually": "Manuale", "PrefereceCenter": "Centro Preferenze", "API": "API", "SubjectIdentity": "Identità del Soggetto", "Source": "Fonte", "GeoLocation": "Geolocalizzazione", "ConsentedAt": "Consenso <PERSON> Il", "PIILabelName": "Nome Etichetta PII", "TotalConsents": "Totale Consensi", "WebConsents": "Consensi Web", "MobileConsents": "Consensi Mobile", "ApiConsents": "Consensi API", "ConsentStatusTypes": "Tipi di Stato del Consenso", "Granted": "Concesso", "Declined": "Rifiutato", "Withdrawn": "Revocato", "ConsentDistributionByProcessing": "Distribuzione dei Consensi per Finalità di Trattamento", "SubjectPreferenceList": "Elenco Preferenze del Soggetto", "UserLogs": "Registri Utente", "ConsentName": "Nome del Consenso", "Frequency": "Frequenza", "ConsentExpiration": "Scadenza del Consenso", "ConsentStatus": "Stato del Consenso", "Filter": "Filtro", "UserDetails": "Dettagli Utente", "ConsentFlowVisualization": "Visualizzazione del Flusso di Consenso", "NoDataAvailable": "<PERSON><PERSON><PERSON> dato disponibile", "LoadingData": "Caricamento dati...", "ListOfTemplateTable": "Elenco della Tabella dei Modelli"}}, "DPO": {"Dashboard": {"Risk": {"EntityRisks": "Rischi dell'Entità", "DataMappingRisks": "Rischi di Mappatura dei Dati", "VendorRisks": "Rischi del Fornitore", "AssessmentRisks": "Rischi di Valutazione", "RiskByCategory": "R<PERSON>io per Categoria", "RiskByStages": "<PERSON><PERSON><PERSON> per <PERSON>", "MonetaryRisk": "Rischio Monetario", "RiskByImpactProbability": "Rischio per Impatto e Probabilità", "RegulatoryComplianceBreakdown": "Dettaglio della Conformità Regolatoria", "ControlByFramework": "Controllo per Quadro Normativo", "RecentRisks": "<PERSON><PERSON><PERSON>", "RiskID": "ID del Rischio", "RiskTitle": "<PERSON><PERSON>", "RiskCategory": "Categoria del Rischio", "SourceOfRisk": "Fonte del Rischio", "DateIdentified": "Data di Identificazione", "RiskDescription": "Descrizione del Rischio", "Identification": "Identificazione", "Evaluation": "Valutazione", "Mitigation": "Mitigazione", "Closure": "<PERSON><PERSON><PERSON>", "Compliance": "Conformità", "DataBreach": "Violazione dei Dati", "Legal": "Legale", "Likelihood": "Probabilità", "Severity": "Gravità", "GDPR": "GDPR", "CCPA": "CCPA", "NIST": "NIST", "DPDPA": "DPDPA", "UAEPDPL": "PDPL EAU", "UAE": "EAU", "DPD": "DPD", "EUG": "UEG", "Controls": "<PERSON><PERSON>", "Completed": "Completato", "Pending": "In Sospeso", "Score": "<PERSON><PERSON><PERSON><PERSON>"}, "Compliance": {"ComplianceScoreByCategories": "Punteggio di Conformità per Categorie", "OverAllScore": "Punteggio <PERSON>e", "YourScore": "Il Tuo Punteggio di Conformità", "DataProtection": "Protezione dei Dati", "OrganizationControls": "<PERSON><PERSON>", "TechnologyControls": "Controlli Tecnologici", "SystemSecurity": "Sicurezza del Sistema", "RegulatoryReporting": "Report Normativi", "Policies": "Politiche", "KeyImprovementActions": "Azioni Chiave di Miglioramento", "ImprovementAction": "Azioni di Miglioramento", "Impact": "Impatto", "Status": "Stato", "Group": "Gruppo", "ActionType": "Tipo di Azione", "Completed": "Completato", "NotCompleted": "Non Completato", "OutOfScope": "<PERSON><PERSON>", "ViewImprovementActions": "Visualizza Azioni di Miglioramento", "ViewAll": "<PERSON><PERSON><PERSON>", "PointAchieved": "<PERSON><PERSON><PERSON>", "ComplianceScore": "Il punteggio di conformità misura i tuoi progressi nel completare le azioni raccomandate che aiutano a ridurre i rischi legati alla protezione dei dati e agli standard normativi.", "DataProtectionDescription": "Abilita e configura la crittografia, controlla l'accesso alle informazioni e previene la perdita o l'esfiltrazione dei dati.", "OrganizationControlsDescription": "Definisci politiche di sicurezza, responsabilità e quadri, garantendo la protezione delle risorse informative.", "TechnologyControlsDescription": "Utilizza difese automatizzate, rafforzamento del sistema e misure di integrità dei dati per applicare le politiche di sicurezza.", "SystemSecurityDescription": "Valuta le misure di sicurezza implementate nei sistemi informatici.", "RegulatoryReportingDescription": "Assicura la conformità con le normative sulla protezione dei dati e i requisiti di reporting.", "PoliciesDescription": "Valuta l'adeguatezza e l'aggiornamento delle politiche sulla privacy."}}, "ControlHandbook": {"AddCategory": "Aggiungi Categoria", "ControlCategory": "Categoria di Controllo", "AddRegulation": "Aggiungi Regolamento", "CreatedOn": "Creato il", "UpdatedOn": "Aggiornato il", "Actions": "Azioni", "ControlNo": "Numero di Controllo", "ControlDescription": "Descrizione del Controllo", "SummaryOfInScopeRegulations": "Sintesi delle Regolamentazioni in Ambito", "LoadingRegulations": "Caricamento regolamentazioni", "data_submitted_successfully": "<PERSON>ti inviati con successo", "please_fill_all_fields": "Si prega di compilare tutti i campi", "please_add_business_requirement": "Si prega di aggiungere un requisito aziendale", "category_name_required": "Nome della categoria è obbligatorio", "control_number_required": "Numero di controllo è obbligatorio", "category_number_required": "Numero della categoria è obbligatorio", "control_description_required": "Descrizione del controllo è obbligatoria", "AddControlCategory": {"CategoryName": "Nome della Categoria", "ControlDescription": "Descrizione del Controllo", "ControlNo": "Numero di Controllo", "CategoryNo": "Numero della Categoria", "EnterCategoryName": "Inserisci Nome della Categoria", "EnterControlDescription": "Inserisci Descrizione del Controllo", "EnterControlNo": "Inserisci Numero di Controllo", "EnterCategoryNo": "Inserisci Numero della Categoria", "Reference": "Riferimento", "EnterReference": "Inserisci ID di Riferimento", "BusinessRequirement": "<PERSON><PERSON><PERSON>", "EnterBusinessRequirement": "Inserisci Requisiti Aziendali", "EnterCommaSeparatedValues": "Inserisci valori separati da virgole", "SaveBusinessRequirement": "<PERSON>va <PERSON>quis<PERSON>"}, "AddRegulations": {"AddRegulation": "Aggiungi nuova regolamentazione", "FillInDetailsToAddRegulation": "Compila i dettagli per aggiungere una nuova regolamentazione.", "Geography": "Geografia", "MappingColumnHeader": "Intestazione colonna di mappatura", "Source": "Fonte", "AuthoritativeSource": "Fonte autorevole", "Version": "Versione", "URL": "URL", "Available": "Disponibile", "GroupIDs": "ID gruppi", "ISRegulationAvailable": "Questa regolamentazione è attualmente disponibile?", "SelectAssignee": "Seleziona assegnatario", "RegulationAddedSuccessfully": "Regolamentazione aggiunta con successo", "FailedToAddRegulation": "Impossibile aggiungere la regolamentazione"}}, "Regulation": {"RegulationName": "Regolamento", "CreatedOn": "Creato il", "UpdatedOn": "Aggiornato il", "ComplianceStatus": "Stato di conformità", "ViewDetails": "Visualizza <PERSON>", "SelectEntity": "Seleziona entità", "importSign": "importa segno", "RepositoryDocumentUpload": "Privacy-Ops repository document upload", "AddControlDialog": {"AddControl": "Aggiungi controllo", "ControlNumber": "Numero di controllo", "ControlDescription": "Descrizione del controllo", "ArticleNumber": "Numero dell'articolo", "RegulationSummary": "Riepilogo del regolamento"}, "RegulationDetails": {"ComplianceStatus": "Stato di conformità", "PartialComplianceStatus": "Stato di conformità parziale", "NonComplianceStatus": "Stato di non conformità"}, "NavHeadings": {"Controls": "<PERSON><PERSON>", "Documents": "Documenti", "Duties": "<PERSON><PERSON>", "Actions": "Azioni", "Improvements": "Miglioramenti"}, "EditControl": {"EditControl": "Modifica controllo", "Applicable": "Applicabile", "Document": "Documento", "SelectDocument": "Seleziona documento", "NotApplicable": "Non applicabile", "ComplianceStatus": "Stato di conformità", "Observation": "Osservazione", "AddObservation": "Aggiungi osservazione", "Select": "Seleziona", "Compliant": "Conforme", "PartiallyCompliant": "Parzialmente conforme", "NonCompliant": "Non conforme", "AddYourObservationHere": "Aggiungi qui la tua osservazione..."}, "Controls": {"ControlNumber": "Numero di controllo", "ControlDescription": "Descrizione del controllo", "RegulationSummary": "Riepilogo delle normative applicabili", "NoDataAvailable": "<PERSON><PERSON><PERSON> dato disponibile"}, "ExtendedControlTabel": {"ReferenceId": "ID di riferimento", "BusinessRequirement": "<PERSON><PERSON><PERSON>", "ArticleNumber": "Numero dell'articolo", "Applicability": "Applicabilità", "Document": "Documento", "ComplianceStatus": "Stato di conformità", "Observation": "Osservazione", "Action": "Azione", "AddToAction": "Aggiungi all'azione", "AddToDuty": "Aggiungi al dovere", "AddToImprovement": "Aggiungi al miglioramento", "Action Added Successfully": "Azione aggiunta con successo"}, "Documents": {"Document": "Documento", "Description": "Descrizione", "Category": "Categoria", "CreatedOn": "Creato il", "CreatedBy": "<PERSON><PERSON><PERSON> <PERSON>", "Attachment": "Allegato"}, "Duties": {"Overdue": "Scaduto", "NoOverdueData": "<PERSON><PERSON><PERSON> dato scaduto", "Open": "Aperto", "NoOpenData": "<PERSON><PERSON><PERSON> dato aperto"}, "Actions": {"ActionTitle": "Titolo azione", "AssignedTo": "Assegnato a", "AssignedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "AssignedDate": "Data assegnata", "Deadline": "Scadenza", "Status": "Stato", "NoResult": "<PERSON><PERSON><PERSON> r<PERSON>"}, "Improvements": {"Overdue": "Scaduto", "NoOverdueDuties": "<PERSON><PERSON><PERSON> dovere scaduto", "NoCompletedDuties": "<PERSON><PERSON><PERSON> dovere completato", "Open": "Aperto"}}, "RiskRegister": {"NavHeadings": {"Activity": "Attività", "Audit Log": "Registro di controllo"}, "Activity": {"RiskTitle": "<PERSON><PERSON>", "RiskCategory": "Categoria di rischio", "SourceOfRisk": "Fonte del rischio", "DateIdentified": "Data di identificazione", "RiskDescription": "Descrizione del rischio", "Regulation": "Regolamento", "Module": "<PERSON><PERSON><PERSON>", "Entity": "Entità", "CreateRisk": "<PERSON><PERSON> r<PERSON>"}, "RiskForm": {"RiskTitle": "<PERSON><PERSON>", "Write": "<PERSON><PERSON><PERSON>", "DescriptionOfRisk": "Descrizione del rischio", "EnterDescription": "Inserisci una descrizione...", "Module": "<PERSON><PERSON><PERSON>", "Select": "Seleziona", "RiskCategory": "Categoria di rischio", "TentativeDate": "Data provvisoria", "PickADate": "Scegli una data", "SourceOfRisk": "Fonte del rischio", "Threat": "<PERSON><PERSON><PERSON>", "Vulnerability": "Vulnerabilità", "Regulation": "Regolamento", "Entity": "Entità", "RiskCreatedSuccessfully": "<PERSON><PERSON><PERSON> creato con successo"}, "Steeper": {"SteeperHeadings": {"Identified": "Identificato", "Evaluation": "Valutazione", "Mitigation": "Mitigazione", "Closure": "<PERSON><PERSON><PERSON>", "Monitoring": "Monitoraggio"}, "EvaluationStep": {"Evaluation": "Valutazione", "Category": "Categoria", "Compliance": "Conformità", "DateClosed": "Data di Chiusura", "PickADate": "Seleziona una data", "DateCreated": "Data di Creazione", "Deadline": "Scadenza", "Description": "Descrizione", "RiskId": "ID Rischio", "InherentRiskLevel": "Livello di Rischio Inerente", "ResidualRiskLevel": "Livello di Rischio Residuo", "Reminder": "Promemoria", "Organization": "Organizzazione", "Result": "Risultato", "RiskApprover": "Approvazione del Rischio", "RiskName": "Nome del Rischio", "RiskOwner": "Responsabile del Rischio", "RiskTemplate": "<PERSON>lo di Rischio", "Source": "Fonte", "TargetRiskLevel": "Livello di Rischio Obiettivo", "Treatment": "Trattamento", "TreatmentPlan": "Piano di Trattamento", "TreatmentStatus": "Stato del Trattamento", "Threat": "<PERSON><PERSON><PERSON>", "Type": "Tipo", "Vulnerability": "Vulnerabilità"}, "MitigationStep": {"Strategy": "Strategia", "Write": "<PERSON><PERSON><PERSON>", "YourProgress": "Il tuo progresso", "AddAction": "Aggiungi azione", "ActionTitle": "Titolo dell'azione", "AssignedTo": "Assegnato a", "AssignedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "AssignedDate": "Data di assegnazione", "Deadline": "Scadenza", "Status": "Stato", "ActionAddedSuccessfully": "Azione aggiunta con successo", "ProceedToNextStep": "Procedi al prossimo passo", "ConfirmationText": "Sei soddisfatto della migrazione", "AddActionForm": {"AddAction": "Aggiungi azione", "Category": "Categoria", "Entity": "Entità", "Select": "Seleziona", "Description": "Descrizione", "AssignedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "AssignedTo": "Assegnato a", "AssignDate": "Data di assegnazione", "Deadline": "Scadenza", "PickADate": "Scegli una data"}}, "ClosureStep": {"ClosureReview": "Revisione della Chiusura", "Write": "<PERSON><PERSON><PERSON>", "AssociateDuty": "<PERSON><PERSON><PERSON> dovere", "Duty": "<PERSON><PERSON>", "AssociatePolicyProcedure": "Associa politica e procedura", "PolicyProcedure": "Politica e Procedura", "AssociateImprovement": "As<PERSON>cia miglioramento", "Improvement": "Miglioramento"}, "MonitoringStep": {"Title": "<PERSON><PERSON>", "Comment": "Commento", "Standard": "Standard", "CreatedDate": "Data di Creazione", "DueDate": "Data di Scadenza", "Status": "Stato"}}}, "Activities": {"Duty": {"TOTAL": "TOTALE", "OPEN": "APERTO", "ARCHIVE": "ARCHIVIO", "NoArchiveData": "<PERSON><PERSON><PERSON> dato di archivio", "Archieved": "Archiviato", "Active": "Attivo", "Open": "Aperto", "Overdue": "In ritardo", "NoOpenData": "<PERSON><PERSON><PERSON> dato aperto", "AddNew": "AGGIUNGI NUOVO", "DutyDetails": "Dettagli del dovere", "Fulfilment": "Adempimento", "AuditLog": "Registro di controllo", "Description": "Descrizione", "Name": "Nome", "Date": "Data", "Time": "<PERSON>a", "AddDutyForm": {"AddDuty": "Aggiungi dovere", "AddTag": "Aggiungi etichetta", "DutyTitle": "<PERSON><PERSON>", "AddAssignee": "Aggiungi assegnatario", "SelectAssignee": "Seleziona assegnatario", "DueDate": "Data di scadenza", "PickADate": "Scegli una data", "Entity": "Entità", "Select": "Seleziona", "Standards": "Standard", "EnterYourStandardsHere": "Inserisci qui i tuoi standard...", "Comment": "Commento", "EnterYourCommentsHere": "Inserisci qui i tuoi commenti...", "Attachment": "Allegato", "AddDocument": "Aggiungi documento", "SelectedFiles": "File selezionati:"}, "DutyDetailForm": {"DutyTitle": "<PERSON><PERSON>", "Dummy": "dummy", "AssignedTo": "Assegnato a", "SelectAssignee": "Seleziona assegnatario", "Status": "Stato", "Open": "APERTO", "Completed": "COMPLETATO", "StartDate": "Data di inizio", "Owner": "Proprietario", "DueDate": "Data di scadenza", "DueDateValue": "7 maggio 2025", "Frequency": "Frequenza", "Select": "Seleziona", "Standards": "Standard", "Criteria": "<PERSON><PERSON><PERSON>", "DefinitionOfEvidence": "Definizione della prova", "Comments": "Commenti", "Attachment": "Allegato", "AddDocument": "Aggiungi documento", "SelectedFiles": "File selezionati:", "Annually": "Annualmente", "BiAnnual": "Semestrale", "TriMonthly": "Trimestr", "Monthly": "<PERSON><PERSON><PERSON>"}, "DutyFulfilmentForm": {"DutyInterval": "Intervallo del dovere", "PickDate": "Seleziona una data", "Assigned": "<PERSON><PERSON><PERSON><PERSON>", "Select": "Seleziona", "FulfilledBy": "Completato da", "Write": "<PERSON><PERSON><PERSON>", "Status": "Stato", "FulfilmentProof": "Prova di adempimento", "VerificationQuestion": "L’efficacia del dovere è stata verificata?", "Yes": "Sì", "No": "No", "Attachments": "Allegati", "Vector": "vettore", "AddDocuments": "Aggiungi documenti", "URLLink": "Collegamento URL", "LandingPageForm": "Modulo della pagina di destinazione", "WebsiteSignup": "Iscrizione al sito web", "SocialMedia": "Social media", "Comment": "Commento", "EnterURL": "Inserisci URL", "Finalized": "Finalizzato"}}, "Action": {"AddActionForm": {"AddAction": "Aggiungi azione", "Category": "Categoria", "Entity": "Entità", "Select": "Seleziona", "Description": "Descrizione", "AssignedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "AssignedTo": "Assegnato a", "AssignDate": "Data di assegnazione", "PickDate": "Seleziona una data", "Deadline": "Scadenza"}, "ActionActivity": {"TotalActionItems": "Totale elementi azione", "OpenActionItems": "Elementi azione aperti", "ClosedActionItems": "Elementi azione chiusi", "AddNew": "AGGIUNGI NUOVO", "ActionAddedSuccessfully": "Azione aggiunta con successo", "ActionTitle": "Titolo dell'Azione", "AssignedTo": "Assegnato a", "AssignedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "AssignedDate": "Data di assegnazione", "Deadline": "Scadenza", "Status": "Stato"}}, "Improvement": {"TOTAL": "TOTALE", "INPROGRESS": "IN CORSO", "ARCHIVE": "ARCHIVIO", "Open": "Aperto", "Completed": "Completato", "Overdue": "In Ritardo", "ImprovementDetails": "Dettagli del miglioramento", "Evaluation": "Valutazione", "AuditLog": "Registro di controllo", "NoCompletedData": "<PERSON><PERSON><PERSON> dato completato", "ImprovementDetailsForm": {"DutyTitle": "Titolo dell'Incarico", "DummyValue": "pkjw", "AssignedTo": "Assegnato a", "SelectAssignee": "Seleziona Assegnatario", "Status": "In Corso", "Completed": "Completato", "DueDate": "Data di Scadenza", "DueDateValue": "10 marzo 2025", "Owner": "Proprietario", "Regulation": "Regolamento", "Select": "Seleziona", "Finding": "Altre aree interessate dalla stessa constatazione", "RootCause": "<PERSON><PERSON><PERSON>", "TreatmentPlan": "Piano di Trattamento", "Attachment": "Allegato", "AddDocument": "Aggiungi Documento", "TechTeamInvolved": "È coinvolto un team tecnico nell'implementazione?", "Yes": "Sì", "No": "No", "SelectedFiles": "File Selezionati:", "AddProgress": "Aggiungi i tuoi progressi"}, "AddNewImprovementForm": {"AddImprovement": "Aggiungi Miglioramento", "AddTag": "Aggiungi Etichetta", "ImprovementActionTitle": "Titolo dell'Azione di Miglioramento", "Title": "<PERSON><PERSON>", "AddAssignee": "Aggiungi Assegnatario", "SelectAssignee": "Seleziona Assegnatario", "DueDate": "Data di Scadenza", "PickDate": "Scegli una data", "Regulation": "Regolamento", "Select": "Seleziona", "Entity": "Entità", "Comment": "Commento", "Comments": "Commenti", "Attachment": "Allegato", "AddDocument": "Aggiungi Documento", "SelectedFiles": "File Selezionati:"}, "EvaluationForm": {"ReviewedBy": "Revisionato da", "ReviewedByPlaceholder": "<PERSON><PERSON><PERSON>", "EffectOnFinding": "Ha un effetto sulla scoperta?", "EffectOnFindingPlaceholder": "<PERSON><PERSON><PERSON>", "OtherAreasAffected": "Altre aree interessate dalla stessa scoperta", "OtherAreasAffectedPlaceholder": "<PERSON><PERSON><PERSON>", "ManagementReviewTopic": "Argomento della revisione gestionale", "Select": "Seleziona", "ManagementReviewText": "Testo per la revisione della gestione", "LandingPageForm": "Modulo della pagina di destinazione", "WebsiteSignup": "Iscrizione al sito web", "SocialMedia": "Social media", "Source": "Fonte"}}}, "Repository": {"DocumentRepository": {"Total": "Totale", "Creation": "Creazione", "In Progress": "In Corso", "In Use": "In Uso", "Document": "Documento", "Description": "Descrizione", "Category": "Categoria", "CreatedOn": "Creato il", "CreatedBy": "<PERSON><PERSON><PERSON> <PERSON>", "Attachments": "Allegati"}, "AssesmentRepository": {"Total": "Totale", "In Progress": "In Corso", "Completed": "Completato", "Department": "Dipartimento", "AssessmentName": "Nome della Valutazione", "AssessmentType": "Tipo di Valutazione", "CreatedDate": "Data di Creazione", "CreatedBy": "<PERSON><PERSON><PERSON> <PERSON>", "Action": "Azione"}, "RecordOfProcessingActivitiesRepository": {"Total": "Totale", "In Progress": "In Corso", "Completed": "Completato", "Department": "Dipartimento", "Process": "Processo", "CreatedDate": "Data di Creazione", "CreatedBy": "<PERSON><PERSON><PERSON> <PERSON>", "Version": "Versione", "Action": "Azione"}}}, "AuditLog": {"RequestStatusChangedToAcknowledgement": "Stato della richiesta cambiato in Accoglienza", "RequestStatusChangedToDataGathering": "Stato della richiesta cambiato in Raccolta dati"}, "ToastMessages": {"Authentication": {"OTPVerifiedSuccessfully": "OTP verificato con successo!", "OTPSentSuccessfully": "OTP inviato con successo", "OTPResentSuccessfully": "OTP reinviato con successo", "VerificationCodeSentSuccessfully": "Codice di verifica inviato con successo", "VerificationCodeResentSuccessfully": "Codice di verifica reinviato con successo", "PasswordUpdatedSuccessfully": "Password aggiornata con successo", "CodeSentSuccessfully": "Codice inviato con successo", "FailedToSendOTP": "Impossibile inviare OTP. Riprova."}, "Forms": {"FormSubmittedSuccessfully": "<PERSON><PERSON><PERSON> inviato con successo", "FormTranslatedSuccessfully": "<PERSON><PERSON><PERSON> tradotto con <PERSON>o", "DataSavedSuccessfully": "<PERSON>ti salvati con successo", "DataUpdatedSuccessfully": "<PERSON>ti aggiornati con successo", "OperationSuccessful": "Operazione riuscita", "ProcessCompletedSuccessfully": "Processo completato con successo", "SubmittedSuccessfully": "Inviato con successo", "AllChangesSaved": "<PERSON>tte le modifiche salvate", "SavedSuccessfully": "Salvato con successo"}, "CookieConsent": {"DomainDetailsUpdatedSuccessfully": "I dettagli del dominio sono stati aggiornati con successo.", "CategorySavedSuccessfully": "Categoria salvata con successo", "ServiceSavedSuccessfully": "<PERSON><PERSON><PERSON> sal<PERSON> con <PERSON>o", "CookieSavedSuccessfully": "<PERSON><PERSON> salvato con <PERSON>o", "CookieCreatedSuccessfully": "<PERSON><PERSON> creato con <PERSON>o"}, "Files": {"FileUploadedSuccessfully": "File caricato con successo", "FileImportedSuccessfully": "File importato con successo", "FileProcessedSuccessfully": "File elaborato con successo", "DocumentUploadedSuccessfully": "Documento caricato con successo", "DocumentDeletedSuccessfully": "Documento eliminato con successo", "DocumentsAddedSuccessfully": "Documenti aggiunti con successo", "DocumentsDeletedSuccessfully": "Documenti eliminati con successo", "UploadedSuccessfully": "Caricato con successo", "TemplateDownloadedSuccessfully": "Template scaricato con successo"}, "UCM": {"ProcessingPurposeUpdatedSuccessfully": "Scopo del trattamento aggiornato con successo"}, "Assessment": {"AssessmentCreatedSuccessfully": "Valutazione creata con successo", "AssessmentSubmittedSuccessfully": "Valutazione inviata con successo", "AssessmentReviewedSuccessfully": "Valutazione rivista con successo", "AnswerSavedSuccessfully": "Risposta salvata con successo", "ReviewSavedSuccessfully": "Revisione salvata con successo", "PleaseAnswerAllQuestions": "Si prega di rispondere a tutte le domande!", "ProgressBarError": "Errore della barra di progresso!", "ErrorInUpdatingTheQuestion": "Errore nell'aggiornamento della domanda", "FailedToCreateAssessment": "Creazione valutazione fallita", "FailedToLoadCategoryTypes": "Impossibile caricare i tipi di categoria", "PleaseReviewAllQuestions": "Si prega di rivedere tutte le domande!"}, "ROPA": {"ROPASubmittedSuccessfully": "ROPA inviato con successo", "ROPAStartedSuccessfully": "ROPA avviato con successo", "ROPADataUpdatedSuccessfully": "Dati ROPA aggiornati con successo", "TentativeDateUpdatedSuccessfully": "Data tentativa aggiornata con successo"}, "Users": {"UserAssignedSuccessfully": "Utente assegnato con successo", "UserAddedSuccessfully": "Utente aggiunto con successo", "UserUpdatedSuccessfully": "Utente aggiornato con successo", "PasswordResetEmailSentSuccessfully": "Email di reset password inviata con successo"}, "Vendors": {"VendorAddedSuccessfully": "Fornitore aggiunto con successo", "VendorUpdatedSuccessfully": "Fornitore aggiornato con successo"}, "Cookies": {"CookieUpdatedSuccessfully": "<PERSON><PERSON> a<PERSON> con <PERSON>o", "TranslationSavedSuccessfully": "Traduzione salvata con successo", "PolicyAddedSuccessfully": "Politica aggiunta con successo", "PolicyUpdatedSuccessfully": "Politica aggiornata con successo"}, "Consent": {"ConsentPurposeUpdatedSuccessfully": "Finalità del consenso aggiornata con successo", "ConsentPurposeDeletedSuccessfully": "Finalità del consenso eliminata con successo", "ProcessingPurposeAddedSuccessfully": "Finalità di elaborazione aggiunta con successo", "ProcessingPurposeUpdatedSuccessfully": "Finalità di elaborazione aggiornata con successo", "ProcessingPurposeDeletedSuccessfully": "Finalità di elaborazione eliminata con successo"}, "Workflow": {"WorkflowCreatedSuccessfully": "Flusso di lavoro creato con successo", "WorkflowUpdatedSuccessfully": "Flusso di lavoro aggiornato con successo", "WorkflowAddedSuccessfully": "Flusso di lavoro aggiunto con successo", "TaskAddedSuccessfully": "Attività aggiunta con successo", "TaskUpdatedSuccessfully": "Attività aggiornata con successo", "TaskAutomationUpdatedSuccessfully": "Automazione attività aggiornata con successo", "AutomationRemovedSuccessfully": "Automazione rimossa con successo", "StepAddedSuccessfully": "Passaggio aggiunto con successo", "WorkflowStepDeletedSuccessfully": "Passaggio del flusso di lavoro eliminato con successo", "WorkflowStepRenamedSuccessfully": "Passaggio del flusso di lavoro rinominato con successo"}, "PII": {"PIIAddedSuccessfully": "PII aggiunto con successo", "PIIEditedSuccessfully": "PII modificato con successo", "PIIUpdatedSuccessfully": "PII aggiornato con successo", "PIIDeletedSuccessfully": "PII eliminato con successo", "PIIsUpdatedSuccessfully": "PII aggiornati con successo"}, "DataCatalogue": {"ServiceAddedSuccessfully": "<PERSON><PERSON><PERSON> aggiunto con successo", "TaskTriggeredSuccessfully": "Attività attivata con successo", "CredentialsSavedSuccessfully": "Credenziali salvate con successo", "FailedToSaveCredentials": "Impossibile salvare le credenziali. Controlla i tuoi dati e riprova."}, "General": {"DashboardUpdatedSuccessfully": "Dashboard aggiornato con successo", "DashboardDeletedSuccessfully": "Dashboard eliminato con successo", "CopiedToClipboard": "Copiato negli appunti!", "URLCopiedToClipboard": "URL copiato negli appunti!", "ScanStartedSuccessfully": "Scansione avviata con successo", "ErrorReportSentSuccessfully": "Rapporto errore inviato con successo", "TicketCreatedSuccessfully": "Ticket creato con successo", "BusinessUnitUpdatedSuccessfully": "Unità aziendale aggiornata con successo", "DepartmentUpdatedSuccessfully": "Dipartimento aggiornato con successo", "ProcessUpdatedSuccessfully": "Processo aggiornato con successo", "QuestionAddedSuccessfully": "Domanda aggiunta con successo", "QuestionUpdatedSuccessfully": "Domanda aggiornata con successo", "QuestionDeletedSuccessfully": "Domanda eliminata con successo", "TextFieldAdded": "Campo di testo aggiunto", "DeletedSuccessfully": "Eliminato con successo", "Success": "Successo!", "RequestApprovedSuccessfully": "Richiesta approvata con successo", "RequestRejectedSuccessfully": "Richiesta rifiutata con successo", "RequestArchivedSuccessfully": "Richiesta archiviata con successo", "RequestUnarchivedSuccessfully": "Richiesta desarchiviata con successo", "RequestCompletedSuccessfully": "Richiesta completata con successo", "MailTemplateSentSuccessfully": "Template email inviato con successo", "TemplateDeletedSuccessfully": "Template eliminato con <PERSON>o", "FiltersAppliedSuccessfully": "Filtri applicati con successo", "RulesAppliedSuccessfully": "Regole applicate con successo", "OptionRemovedSuccessfully": "Opzione rimossa con successo", "LogoUploadedSuccessfully": "Logo caricato con successo", "URLCopiedSuccessfully": "URL copiato con successo", "FormCreatedSuccessfully": "<PERSON><PERSON><PERSON> creato con successo", "QuestionOrderUpdatedSuccessfully": "Ordine domande aggiornato con successo", "QuestionRemovedSuccessfully": "<PERSON><PERSON> rim<PERSON> con successo", "FormSavedSuccessfully": "<PERSON><PERSON><PERSON> salvato con <PERSON>o", "FormVersionCreatedSuccessfully": "Versione del modulo creata con successo", "FailedToFetchControls": "Impossibile recuperare i controlli", "RejectMessageCannotBeEmpty": "Il messaggio di rifiuto non può essere vuoto", "FailedToSendMailTemplate": "Impossibile inviare il modello di email", "DataSubjectEmailNotAvailable": "Email del soggetto interessato non disponibile", "FailedToFetchReviewers": "Impossibile recuperare i revisori", "AutoScanRequired": "È richiesta la scansione automatica", "FailedToUpdateCookie": "Impossibile aggiornare il cookie", "FailedToSaveRetentionRule": "Impossibile salvare la regola di conservazione", "UploadFilesOrEnterURL": "Carica file o inserisci URL", "EnterValidURL": "Inserisci URL valido", "UploadValidFile": "Carica file valido", "EnterTemplateName": "Inserisci nome template", "DuplicateStepsChooseDifferentName": "Passaggi duplicati. Scegli un nome diverso", "StepAddedSuccessfully": "Passaggio aggiunto con successo", "FailedToAddWorkflowStep": "Impossibile aggiungere il passaggio del flusso di lavoro", "ErrorWhileAddingWorkflow": "Errore durante l'aggiunta del flusso di lavoro", "TentativeDateUpdatedSuccessfully": "Data tentativa aggiornata con successo", "CouldntUpdateTentativeDate": "Impossibile aggiornare la data tentativa", "PublicURLGenerated": "URL pubblico generato", "DeadlineExtendedSuccessfully": "Scadenza estesa con successo", "BusinessUnitUpdated": "Unità aziendale aggiornata", "ActionAddedSuccessfully": "Azione aggiunta con successo", "MitigationPlanSubmittedSuccessfully": "Piano di mitigazione inviato con successo", "PlanSavedSuccessfully": "Piano salvato con successo", "RegulationAddedSuccessfully": "Regolamento aggiunto con successo", "RiskCreatedSuccessfully": "<PERSON><PERSON><PERSON> creato con successo", "ImportedSuccessfully": "Importato con successo", "RecordUpdatedSuccessfully": "Record aggiornato con successo", "PrivacyNoticeSavedSuccessfully": "Informativa privacy salvata con successo", "ProcessingCategoryUpdatedSuccessfully": "Categoria di elaborazione aggiornata con successo", "ProcessingCategoryDeletedSuccessfully": "Categoria di elaborazione eliminata con successo", "PIILabelUpdatedSuccessfully": "Etichetta PII aggiornata con successo", "PIILabelDeletedSuccessfully": "Etichetta PII eliminata con successo", "TemplateActivatedSuccessfully": "Template attivato con successo", "TemplateInactivatedSuccessfully": "Template disattivato con successo", "TranslatedDataFetched": "<PERSON><PERSON> trado<PERSON> recuperati", "FormTranslated": "<PERSON><PERSON><PERSON>", "GTMSettingsAppliedToAll": "Impostazioni GTM applicate a tutti", "PIIAnalysisCompletedSuccessfully": "Analisi PII completata con successo", "RetentionRuleSavedSuccessfully": "Regola di conservazione salvata con successo", "MappingDeletedSuccessfully": "Mappatura eliminata con successo", "BreachEvaluationUpdatedSuccessfully": "Valutazione violazione aggiornata con successo", "BreachAddedSuccessfully": "Violazione aggiunta con successo", "BreachResolutionDoneSuccessfully": "Risoluzione violazione completata con successo", "PreventiveActionAddedSuccessfully": "Azione preventiva aggiunta con successo", "NotificationSavedSuccessfully": "Notifica salvata con successo", "CustomerAddedSuccessfully": "Cliente aggiunto con successo", "CustomerUpdatedSuccessfully": "Cliente aggiornato con successo", "ResourceAddedSuccessfully": "Risorsa aggiunta con successo", "CollaboratorAddedSuccessfully": "Collaboratore aggiunto con successo", "PreferencesChangedSuccessfully": "Preferenze modificate con successo", "AddedSuccessfully": "{{type}} aggiunto con successo", "AssessmentStartedSuccessfully": "Valutazione avviata con successo"}}}