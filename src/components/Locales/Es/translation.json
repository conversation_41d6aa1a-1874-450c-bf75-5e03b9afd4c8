{"Common": {"CreateNew": "<PERSON><PERSON>r <PERSON>", "ClearFilters": "<PERSON><PERSON><PERSON>", "Search": "Buscar", "RequiredField": "¡Este campo es obligatorio!", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Submit": "Enviar", "Cancel": "<PERSON><PERSON><PERSON>", "Save": "Guardar", "Saving": "Guardando...", "SaveAndContinue": "Guardar y continuar", "Delete": "Eliminar", "Edit": "<PERSON><PERSON>", "Next": "Siguient<PERSON>", "Prev": "Anterior", "Create": "<PERSON><PERSON><PERSON>", "Add": "<PERSON><PERSON><PERSON>", "AddNew": "<PERSON><PERSON><PERSON>ue<PERSON>", "Import": "Importar", "All": "Todo", "Submitting": "Enviando...", "Deleting": "Eliminando...", "Adding": "Agregando...", "Updating": "Actualizando...", "Reset": "Restablecer", "True": "Verdadero", "False": "<PERSON><PERSON><PERSON>", "Low": "<PERSON><PERSON>", "Medium": "Medio", "High": "Alto", "Apply": "Aplicar", "Write": "Escribir", "Action": "Acción", "Name": "Nombre", "NoResult": "Sin resultados.", "Update": "Actualizar", "No": "No", "YesProceed": "<PERSON><PERSON>, Proceder", "Copy": "Copiar", "Copied": "Copiado", "BackToList": "Volver a la lista"}, "ErrorPage": {"Page": "<PERSON><PERSON><PERSON><PERSON>", "NotFound": "No encontrado", "NotExist": "Lamentablemente, la página que buscas no existe.", "GoBack": "Regresar", "ReportError": "Informar error"}, "Breadcrumbs": {"ControlRoom": "Sala de Control", "Policy Management": "Gestión de Políticas", "All Policies": "Todas las Políticas", "Privacy Notice": "Aviso de Privacidad", "Audit Log": "Registro de Auditoría", "Data Subject Rights Management": "Gestión de Derechos del Titular de los Datos", "Pending Requests": "Solicitudes Pendientes", "View Request": "<PERSON><PERSON>", "Approved Requests": "Solicitudes Aprobadas", "Reject In Progress Requests": "Solicitudes Rechazadas en Progreso", "Rejected Requests": "Solicitudes Rechazadas", "Completed Requests": "Solicitudes Completadas", "Profile": "Perfil", "Change Password": "Cambiar <PERSON>", "Blogs": "Blogs", "View Blog": "Ver Blog", "About GoTrust": "Acerca de GoTrust", "Company Structure": "Estructura de la Empresa", "Group Detail": "Detalle del Grupo", "Customer Management": "Gestión de Clientes", "Role Management": "Gestión de Roles", "Role Details": "Detalles del Rol", "Add Role": "Agregar Rol", "Edit Role": "<PERSON><PERSON>", "Edit Role Details": "Editar Detalles del Rol", "User Management": "Gestión de Usuarios", "Home": "<PERSON><PERSON>o", "Insights into Processing Activities": "Información sobre actividades de procesamiento", "Task Overview": "Resumen de tareas", "ROPA": "ROPA", "ROPA Review": "Revisión de ROPA", "Basic Information": "Información básica", "Add Control": "Agregar control", "PII Inventory": "Inventario de PII", "Unstructured Data Inventory": "Inventario de datos no estructurados", "Data Catalogue": "Catálogo de datos", "Structured": "Estructurado", "Unstructured": "No estructurado", "Services": "<PERSON><PERSON><PERSON>", "Ingestion": "Ingesta", "Data Catalogue Dashboard": "Panel de catálogo de datos", "PII List": "Lista de PII", "File Classification": "Clasificación de archivos", "DSR Form Repository": "Repositorio de formularios DSR", "DSR Form Builder": "Creador de formularios DSR", "Create Form": "Crear formulario", "Form Review": "Revisión del formulario", "Add Question": "Agregar pregunta", "Schema Entity": "Entidad de esquema", "Table Entity": "Entidad de tabla", "Column Entity": "Entidad de columna", "Profile Entity": "Entidad de perfil", "Template": "Plantilla", "Assessment Management": "Gestión de evaluación", "Dashboard": "Panel de control", "Templates": "Plantillas", "Vendor Risk Management": "Gestión de Riesgos del Proveedor", "Vendor List": "Lista de Proveedores", "Details": "Detalles", "Internal Assessment": "Evaluación Interna", "Vendor Assessment": "Evaluación del Proveedor", "Mitigation": "Mitigación", "Cookie Consent Management": "Gestión de Consentimiento de Cookies", "Cookie Consent Domain": "Dominio de Consentimiento de Cookies", "Cookie Configuration": "Configuración de Cookies", "Universal Consent Management": "Gestión Universal del Consentimiento", "Preference Center": "Centro de Preferencias", "Add Preference Center": "Añadir Centro de Preferencias", "Update Preference Center": "Actualizar Centro de Preferencias", "Consent Collection": "Recopilación de Consentimiento", "Consent Upload": "Carga de Consentimiento", "Custom Parameters": "Parámetros Personalizados", "Subject Consent Types": "Tipos de Consentimiento del Sujeto", "Subject Consent Detail": "Detalles del Consentimiento del Sujeto", "Form": "Formulario", "Consent Collection Templates": "Plantillas de Recopilación de Consentimiento", "Create Consent Collection Template": "Crear Plantilla de Recopilación de Consentimiento", "Processing Category": "Categoría de Procesamiento", "Processing Purpose": "Propósito del Procesamiento", "Consent Purpose": "Propósito del Consentimiento", "PII Label": "Etiqueta PII", "Privacy Notice Details": "Detalles del Aviso de Privacidad", "View Privacy Notice": "Ver Aviso de Privacidad", "Subject Consent List": "Lista de Consentimiento del Sujeto", "Customization": "Personalización", "Requests": "Solicitudes", "View Requests": "Ver Solicitudes", "Tasks": "<PERSON><PERSON><PERSON>", "View Tasks": "<PERSON><PERSON>", "Reject in Progress Requests": "<PERSON><PERSON><PERSON> en Progreso", "Create Request": "<PERSON><PERSON><PERSON>", "Email Templates": "Plantillas de Correo Electrónico", "Create Email Template": "<PERSON><PERSON><PERSON>", "Retention Schedule": "Calendario de Retención", "Workflow": "Flujo de Trabajo", "Add Workflow": "Agregar Flujo de Trabajo", "Edit Workflow": "Editar Flujo de Trabajo", "My Request": "<PERSON>", "My Request View": "Vista de Mi Solicitud", "View Workflow": "Ver Flujo de Trabajo", "Support": "Soporte", "Create Ticket": "<PERSON><PERSON><PERSON> Ticket", "View Ticket": "Ver Ticket", "Edit Ticket": "<PERSON><PERSON>", "Finance": "Finanzas", "Universal Control Framework": "<PERSON> Universal", "Improvements": "<PERSON><PERSON><PERSON>", "Risk Dashboard": "Panel de Riesgos", "Compliance Dashboard": "Panel de Cumplimiento", "Privacy Ops": "Operaciones de Privacidad", "Document Repository": "Repositorio de Documentos", "Assessment Repository": "Repositorio de Evaluaciones", "Processing Activities": "Actividades de Procesamiento", "Regulations": "Regulaciones", "Risk Register": "Registro de Riesgos", "Duty": "<PERSON><PERSON>", "Action": "Acción", "Improvement Actions": "Acciones de Mejora", "Breaches": "Incumplimientos", "Breach Details": "Detalles del Incumplimiento", "Subject Consent Manager": "Gestor de Consentimiento del Sujeto"}, "SideBar": {"GroupLabel": {"Account Setup": "Configuración de la cuenta", "Theme": "<PERSON><PERSON>", "Data Mapping": "Mapeo de datos", "Policy Management": "Gestión de políticas", "Data Subject Rights Management": "Gestión de derechos del sujeto de datos", "Assessment Management": "Gestión de evaluaciones", "Universal Consent Management": "Gestión de consentimientos universales", "Universal Control Framework": "Marco <PERSON> universal", "Cookie Consent Management": "Gestión del consentimiento de cookies", "Vendor Risk Management": "Gestión de riesgos de proveedores", "Awareness Program": "Programa de concienciación", "Blogs": "Blogs", "Support": "Soporte", "Finance/Commercials": "Finanzas/Comerciales", "Workflow Automation": "Automatización de flujos de trabajo", "Data Discovery": "Descubrimiento de datos", "DPO Runbook": "Guía de ejecución del DPO", "Data Breach Management": "Gestión de violaciones de datos", "Configurations": "Configuraciones", "Policy & Notice Management": "Gestión de Políticas y Avisos"}, "SideBarData": {"Profile Configuration": "Configuración del perfil", "On-boarding Questionnaire": "Cuestionario de incorporación", "Company Structure": "Estructura de la empresa", "Access Management": "Gestión de accesos", "Role Management": "Gestión de roles", "User Management": "Gestión de usuarios", "Vendor Management": "Gestión de proveedores", "About GoTrust": "Acerca de GoTrust", "Customization": "Personalización", "Dashboard": "<PERSON><PERSON>", "Task Overview": "Resumen de tareas", "Content Profiles": "<PERSON><PERSON>les de contenido", "Data Catalogue": "Catálogo de datos", "Structure": "Estructurado", "Unstructure": "No estructurado", "Data Catalogue V0": "Catálogo de datos V0", "File Classification": "Clasificación de archivos", "DSR Lab": "Laboratorio DSR", "DSR Form Builder": "Constructor de formularios DSR", "DSR Report": "Informe DSR", "DSR Form Repository": "Repositorio de formularios DSR", "DSR Email Templates": "Plantillas de correo electrónico de DSR", "DSR Retention Schedule": "Calendario de retención DSR", "Workflow": "Flujo de trabajo", "Impact Assessment": "Evaluación de impacto", "Privacy Impact Assessment": "Evaluación de impacto en la privacidad", "Privacy by Design Assessment": "Evaluación de privacidad desde el diseño", "Legitimate Interests Assessment": "Evaluación de intereses legítimos", "Transfer Impact Assessment": "Evaluación del impacto de la transferencia", "EU AI Assessment": "Evaluación de IA de la UE", "Assessment Lab": "Laboratorio de evaluación", "Assessment Templates": "Plantillas de evaluación", "Subject Consent Types": "Tipos de consentimiento del sujeto", "Subject Consent List": "Lista de consentimientos del sujeto", "Privacy Notice": "Aviso de privacidad", "Source": "Fuente", "Consent Upload": "Carga de consentimientos", "UCM Lab": "Laboratorio UCM", "Processing Category": "Categoría de procesamiento", "Processing Purpose": "Propósito del procesamiento", "Consent Purpose": "Propósito del consentimiento", "Consent POC": "Punto de contacto para el consentimiento", "PII Label": "Etiqueta de PII", "Consent Collection Builder": "Constructor de colección de consentimientos", "Preference Form": "Formulario de preferencias", "Cookie Consent Domain": "Dominio de consentimiento de cookies", "VRM Lab": "Laboratorio VRM", "Vendor Assessment Templates": "Plantillas de evaluación de proveedores", "Course Management": "Gestión de cursos", "Registration": "Registro", "Enrollment": "Inscripción", "Project View": "Vista del proyecto", "Blogs": "Blogs", "Billing & Invoice": "Facturación y facturas", "Workflow Automation": "Automatización de flujos de trabajo", "Data Discovery": "Descubrimiento de datos", "Risk Dashboard": "Panel de riesgos", "Compliance Dashboard": "Panel de cumplimiento", "Regulations": "Regulaciones", "Risk Register": "Registro de riesgos", "Activities": "Actividades", "Duties": "<PERSON><PERSON><PERSON>", "Actions": "Acciones", "Improvements": "<PERSON><PERSON><PERSON>", "Repository": "Repositorio", "Document Repository": "Repositorio de documentos", "Assessment Repository": "Repositorio de evaluaciones", "Record of Processing Activities Repository": "Repositorio de registro de actividades de procesamiento", "User Guide": "Guía del usuario", "Breach List": "Lista de violaciones", "Subject Consent Manager": "Gestor de Consentimiento del Sujeto", "Theme Customization": "Personalización del Tema", "Data Visualization": "Visualización de Datos", "Data Insights": "Información de Datos", "Data Flow Diagram": "Diagrama de Flujo de Datos", "Data Vizualization": "Visualización de Datos"}}, "Home": {"Account Setup": "Configuración de la cuenta", "Theme": "Temática", "Data Mapping": "Mapeo de datos", "Policy & Notice Management": "Gestión de políticas y avisos", "Data Subject Rights Management": "Gestión de derechos del interesado", "Assessment Management": "Gestión de evaluaciones", "Universal Consent Management": "Gestión universal de consentimientos", "Universal Control Framewok": "Marco <PERSON> universal", "Cookie Consent Management": "Gestión de consentimiento de cookies", "Vendor Risk Management": "Gestión de riesgos de proveedores", "Awareness Program": "Programa de concienciación", "Blogs": "Blogs", "Support": "Soporte", "Finance/Commercials": "Finanzas/Comerciales", "Workflow Automation": "Automatización del flujo de trabajo", "Data Discovery": "Descubrimiento de datos", "DPO Runbook": "Manual del DPO", "Data Breach Management": "Gestión de violaciones de datos", "Data Retention": "Retención de datos", "Universal Control Framework": "<PERSON> Universal"}, "CompanyStructure": {"GroupDetails": {"Name": "Nombre", "Parent": "<PERSON><PERSON>", "NoOfUsers": "N.º de Usuarios", "CreatedOn": "Creado el", "UpdatedOn": "Actualizado el", "FirstName": "Nombre", "LastName": "Apellido"}, "CompanyTree": {"BusinessUnit": "Unidad de Negocio", "DepartmentUnit": "Unidad de Departamento", "ProcessUnit": "Unidad de Proceso"}, "CompanyView": {"Name": "Nombre", "Added On": "Añadido el", "Last Updated": "Última Actualización", "No. Of Users": "Número de Usuarios", "Actions": "Acciones"}}, "RoleManagement": {"AddRole": {"AddRole": "<PERSON><PERSON><PERSON>", "Heading": "Detalles del Rol", "RoleName": "Nombre del Rol", "EnterRoleName": "Ingrese el nombre del rol", "GivenAccess": "Accesos otorgados al usuario"}, "RoleTable": {"RoleName": "Nombre del Rol", "CreatedBy": "<PERSON><PERSON>o por", "Totaluser": "Total de Usuarios", "CreatedOn": "Creado el", "UpdatedOn": "Actualizado el", "Action": "Acción"}, "ActiveStatus": {"Active": "Activo", "Inactive": "Inactivo", "Archived": "Archivado"}, "ViewRole": {"Heading": "Detalles del Rol", "RoleName": "Nombre del Rol", "EnterRoleName": "Ingrese el nombre del rol", "CreatedBy": "<PERSON><PERSON>o por", "Status": "Estado", "CreatedDate": "Fecha de Creación", "UpdatedDate": "Fecha de Actualización", "GivenAccess": "Accesos otorgados al usuario", "EditRole": "<PERSON><PERSON>"}, "EditRole": {"Heading": "Detalles del Rol", "RoleName": "Nombre del Rol", "Status": "Estado", "GivenAccess": "Accesos otorgados al usuario"}}, "UserManagement": {"AddUser": {"Heading": "Detalles del Usuario", "FirstName": "Nombre", "EnterFirstName": "Ingrese el nombre", "LastName": "Apellido", "EnterLastName": "Ingrese el apellido", "Email": "Correo Electrónico", "EnterEmail": "Ingrese el correo electrónico", "Phone": "Teléfono", "EnterPhoneNumber": "Ingrese el número de teléfono", "SelectRole": "Seleccionar Rol", "GroupAssigned": "Grupo Asignado", "AccessesUser": "Accesos otorgados al usuario", "SeeAccesses": "Seleccionar Rol para ver Accesos"}, "EditUser": {"Heading": "Detalles del Usuario", "FirstName": "Nombre", "EnterFirstName": "Ingrese el nombre", "LastName": "Apellido", "EnterLastName": "Ingrese el apellido", "Email": "Correo Electrónico", "EnterEmail": "Ingrese el correo electrónico", "Phone": "Teléfono", "EnterPhoneNumber": "Ingrese el número de teléfono", "SelectRole": "Seleccionar Rol", "Status": "Estado", "GroupAssigned": "Grupo Asignado", "AccessesUser": "Accesos otorgados al usuario", "SeeAccesses": "Seleccionar Rol para ver Accesos"}, "ViewUser": {"Heading": "Detalles del Usuario", "FirstName": "Nombre", "EnterFirstName": "Ingrese el nombre", "LastName": "Apellido", "EnterLastName": "Ingrese el apellido", "Email": "Correo electrónico", "EnterEmail": "Ingrese el correo electrónico", "Phone": "Teléfono", "EnterPhoneNumber": "Ingrese el número de teléfono", "SelectRole": "Seleccionar rol", "Status": "Estado", "CreatedDate": "Fecha de creación", "UpdatedDate": "Fecha de actualización", "GroupAssigned": "Grupo asignado", "AccessesUser": "Accesos otorgados al usuario", "SeeAccesses": "Seleccione un rol para ver los accesos", "EditUser": "<PERSON>ar usuario"}, "UserTable": {"AddUser": "<PERSON><PERSON><PERSON>", "FirstName": "Nombre", "LastName": "Apellido", "Email": "Correo Electrónico", "Phone": "Teléfono", "AddedDate": "<PERSON>cha de Adición", "UpdatedDate": "Fecha de Actualización", "Group": "Grupo", "RoleName": "Nombre del Rol", "Action": "Acción"}}, "CustomerManagement": {"AddCustomer": {"Heading": "Detalles del Cliente", "Email": "Correo electrónico", "EnterEmail": "Ingrese el correo electrónico", "Address": "Dirección", "EnterAddress": "Ingrese la dirección", "AdminDetails": "Detalles del Administrador", "AdminName": "Nombre del Administrador", "EnterAdminName": "Ingrese el nombre del administrador", "Phone": "Teléfono", "EnterPhoneNumber": "Ingrese el número de teléfono", "AccessesUser": "Accesos otorgados al usuario", "SeeAccesses": "Seleccione un rol para ver los accesos"}}, "CookieConsentManagement": {"ServiceSavedSuccessfully": "Servicio guardado exitosamente", "DomainDetails": {"BusinessUnit_Tooltip": "La unidad de negocio a la que pertenece este dominio. Ayuda a mapear las banners de consentimiento de cookies al equipo o función adecuada dentro de su organización.", "DomainGroup_Tooltip": "El nombre del dominio donde se realizará la escaneo de cookies.", "DomainURL_Tooltip": "La URL completa del sitio web donde se ejecutará el escaneo de cookies. Ejemplo: https://www.example.com.", "Owner_Tooltip": "La persona o departamento responsable de gestionar el consentimiento de cookies para este dominio. Será notificado de cualquier cambio o problema.", "OwnerEmail_Tooltip": "La dirección de correo electrónico donde se enviarán alertas y actualizaciones relacionadas con el consentimiento de cookies. Puede ser un correo electrónico de equipo o una dirección individual.", "CookiePolicyLink_Tooltip": "Enlace directo a la página de política de cookies del dominio. Esto se mostrará en el banner de consentimiento para informar a los usuarios sobre las prácticas de datos.", "Entity_Tooltip": "Las regulaciones de protección de datos que son aplicables a esta URL de dominio específica.", "ConsentFramework_Tooltip": "Las regulaciones de protección de datos aplicables a esta URL de dominio específica.", "BusinessUnit": "Unidad de Negocio", "CookiePolicyLink": "Enlace de Política de Cookies", "GroupDomainName": "Nombre del Grupo de Dominio", "Heading": "Detalles del Dominio", "DomainGroup": "Grupo de Dominio", "DomainGroup_Placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DomainName": "Nombre de Dominio", "DomainName_Placeholder": "Ingrese el nombre de dominio", "URL": "URL", "DomainURL": "URL del Dominio", "URL_Placeholder": "Ingrese la URL", "Owner": "Propietario", "Owner_Placeholder": "Ingrese el nombre del propietario", "OwnerEmail": "Correo electrónico del propietario", "OwnerEmail_Placeholder": "Ingrese el correo electrónico del propietario", "Entity": "Entidad", "Entity_Placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CreatedOn": "Creado el", "ConsentFramework": "<PERSON>", "CreatedOn_Placeholder": "Seleccionar una fecha", "CompliancePolicyLink": "Enlace de Política de Cumplimiento", "CompliancePolicyLink_Placeholder": "Escribir"}}, "VendorRiskManagement": {"CreateNewVendor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "EnterName": "Ingrese el nombre del proveedor", "SelectName": "Seleccione el nombre del proveedor", "entity": "Entidad", "SelectEntity": "Seleccione el nombre de la entidad", "department": "Departamento", "SelectDepartment": "Seleccione el nombre del departamento", "assigned_to": "Asignado a", "SelectAssignee": "Seleccione el nombre del asignado", "reviewer": "Revisor", "SelectReviewer": "Seleccione el nombre del revisor", "template": "Plantilla", "SelectTemplate": "Seleccione el nombre de la plantilla", "DefaultTemplate": "Marco de Evaluación de Riesgo de Proveedores (predeterminado)"}, "Assessment": {"next": "Siguient<PERSON>", "previous": "Anterior", "controls": "Controles", "collaborator": "Colaborador", "upload": "Subir Archivo"}, "Lab": {"upload": "Subir Plantilla", "DropHere": "Arrastre aquí para adjuntar o", "Upload": "subir", "FileType": "Archivo CSV o XLSX | Tamaño máximo: 10MB", "name": "Nombre de la Plantilla", "PasteURL": "<PERSON><PERSON><PERSON> U<PERSON>"}, "ViewDetails": {"CollaboratorsProgressInVRM": "Categoría Progreso en la evaluación interna"}}, "AboutUs": {"OurMission": "Nuestra Misión", "KeyFeature": "Características Claves", "WhyChoose": "¿Por qué elegir GoTrust?"}, "Ropa": {"PiiHandbook": {"PIIHandBook": "Manual de PII", "AddPii": "<PERSON><PERSON><PERSON>", "PiiName": "Tipo de PII", "Description": "Descripción", "Tags": "Etiquetas", "TypeTag": "Inserisci un tag ", "Status": "Estado", "Cancel": "<PERSON><PERSON><PERSON>", "Save": "Guardar", "AddingPii": "Añadiendo PII...", "PiiType": "Tipo PII", "PiiCategory": "Categoría PII", "PiiValue": "Valor PII", "PiiDescription": "Descripción PII", "PiiTags": "Tags PII", "PiiTypeTag": "Inserisci un tag y pulsa Enter", "PiiStatus": "Estado", "PiiCancel": "<PERSON><PERSON><PERSON>", "PiiSave": "Guardar", "PiiNamePlaceholder": "Ingrese el nombre de PII", "PiiDescriptionPlaceholder": "Ingrese la descripción", "PiiTagsPlaceholder": "Inserisci un tag y pulsa Enter", "PiiStatusPlaceholder": "Seleziona Stato", "PiiTypePlaceholder": "Seleziona Tipo PII", "PiiCategoryPlaceholder": "Seleziona Categoría PII", "PiiValuePlaceholder": "Ingrese el valor de PII", "PiiSaving": "Guardando...", "PiiEditing": "Editando...", "PiiAdding": "Añadiendo...", "PiiDeleting": "Eliminando...", "PiiDeletingConfirmation": "¿Estás seguro de querer eliminar este PII?", "PiiEditPii": "Editar PII", "PiiPiiType": "Tipo PII", "PiiPiiTypePlaceholder": "Seleziona Tipo PII", "PiiPiiDescription": "Descripción PII", "PiiPiiDescriptionPlaceholder": "Ingrese la descripción", "PiiPiiTags": "Tag PII", "PiiPiiTagsPlaceholder": "Inserisci un tag e premi Enter", "PiiPiiStatus": "Estado", "PiiPiiStatusPlaceholder": "Seleziona Estado", "PiiPiiSource": "Fuente", "PiiPiiSourcePlaceholder": "Ingrese la fuente", "PiiPiiAddedSuccessfully": "PII agregado con éxito", "PiiPiiAddedFailed": "No se pudo agregar PII", "PiiPiiUpdatedSuccessfully": "PII actualizado con éxito", "PiiPiiUpdatedFailed": "No se pudo actualizar PII", "PiiPiiDeletedSuccessfully": "PII eliminado con éxito", "PiiPiiDeletedFailed": "No se pudo eliminar PII", "PiiPiiAlreadyExists": "PII ya existe", "PiiPiiDeletePii": "Eliminar PII", "PiiPiiDeletePiiDescription": "¿Estás seguro de querer eliminar este PII ", "PiiPiiDelete": "Eliminar", "withTag": "con etiqueta", "PiiPiiCancel": "<PERSON><PERSON><PERSON>", "PiiPiiDeleting": "Eliminando...", "PiiPiiAddFailed": "Error al agregar PII", "PiiPiiDeleteFailed": "Error al eliminar PII", "PiiPiiUpdateFailed": "Error al actualizar PII"}, "Dashboard": {"ByDepartment": {"Heading": "Actividades de Procesamiento por Departamento", "Count": "Total"}, "ByOrganization": {"Heading": "Actividades de Procesamiento por Rol de la Organización", "Count": "Total"}, "ByLawfullBasis": {"Heading": "Actividades de Procesamiento por Base Legal", "y-axis": "Base Legal Aplicada", "x-axis": "Número de Actividades de Procesamiento"}, "ByPersonalData": {"Heading": "Datos Personales y Sensibles por Departamento", "y-axis": "Departamentos", "x-axis": "Número de Elementos de Datos Personales"}, "ThirdPartiesList": {"Heading": "Lista de Terceros", "TableHeading": {"Vendor": "<PERSON><PERSON><PERSON><PERSON>", "Services": "<PERSON><PERSON><PERSON>", "Department": "Departamento", "Location": "Ubicación", "PersonalData": "Datos Personales Involucrados"}}, "DataSystemList": {"Heading": "Lista de Sistemas de Datos/Aplicaciones", "Count": "Total", "TableHeading": {"Purpose": "<PERSON><PERSON><PERSON><PERSON>", "InHouse": "Interno/Tercero", "ThirdPartyName": "Nombre del Tercero", "Location": "Ubicación"}}, "DPIA": {"Heading": "Requisito DPIA", "y-axis": "Número de Actividades de Procesamiento de Alto Riesgo", "x-axis": "Departamento"}, "ROPAV3": {"ROPA Management": "Gestión ROPA", "ROPA Dashboard": "Panel ROPA", "ROPA Assessments": "Evaluaciones ROPA", "Overview of all processing activity assessments": "Descripción general de todas las evaluaciones de la actividad de procesamiento", "Comprehensive Records of Processing Activities management and compliance tracking": "Gestión integral de Registros de Actividades de Tratamiento y seguimiento de cumplimiento", "ROPA Management Dashboard": "Panel de Gestión ROPA", "Records of Processing Activities (Article 30 GDPR Compliance)": "Registros de Actividades de Tratamiento (Cumplimiento del Artículo 30 RGPD)", "Select Entity": "Seleccionar Entidad", "Active assessments": "Evaluaciones activas", "Total ROPAs": "ROPAs totales", "Yet to Start": "<PERSON>r comenzar", "In Progress": "En progreso", "Completed": "Completado", "Processing Activities by Organization Role": "Actividades de Tratamiento por Rol Organizacional", "Total count": "Recuento total", "Loading organization role data...": "Cargando datos de rol organizacional...", "Failed to load organization role data": "Error al cargar datos de rol organizacional", "Controller": "Responsable del Tratamiento", "Joint-Controller": "Corresponsable del Tratamiento", "Processor": "<PERSON><PERSON><PERSON> del Tratamiento", "Sub-Processor": "Subencargado del Tratamiento", "ROPAs": "ROPAs", "Processing Activities by Department": "Actividades de Tratamiento por Departamento", "Total processes": "Procesos totales", "Departments": "Departamentos", "Error loading department data": "Error al cargar datos del departamento", "processes": "procesos", "ROPA Progress": "Progreso ROPA", "Total": "Total", "Completed Department": "Completado", "In Progress Department": "En progreso", "No department data available": "No hay datos de departamento disponibles", "List of Third Parties by Department": "Lista de terceros por departamento", "Loading...": "Cargando...", "Failed to load data.": "Error al cargar datos.", "Vendor": "<PERSON><PERSON><PERSON><PERSON>", "Services": "<PERSON><PERSON><PERSON>", "Department": "Departamento", "Location": "Ubicación", "Personal Data Involved": "Datos Personales Involucrados", "No data available": "No hay datos disponibles", "No records found.": "No se encontraron registros.", "Active Collaborators": "Colaboradores Activos", "Team members working on ROPA assessments": "Miembros del equipo trabajando en evaluaciones ROPA", "Loading collaborators...": "Cargando colaboradores...", "Failed to fetch collaborator data": "Error al obtener datos de colaboradores", "Failed to load collaborator data": "Error al cargar datos de colaboradores", "No active collaborators found": "No se encontraron colaboradores activos", "ROPAs assigned": "ROPAs asignados", "completed": "completados", "Complete": "Completo", "Progress": "Progreso", "more": "más", "total ROPAs": "ROPAs totales", "Recent ROPA Activities": "Actividades ROPA Recientes", "Latest updates on processing activity assessments": "Últimas actualizaciones sobre evaluaciones de actividades de tratamiento", "View All": "<PERSON><PERSON>", "Loading recent activities...": "Cargando actividades recientes...", "No recent ROPA activities found": "No se encontraron actividades ROPA recientes", "Assignee": "Asignado a", "Importing a new document will result in the loss of all previous ROPA performed. Are you sure you want to continue?": "Importar un nuevo documento resultará en la pérdida de todos los ROPA anteriores realizados. ¿Está seguro de que desea continuar?", "Manage Records of Processing Activities assessments": "Gestionar evaluaciones de Registros de Actividades de Tratamiento", "Department Level": "<PERSON><PERSON> de Departamento", "Process Level": "Nivel de Proceso"}}, "Activity": {"TableHeading": {"Department": "Departamento", "Process": "Unidad de Proceso", "SPOC": "Punto de Contacto", "StartDate": "Fecha de Inicio", "UpdatedDate": "<PERSON>cha <PERSON>ual<PERSON>", "AssignedTo": "Asignado A", "is_assigned": "Asignado a Mí", "Reviewer": "Revisor", "progress": "Progreso", "Review": "Revisión", "Risks": "Riesgo", "Status": "Estado", "Action": "Acción", "Import": "Importar", "ImportNewFile": "Importar Nuevo Archivo", "DownloadSampleFile": "Descargar Archivo de Ejemplo", "Filter": "Filtrar", "ClearFilter": "<PERSON><PERSON><PERSON>", "Start": "Iniciar", "Re-Start": "Reiniciar", "View": "<PERSON>er", "Assign": "<PERSON><PERSON><PERSON>", "ROPA Details": "Detalles de ROPA", "Type": "Tipo", "Assignment": "Asignación"}}, "ViewDetails": {"Reviewer": "Revisor", "AssignedTo": "Asignado a", "Department": "Departamento", "Entity": "Entidad", "UpdatedDate": "Fecha de Actualización", "SPOC": "Punto Único de Contacto (SPOC)", "Review": "Revisión", "Start": "Iniciar", "Assign": "<PERSON><PERSON><PERSON>", "CollaboratorsProgressInRopa": "Progreso del Category en ROPA", "RopaDetails": "Detalles de ROPA", "TentativeCompletionDate": "Fecha de Finalización Estimada", "PickaDate": "Selecciona un rango de fechas", "Category": "Categoría", "Collaborators": "Colaboradores", "ROPADetails": "Detalles de ROPA"}, "DataCatalogue": {"DashBoard": {"PIIHandBook": "Manual de PII", "TabList": {"Structured": "Estructurado", "Unstructured": "No estructurado"}, "Structured": {"ServiceOwner": "Propietario del Servicio", "DataManagement": "Gestión de Datos", "DataDetails": "Detalles de Datos", "TotalPIIs": "Total de PII", "TotalPIICategories": "Total de Categorías de PII", "DistinctPIIsDetected": "PII distintos detectados", "DataLocations": {"DataLocations": "Ubicaciones de Datos", "DataLocation": "Ubicación de Datos", "DataElements": "Elementos de Datos", "Databases": "Bases de Datos", "Schemas": "Esquemas", "Tables": "Tablas", "DataSystems": "<PERSON><PERSON><PERSON>"}, "SankeyGraph": {"DisplayAllData": "Mostrando todos los elementos de datos", "NoDataAvailable": "No hay datos disponibles"}, "DataSensitivityCard": {"Data Sensitivity": "Sensibilidad de Datos", "NoDataAvailable": "No hay datos disponibles"}, "PIICategories": "Categorías de PII", "PIIDistributions": {"PII": "PII", "PIICount": "Recuento de PII", "PIIDistribution": "Distribución de PII", "Columns": "Columnas", "Schemas": "Esquemas", "Database": "Base de Datos", "DataSystems": "<PERSON><PERSON><PERSON>", "Location": "Ubicación"}}, "Unstructured": {"DataService": "<PERSON><PERSON><PERSON>", "DataLocations": "Ubicaciones de Datos", "DataManagement": "Gestión de Datos", "ScannedDocuments": "Documentos Escaneados", "ScannedVolume": "Volumen Escaneado", "ScannedFileFormat": "Formato de Archivo Escaneado", "DataDetails": "Detalles de Datos", "TotalPIICategories": "Total de Categorías de PII", "DistinctPIIsDetected": "PII distintos detectados", "TotalDetectedPIIs": "Total de PII Detectados", "SankeyGraph": {"DisplayAllElements": "Mostrando todos los elementos de datos", "NoDataAvailable": "No hay datos disponibles"}, "DocumentCards": {"Documents": "Documentos", "DocumentName": "Nombre del Documento", "DocumentType": "Tipo de Documento", "PIIsDetected": "PII Detectados", "DocumentSize": "Tamaño del Documento", "Location": "Ubicación", "DataService": "<PERSON><PERSON><PERSON>", "SubService": "Subservicio"}, "DataLocationCard": {"DataLocation": "Ubicación de Datos", "DataServiceLoc": "Ubicación del Servicio de Datos", "Document": "Documento", "DocumentType": "Tipo de Documento", "DataService": "<PERSON><PERSON><PERSON>"}, "DataSensitivityCard": {"DataSensitivity": "Sensibilidad de Datos", "NoDataAvailable": "No hay datos disponibles"}, "FileFormats": "Formatos de Archivo", "PIICard": {"PII": "PII", "PIICount": "Recuento de PII", "Documents": "Documentos", "Location": "Ubicación", "DataService": "<PERSON><PERSON><PERSON>"}, "Services": "<PERSON><PERSON><PERSON>", "ID": "ID", "ServiceName": "Nombre del servicio", "ServiceType": "Tipo de servicio", "Status": "Estado", "Running": "En ejecución", "Done": "Completado", "Failed": "Fallido", "NA": "N/A", "Action": "Acción", "AddNewService": "Agregar nuevo servicio", "SelectIngestion": "Por favor seleccione una ingesta", "Microsoft365": "Microsoft 365", "Aws": "AWS", "GoogleWorkspace": "Google Workspace", "AzureDataLake": "Azure Data Lake"}, "PIIList": {"AddNew": "<PERSON><PERSON><PERSON>ue<PERSON>", "PIICategory": "Categoría de PII", "PIIValue": "Valor de PII", "PIIDescription": "Descripción de PII", "Applicability": "Aplicabilidad", "Sensitivity": "Sensibilidad", "Actions": "Acciones", "EnterDescription": "Introduce tu descripción aquí"}}, "TotalColumns": "Total de Columnas", "TotalTables": "Total de Tablas", "TotalDatabases": "Total de Bases de Datos", "DistinctPIIsCount": "Conteo Distinto de PII", "ColumnName": "Nombre de la Columna", "TableName": "Nombre de la Tabla", "ServiceName": "Nombre del Servicio", "DataCategory": "Categoría de Datos", "PIIDetected": "PII Detectado", "Sensitivity": "Sensibilidad", "DataSystem": "Sistema de Datos", "DataSystemOwner": "Propietario del Sistema de Datos", "DataServiceCount": "Conteo de Servicios de Datos", "ScannedDocumentCount": "Conteo de Documentos Escaneados", "TotalVolume": "Volumen Total", "FileFormatCount": "Conteo de Formatos de Archivo", "DocumentName": "Nombre del Documento", "DocumentType": "Tipo de Documento", "PIIsDetected": "PII Detectados", "Size": "<PERSON><PERSON><PERSON>", "Location": "Ubicación", "DataService": "<PERSON><PERSON><PERSON>", "SubService": "Subservicio", "FileLocation": "Ubicación del archivo", "DropHere": "Suelta aquí para adjuntar o", "Upload": "Subir", "SelectedFiles": "Archivos Seleccionados", "MaxSize": "<PERSON><PERSON><PERSON> Máximo: 10MB", "AcceptedFormats": "Formato<PERSON>", "Datatype": "T<PERSON><PERSON> de <PERSON>", "Tags": "Etiquetas", "PIIType": "Tipo de PII", "Version": "Versión", "TagDetails": "Detalles de la Etiqueta", "DataSystemCount": "Conteo de Sistemas de Datos", "DetectedData": "<PERSON><PERSON>", "ConfidenceScore": "Puntaje de Confianza"}, "ChartData": {"Controller": "Controlador", "Joint-Controller": "Controlador conjunto", "Processor": "Procesador", "Sub-Processor": "Subprocesador", "Finance": "Finanzas", "AI": "IA", "Cloud": "Nube", "Frontend": "Interfaz", "ML": "Aprendizaje automático", "Consultancy": "Consultoría", "Backend": "Backend", "HR": "Recursos Humanos", "IT": "TI", "DevOps": "DevOps", "Sales": "Ventas", "Consent": "Consentimiento", "PII": "Información de identificación personal", "SPI": "Información personal sensible", "Performance of a contract": "Ejecución de un contrato", "A legitimate interest": "Un interés legítimo", "A vital interest": "Un interés vital", "A legal requirement": "Un requisito legal"}}, "Policy": {"Dashboard": {"Total": "Número Total de Políticas", "InUse": "Número Total de Políticas en Uso", "Expiring": "Número Total de Políticas que Expiran en 45 Días", "Policies": "Políticas", "NoOfPolicies": "Número de Políticas", "WorkflowHeading": "Políticas por etapa del flujo de trabajo", "Entities": "Entidades", "EntitiesHeading": "Políticas por Entidades", "DepartmentsHeading": "Políticas por Departamentos"}, "AllPolicies": {"PolicyName": "Nombre de la Política", "PolicyCategory": "Categoría de la Política", "Entity": "Entidad", "Recurrence": "Recurrencia", "RenewalDate": "Fecha de Renovación", "WorkflowStage": "Etapa del Flujo de Trabajo", "Department": "Departamento", "Action": "Acción", "CreationofPolicy": "Creación de la Política", "ReviewofPolicy": "Revisión de la Política", "ApprovalofPolicy": "Aprobación de la Política", "PolicyinUse": "Política en Uso"}, "NewPolicyModal": {"Heading": "Crear Nueva Política", "PolicyName": "Ingrese el Nombre de la Política", "PolicyDescription": "Descripción de la Política", "PolicyCategory": "Categoría de la Política", "EntityName": "Nombre de la Entidad", "Language": "Idioma", "PolicyAuthor": "Autor de la Política", "PolicyReviewer": "Revisor de la Política", "PolicyApprover": "Aprobador de la Política", "EffectiveDate": "Fecha de Entrada en Vigor", "RecurrencePeriod": "Periodo de Recurrencia", "Department": "Departamento", "VersionNumber": "Número de Versión", "PolicyID": "ID de la Política", "RelevantStandard/Law": "<PERSON>/<PERSON><PERSON>", "Create": "CREAR"}, "PolicyRequirement": {"Heading": "Requisito de la Política", "Reviewer": "Revisor", "Approver": "Aprobador", "Department": "Departamento", "Entity": "Entidad", "ReviewDate": "<PERSON><PERSON> de Revisión", "Recurrence": "Recurrencia", "Collaborator": "Colaborador"}, "PolicyAttachment": {"Heading": "Adjunto", "CreateWithAI": "Crear Política con IA", "UpdateWithAI": "Actualizar Política con IA", "UploadedAttachment": "Adjunto Subido"}, "AddAttachment": {"Heading": "Crear Nueva Versión", "DropHere": "Arrastre aquí para adjuntar o", "Upload": "subir", "FileType": "Archivo PDF o Word | Tamaño máximo: 5MB", "URL": "URL", "PasteURL": "<PERSON><PERSON><PERSON> U<PERSON>"}}, "AssessmentManagement": {"Dashboard": {"TotalAssessments": "Número total de evaluaciones", "ByReadiness": "Distribución por preparación", "ByRegulation": "Por tipo de regulación", "ByOwners": "Evaluación por propietarios", "RecentAssessments": "Evaluaciones recientes", "ViewAll": "Ver todo", "AssessmentName": "Nombre de la evaluación", "Owner": "Propietario", "Entity": "Entidad", "Department": "Departamento", "ProcessUnit": "Unidad de proceso", "StartDate": "Fecha de inicio", "UpdatedDate": "<PERSON><PERSON>", "Reviewer": "Revisor", "AssignedTo": "Asignado a", "Risks": "<PERSON><PERSON><PERSON>", "Progress": "Progreso", "Status": "Estado"}, "ViewDetails": {"CollaboratorsProgressInRopa": " Categoría Progreso en la Evaluación"}}, "DSR": {"Dashboard": {"Total": "Número total de solicitudes", "Request": "Solicitud", "RequestStats": "Estadísticas de solicitudes", "Approved Requests": "Solicitudes aprobadas", "Pending Requests": "Solicitudes pendientes", "Rejected Requests": "Solicitudes rechazadas", "Completed Requests": "Solicitudes completadas", "Extended": "Solicitudes extendidas", "Reject in Progress": "Rechazo en proceso", "RequestsByRights": "Solicitudes por derechos", "TotalCount": "Total", "RequestStatistics": "Estadísticas de solicitudes", "MoreThan": "Más <PERSON>", "newRequests": "nuevas solicitudes", "RecentRequests": "Solicitudes recientes", "Monthly": "<PERSON><PERSON><PERSON>", "Annually": "<PERSON><PERSON>", "NumberOfApprovedRequest": "Número de solicitudes aprobadas", "RequestsByStages": "Solicitudes por etapas", "Verify": "Verificar", "Stages": "Etapas", "days": "días", "NoData": "No hay datos", "ApproachingDeadline": "Fecha límite próxima", "Acknowledgement": "Confirmación", "Count": "Cantidad", "RequestByResidency": "Solicitudes por residencia", "RequestStatusPerOwner": "Estado de solicitud por propietario", "Completed": "Completado", "Pending": "Pendiente", "Last7days": "Últimos 7 días", "Last14days": "Últimos 14 días", "Last30days": "Últimos 30 días", "Last60days": "Últimos 60 días", "All": "Todo", "RequestTypes": "Tipos de solicitud"}, "TaskOverView": {"CreateRequest": "<PERSON><PERSON><PERSON> solicitud", "CreateForm": "Crear formulario", "DueDays": "Días hasta el vencimiento de la tarea", "TaskType": "Tipo de tarea", "Automation Workflow": "Flujo de trabajo de automatización", "EnterTaskTitle": "Ingrese el título de la tarea", "UpdateTask": "Actualizar tarea", "UpdateNote": "Actualizar nota", "Approve": "<PERSON><PERSON><PERSON>", "Reject": "<PERSON><PERSON><PERSON>", "DataSubjectRequestDetailsID": "ID de Detalles de Solicitud del Sujeto de Datos", "RequestID": "ID de Solicitud", "NoResult": "Sin resultado", "Assign": "<PERSON><PERSON><PERSON>", "View": "<PERSON>er", "EmailVerified": "Correo Electrónico Verificado", "Region": "Región", "RequestDate": "<PERSON><PERSON>", "RequestedBy": "Solicitado Por", "RejectRequest": "¿Estás seguro de que deseas rechazar esta solicitud?", "ConfirmApprove": "¿Estás seguro de que deseas aprobar esta solicitud?", "Attachments": "<PERSON><PERSON><PERSON>", "NoAttachmentsAvailable": "No hay archivos adjuntos disponibles", "PostalCode": "Código Postal", "Location": "Ubicación", "Email": "Correo Electrónico", "FirstName": "Nombre", "LastName": "Apellido", "Phone": "Teléfono", "UserDetails": "Detalles del Usuario", "RequestDescription": "Descripción de la Solicitud", "Status": "Estado", "RelationshipWithUs": "Relación con Nosotros", "RequestType": "Tipo de Solicitud", "CreatedOn": "Creado el", "RequestInformation": "Información de la Solicitud", "AddNote": "<PERSON><PERSON><PERSON><PERSON>", "Approved": "Aprobado", "COMPLETED": "COMPLETADO", "APPROVED": "APROBADO", "Rejected": "<PERSON><PERSON><PERSON><PERSON>", "RejectionInProgress": "Rechazo en Progreso", "Extended": "Extendido", "Confirm": "Confirmar", "ConfirmExtend": "¿Estás seguro de que deseas extender la fecha límite?", "Approver": "Aprobador", "Deadline": "Fecha <PERSON>", "BusinessUnit": "Unidad de Negocio", "WebForm": "Formulario Web", "DSAR": "Formulario DSAR (V12)", "Form Preview": "Vista Previa del Formulario", "PreferredLanguage": "Idioma Preferido", "DSRID": "ID de DSR", "SubjectType": "Tipo de Sujeto", "FullName": "Nombre Completo", "StepsStatus": "Estado de los Pasos", "Assigned": "<PERSON><PERSON><PERSON>", "WantToApprove": "¿Estás seguro de que deseas aprobar?", "WantToReject": "¿Estás seguro de que deseas rechazar?", "ReasonOfRejection": "Motivo del Rechazo", "Action": "Acción", "UploadCompletionDocuments": "Subir documentos de finalización", "ClickToUploadDocuments": "Haz clic para subir documentos", "FormPreview": "Vista previa del formulario", "TasksTitle": "<PERSON><PERSON><PERSON><PERSON>", "NotStarted": "No iniciado", "InProgress": "En progreso", "RejectTask": "<PERSON><PERSON><PERSON>", "Steps": "Pasos", "Attachment": "Adjunto", "DownloadAuditLog": "<PERSON><PERSON><PERSON>", "Automate": "Automatizar"}, "EmailTemplate": {"ConfirmDelete": "Confirmar eliminación", "CreateTemplate": "Crear plantilla", "ConfirmDeleteQuestion": "¿Estás seguro de que deseas eliminar esta plantilla?", "CancelBtn": "<PERSON><PERSON><PERSON>", "DeleteBtn": "Eliminar", "EmailTemplate": "Plantilla de correo electrónico", "Modified": "Modificado", "CreatedAt": "Creado el", "Email": "Correo electrónico", "Name": "Nombre", "Action": "Acción", "Preview": "Vista previa", "TemplateName": "Asunto / Nombre de la plantilla", "CreateEmailTemplate": "Crear plantilla de correo electrónico", "EmailTemplatePreview": "Vista previa de la plantilla de correo electrónico"}, "FormBuilder": {"FormBuilder": "Constructor de formularios", "FormName": "Nombre del formulario", "URLNotAvailableForDraftForms": "URL no disponible para formularios en borrador", "Regulations": "Reglamentos", "VerificationMethod": "Método de verificación", "ConfirmDeleteForm": "¿Estás seguro de que deseas eliminar esto?", "Form": "formulario", "ActionVersion": "Versión de acción", "URL": "URL", "Action": "Acción", "Entity": "Entidad", "LastUpdated": "Última actualización", "Email": "Correo electrónico", "Published": "Publicado", "UploadingLogo": "Subiendo el logotipo...", "LogoUploadedSuccessfully": "Logotipo subido con éxito", "FailedToUploadLogo": "Error al subir el logotipo", "SaveFormBeforeCreatingRules": "Guarde el formulario antes de crear reglas", "FillAllRuleFields": "Por favor, complete todos los campos de la regla", "SelectValidFieldsForRule": "Seleccione campos válidos para la regla", "ViewCodeSnippets": "Ver fragmentos de código", "ViewForm": "Ver formulario", "ViewFormDescription": "Ver detalles del formulario y fragmentos de código de integración", "FormDetails": "Detalles del formulario", "BusinessUnit": "Unidad de negocio", "Status": "Estado", "Draft": "<PERSON><PERSON><PERSON>", "FormID": "ID del formulario", "CodeSnippets": "Fragmentos de código", "CodeSnippetsDescription": "Use los siguientes fragmentos de código para integrar este formulario DSR en su aplicación móvil", "MobileSDKCode": "Código SDK móvil", "WebLink": "Enlace URL web", "FormPreview": "Vista previa del formulario", "CodeSnippet": "Fragmento de código", "RulesAppliedSuccessfully": "Reglas aplicadas con éxito", "OptionRemovedSuccessfully": "¡Opción eliminada con éxito!", "FailedToRemoveOption": "Error al eliminar la opción", "CustomerIDAndFormIDRequired": "Se requiere ID de cliente y ID de formulario", "FailedToSaveFormContent": "No se pudo guardar el contenido del formulario", "URLCopiedToClipboard": "URL copiada al portapapeles", "PublicURLGenerated": "URL pública generada", "FailedToGeneratePublicURL": "No se pudo generar la URL pública", "FormSubmissionFailed": "Error al enviar el formulario", "TranslateForm": "Traducir formulario", "DoYouWantToTranslateThisForm": "¿Deseas traducir este formulario?", "NoPublishNow": "No, publicar ahora", "YesTranslateForm": "Sí, traducir formulario", "SelectLanguage": "Seleccionar idioma", "ChooseLanguage": "Elige un idioma", "FormURL": "URL del formulario", "Cancel": "<PERSON><PERSON><PERSON>", "Translating": "Traduciendo...", "StartTranslation": "<PERSON><PERSON><PERSON>"}, "WorkFlow": {"WorkFlow": "Flujo de trabajo", "EnterFlowType": "Ingrese tipo de flujo", "AddWorkflow": "Agregar flujo de trabajo", "TaskTitle": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "Department": "Departamento", "AddTask": "Agregar tarea", "StartDate": "Fecha de inicio", "DueDate": "<PERSON><PERSON>nc<PERSON>o", "AddAssignee": "<PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON>", "GuidanceText": "Texto de orientación", "AddFiles": "Agregar archivos", "TaskChecklist": "Lista de verificación de tareas", "UploadedDocuments": "Documentos subidos", "ConfirmDeleteFile": "¿Estás seguro de que deseas eliminar este archivo?", "DeleteFile": "Eliminar archivo", "Yes": "Sí", "SelectEntity": "Seleccionar entidad", "SelectRegulations": "Seleccionar regulaciones"}, "AssigneeModal": {"Assignee": "<PERSON><PERSON><PERSON>", "FlowType": "Tipo de flujo", "DSRForm": "Formulario de solicitud del sujeto de datos", "PublishForm": "Publicar formulario"}}, "Cookies": {"Interaction Count": "Conteggio interazioni", "DownloadReport": "Des<PERSON><PERSON> informe", "View": "Vista previa", "Source": "Fuente", "ScanFinished": "Escaneo finalizado", "IsThisTheDefaultBanner": "¿Es este el banner predeterminado?", "IfThisIsTheDefaultBanner": "Si este es el banner predeterminado, se mostrará a todos los usuarios que visiten el dominio cuando no se especifica la región.", "DefaultBanner": "Banner predeterminado", "PolicyUpdatedSuccessfully": "Política actualizada con éxito", "FailedToAddPolicy": "Error al agregar política", "FailedToUpdatePolicy": "Error al actualizar política", "PolicyAddedSuccessfully": "Política agregada con éxito", "Consent": "Consentimiento", "Details": "<PERSON><PERSON><PERSON>", "About": "Informazioni", "ThisWebsiteUsesCookies": "Este sitio web utiliza cookies", "AllCategories": "Todas las categorías", "SelectCategory": "Seleccionar categoría", "BannerDescription": "Utilizamos cookies para personalizar contenido y anuncios, para proporcionar funciones de redes sociales y para analizar nuestro tráfico. También compartimos información sobre su uso de nuestro sitio con nuestros socios de redes sociales, publicidad y análisis que podrían combinarla con otras informaciones que ha proporcionado o recopilado de su uso de sus servicios.", "OnlyNecessary": "Solo necesario", "AllowSelection": "<PERSON><PERSON><PERSON>", "AllowAll": "<PERSON><PERSON><PERSON>", "AboutSectionContent": "Los cookies son pequeños archivos de texto que pueden ser utilizados por los sitios web para hacer más eficiente la experiencia del usuario. La ley afirma que podemos almacenar cookies en tu dispositivo si son estrictamente necesarias para el funcionamiento de este sitio. Para todos los demás tipos de cookies necesitamos tu permiso. Este sitio utiliza varios tipos de cookies. Algunos cookies son insertados por servicios de terceros que aparecen en nuestras páginas. Puedes en cualquier momento modificar o revocar tu consentimiento desde la Declaración de cookies en nuestro sitio web. Descubre más sobre quiénes somos, cómo puedes contactarnos y cómo procesamos tus datos personales en nuestra Política de privacidad. Por favor, indica el ID del consentimiento y la fecha cuando nos contactes en relación con tu consentimiento.", "DefaultBannerError": "Debe haber exactamente un banner predeterminado. Por favor, ajuste la selección.", "ChooseRegulation": "Seleccionar regulación", "CreateOrUseExisting": "¿Quieres crear una nueva?", "Existing": "Existente", "New": "Nueva", "SelectOrCreatePolicy": "Selecciona una política de cookies existente o elige crear una nueva.", "SelectVersion": "Selecciona una versión", "SelectCookiePolicy": "Selecciona una política de cookies", "Version": "Versión", "TranslatedLanguages": "Idiomas traducidos", "SavingTranslation": "Guardando la traducción...", "TranslationSaved": "Traducción guardada con éxito", "SaveTranslation": "Guardar <PERSON>", "UnableToTranslate": "No se puede traducir los datos", "UnableToFetchData": "No se puede recuperar los datos", "No Interaction Count": "Recuento de no interacciones", "Last Interaction": "Última interacción", "Cookie Name": "Nombre de la cookie", "Cookie Value": "Valor de la cookie", "Cookie Path": "Ruta de la cookie", "Consent Count": "Recuento de consentimiento", "Declined Count": "Recuento de rechazos", "SubjectIdentity": "Identidad del sujeto", "GeoLocation": "Geolocalización", "CookieCategory": "Categoría de cookies", "Consent Status": "Estado del consentimiento", "Consent Date": "Fecha del consentimiento", "DomainURL": "URL del dominio", "Domain/Subdomain": "Dominio/Subdominio", "Domain Group": "Grupo de dominios", "Last Scanned": "Último es<PERSON>", "Scan Frequency": "Frecuencia de escaneo", "Next Scan": "Próximo escaneo", "Total Cookies": "Total de cookies", "Cookie Policy": "Política de cookies", "NoResults": "No hay resultados.", "Cookie Consent Domain": "Dominio de consentimiento de cookies", "Banner Published": "Banner publicado", "Consent Policy": "Política de consentimiento", "Owner": "Propietario", "NoData": "Sin datos", "Cookie Configuration": "Configuración de cookies", "All Domains": "Todos los dominios", "Basic Information": "Información básica", "Website Scan": "Escaneo del sitio web", "Categorize Cookie": "Categorizar cookie", "Customize Banner": "Personalizar banner", "Language Support": "Soporte de idiomas", "Consent Code": "Código de consentimiento", "AutoScan": "Escaneo automático", "ScanDescription": "Escanee su sitio web para obtener un informe detallado de cookies.", "ScanNow": "Escanear ahora", "Scanning": "Escaneando...", "ScanStarted": "Escaneo iniciado con éxito", "ScanFailed": "Escaneo fallido", "SelectFrequency": "Seleccionar frecuencia", "Weekly": "<PERSON><PERSON><PERSON>", "Monthly": "<PERSON><PERSON><PERSON>", "Yearly": "<PERSON><PERSON>", "Daily": "Diario", "Category": "Categoría", "Service": "<PERSON><PERSON><PERSON>", "Cookie": "<PERSON><PERSON>", "AddCategory": "Añadir categoría", "AddServices": "<PERSON><PERSON><PERSON>", "AddCookies": "<PERSON><PERSON><PERSON> cookies", "ErrorOccurred": "Ha ocurrido un error inesperado.", "WhenShouldICopy": "¿<PERSON>uándo debo copiar esto?", "UseTestCodeInStaging": "Use este código de prueba en su sitio web de prueba antes de implementarlo en su sitio web de producción.", "AddCodeToHead": "Agregue el siguiente código al principio de la etiqueta <head>:", "CookiePolicyURL": "URL de Política de Cookies", "Copied": "Copiado", "DeployedCode": "<PERSON><PERSON><PERSON>", "Granted Count": "<PERSON><PERSON><PERSON>", "IntegrateCookiePolicy": "Para integrar la política de cookies en su sitio web, copie esta URL y péguela en la sección de pie de página de su sitio web.", "TestingCode": "Código <PERSON>rueba", "DeviceInfo": "Información del dispositivo", "Total Users": "Total de Usuarios"}, "DataBreach": {"Dashboard": {"TotalBreaches": "Total de violaciones", "OpenBreaches": "Violaciones abiertas", "ClosedBreaches": "Violaciones cerradas"}}, "CookiePolicy": {"CookiePolicyName": "Política de Cookies", "Version": "Versión", "UpdatedOn": "Actualizado el", "Action": "Acción", "CopiedToClipboard": "¡URL copiada al portapapeles!", "Search": "Buscar", "Create": "<PERSON><PERSON><PERSON>", "Cookie Policy": "Política de Cookies", "View Policy": "Ver Política", "Edit Policy": "Editar Polí<PERSON>", "Create Policy": "<PERSON><PERSON><PERSON>", "SelectEntity": "Seleccionar entidad"}, "CookieCenter": {"Sections": "Secciones", "NoSectionsAvailable": "No hay secciones disponibles", "NewSectionTitle": "Título de la Nueva Sección", "AddSection": "<PERSON>g<PERSON><PERSON>", "SelectSectionFromList": "Selecciona una sección de la lista para editar su contenido", "NoContentYet": "Aún no hay contenido. Haz clic en el botón de editar para agregar contenido.", "SaveChanges": "Guardar Cambios", "UpdateChanges": "<PERSON><PERSON><PERSON><PERSON>", "Save": "Guardar", "ErrorSelectEntity": "Por favor selecciona una entidad para continuar.", "ErrorFillTitle": "Por favor completa el título.", "ErrorAddSection": "Por favor agrega al menos una sección.", "UpdatingPolicy": "Actualizando política...", "AddingPolicy": "Agregando política...", "DoYouWantNewVersion": "¿Desea crear una nueva versión de esta política?", "No": "No", "Yes": "Si", "DiscardChanges": "Descartar cambios"}, "CookiePolicyEditor": {"EnterCookiePolicyTitle": "Ingresa el título de la política de cookies", "SelectEntity": "Seleccionar entidad"}, "ConsentCode": {"TestingCode": "Código <PERSON>rueba", "TestCodeDescription": "Este código de prueba está diseñado para ser utilizado en tu sitio web de prueba antes de implementarlo en tu sitio web de producción.", "TestCodeAddCode": "Agrega el siguiente código al principio de la etiqueta <head>:", "DeployedCode": "Código <PERSON>", "Copied": "Copiado", "WhenShouldICopyThis": "¿Cuándo debería copiar esto?", "CookiePolicyURL": "URL de la Política de Cookies", "CookiePolicyURLDescription": "Para integrar la política de cookies en tu sitio web, copia esta URL y pégala en la sección del pie de página de tu sitio web.", "IntegrateCookiePolicy": "Para integrar la política de cookies en tu sitio web, copia esta URL y pégala en la sección del pie de página de tu sitio web."}, "UniversalConsentManagement": {"downloadFailed": "<PERSON><PERSON><PERSON> fallida", "AddedSuccessfully": "agregado exitosamente", "CouldntCreate": "No se pudo crear", "FillRequiredFields": "Complete los campos obligatorios.", "UploadValidFile": "Cargar archivo válido", "Processing": "Procesando...", "UploadedSuccessfully": "Cargado exitosamente", "UnexpectedError": "Ocurrió un error inesperado", "ProcessingIn": "Procesando en...", "NameIsRequired": "El nombre es obligatorio", "DescriptionIsRequired": "La descripción es obligatoria", "CategoryIsRequired": "La categoría es obligatoria", "DataRetentionIsRequired": "La retención de datos es obligatoria", "LawfulBasisIsRequired": "La base legal es obligatoria", "PurposeIsRequired": "El propósito es obligatorio", "TypeIsRequired": "El tipo es obligatorio", "ComplianceOfficerIsRequired": "El oficial de cumplimiento es obligatorio", "DataSubjectIsRequired": "El sujeto de datos es obligatorio", "SourceIsRequired": "La fuente es obligatoria", "DataImporterIsRequired": "El importador de datos es obligatorio", "DataExporterIsRequired": "El exportador de datos es obligatorio", "PleaseSelectSource": "Por favor seleccione una fuente.", "TitleDescriptionRequired": "Por favor ingrese un título y descripción para su formulario", "FormDescriptionRequired": "Por favor ingrese una descripción para su formulario", "FormTitleRequired": "Por favor ingrese un título para su formulario", "PleaseSelectConsentPurpose": "Por favor seleccione un propósito de consentimiento", "FillAllRequiredFields": "Complete todos los campos obligatorios.", "NameRequired": "El nombre es obligatorio.", "ConsentDescriptionRequired": "La descripción es obligatoria.", "EnterName": "Ingrese Nombre", "EnterDescription": "Ingrese Descripción", "PleaseEnterAllFields": "Por favor ingrese todos los campos", "ConsentPurposeUpdatedSuccessfully": "Propósito de consentimiento actualizado exitosamente", "ConsentPurposeDeletedSuccessfully": "Propósito de consentimiento eliminado exitosamente", "ProcessingPurposeUpdatedSuccessfully": "Propósito de procesamiento actualizado exitosamente", "ProcessingPurposeDeletedSuccessfully": "Propósito de procesamiento eliminado exitosamente", "PiiLabelUpdatedSuccessfully": "Etiqueta Pii actualizada exitosamente", "PiiLabelDeletedSuccessfully": "Etiqueta Pii eliminada exitosamente", "RecordUpdatedSuccessfully": "Registro actualizado exitosamente", "PrivacyNoticeSavedSuccessfully": "Aviso de privacidad guardado exitosamente", "TemplateDownloadedSuccessfully": "Plantilla descargada exitosamente", "TemplateActivatedSuccessfully": "Plantilla activada exitosamente", "TemplateInactivatedSuccessfully": "Plantilla desactivada exitosamente", "CouldntUpdateConsentPurpose": "No se pudo actualizar el propósito de consentimiento", "CouldntUpdatePiiLabel": "No se pudo actualizar la etiqueta pii", "CouldntUpdateProcessingPurpose": "No se pudo actualizar el propósito de procesamiento", "CouldntUpdateRecordData": "No se pudieron actualizar los datos del registro"}, "CommonErrorMessages": {"Sending": "Enviando", "AddingWorkflowNewStep": "Añadiendo nuevo paso al flujo de trabajo...", "UpdatingTaskAutomation": "Actualizando automatización de tareas...", "RemovingAutomation": "Eliminando automatización...", "UpdatingTask": "Actualizando tarea...", "DownloadingAuditLog": "Descargando registro de auditoría...", "SendingConsentLink": "Enviando enlace de consentimiento...", "Saving": "Guardando...", "VerifyingEmail": "Verificando correo electrónico...", "AccessExpired": "El acceso ha expirado. Por favor, renueve su suscripción.", "AccountLocked": "La cuenta ha sido bloqueada. Por favor, contacte al soporte.", "AccountNotVerified": "La cuenta no está verificada. Por favor, revise su correo electrónico.", "AnErrorOccurred": "Ocurrió un error", "AnUnexpectedErrorOccurred": "Ocurrió un error inesperado.", "AnswerSavedSuccessfully": "Respuesta guardada exitosamente", "AssessmentCreatedSuccessfully": "Evaluación creada exitosamente", "AssessmentReviewedSuccessfully": "¡Evaluación revisada exitosamente!", "AssigningUserFailed": "¡Falló la asignación de usuario!", "AuthenticationRequired": "Se requiere autenticación.", "AuthorizationFailed": "Falló la autorización.", "BadRequest": "Solicitud incorrecta. Por favor, verifique su entrada.", "BandwidthExceeded": "Límite de ancho de banda excedido.", "CPULimitExceeded": "Límite de CPU excedido.", "CacheError": "Error de operación de caché.", "CalculationError": "<PERSON><PERSON><PERSON>.", "CertificateError": "Error de validación de certificado.", "CheckConstraintViolation": "Violación de restricción de verificación.", "ChooseAtLeastOneCollaborator": "elija al menos un colaborador", "CircuitBreakerOpen": "El disyuntor está abierto.", "CompressionError": "Error de compresión de datos.", "ConcurrentModification": "El recurso fue modificado por otro usuario.", "ConfirmDelete": "¿Está seguro de que desea eliminar este elemento?", "ConfirmSave": "¿Está seguro de que desea guardar los cambios?", "ConfirmUpdate": "¿Está seguro de que desea actualizar este elemento?", "Conflict": "Ocurrió un conflicto. El recurso puede haber sido modificado.", "ConnectionLost": "Conexión perdida. Por favor, verifique su conexión a internet.", "ConnectionRefused": "Conexión rechazada por el servidor.", "ConversionError": "Error de conversión de datos.", "CouldntAddVendor": "No se pudo agregar el proveedor", "CouldntSaveAnswers": "No se pudieron guardar las respuestas", "CouldntSaveReviews": "No se pudieron guardar las revisiones", "CouldntStartAssessment": "No se pudo iniciar la evaluación", "CouldntDownloadAssessment": "No se pudo descargar la evaluación", "CouldntUpdateVendor": "No se pudo actualizar el proveedor", "DNSError": "Error de resolución DNS.", "DataDeletedSuccessfully": "Datos eliminados exitosamente", "DataSavedSuccessfully": "Datos guardados exitosamente", "DataTruncation": "Error de truncamiento de datos.", "DataUpdatedSuccessfully": "Datos actualizados exitosamente", "DatabaseError": "Ocurrió un error de base de datos.", "DateTimeError": "Error de formato de fecha/hora.", "DecryptionError": "Error de descifrado de da<PERSON>.", "DeleteFailed": "Falló la eliminación", "DependencyError": "No se puede completar la operación debido a dependencias.", "DeserializationError": "Error de deserialización de datos.", "DivisionByZero": "Error de división por cero.", "DomainError": "Error de dominio matemá<PERSON>o.", "DuplicateKey": "Error de clave duplicada.", "EmailAlreadyExists": "La dirección de correo electrónico ya existe.", "EmailNotFound": "Dirección de correo electrónico no encontrada.", "EncodingError": "Error de codificación de caracteres.", "EncryptionError": "<PERSON><PERSON>r de cifrado de da<PERSON>.", "EntityIdRequiredForDeletion": "Se requiere ID de entidad para la eliminación", "Error": "Error", "ErrorDot": "Error.", "ErrorSubmittingForm": "Error al enviar el formulario", "ExecutionTimeout": "Tiempo de ejecución agotado.", "ExternalServiceError": "Ocurrió un error de servicio externo.", "FailedToCreateAssessment": "Falló la creación de la evaluación", "FailedToDeleteData": "Falló la eliminación de datos", "FailedToFetch": "Falló la obtención", "FailedToFetchData": "Falló la obtención de datos.", "FailedToSaveData": "Falló el guardado de datos", "FailedToUpdateData": "Falló la actualización de datos", "FeatureNotAvailable": "Esta función no está disponible en su plan actual.", "FileSizeExceeded": "El tamaño del archivo excede el límite máximo", "FileUploadFailed": "Falló la carga del archivo", "FileUploadSuccessful": "Archivo cargado exitosamente", "FirewallBlocked": "Solicitud bloqueada por firewall.", "ForeignKeyConstraint": "Violación de restricción de clave foránea.", "FormSubmittedSuccessfully": "Formulario enviado exitosamente", "FormatError": "Error de formato de datos.", "HashingError": "Error de hash de datos.", "HostUnreachable": "Host inalcanzable.", "IntegrityConstraintViolation": "Violación de restricción de integridad de datos.", "InvalidConfiguration": "Configuración inválida detectada.", "InvalidCredentials": "Nombre de usuario o contraseña inválidos.", "InvalidFileFormat": "Formato de archivo inválido", "InvalidInput": "Entrada inválida proporcionada", "InvalidOperation": "Operación matemática inválida.", "InvalidState": "Estado inválido para esta operación.", "InvalidToken": "Token inválido o expirado.", "LoadBalancerError": "Error del balanceador de carga.", "LoadingData": "Cargando...", "LoadingTranslations": "Cargando traducciones...", "MaintenanceMode": "El sistema está en mantenimiento. Por favor, inténtelo de nuevo más tarde.", "MappingDeletedSuccessfully": "Mapeo eliminado exitosamente", "MappingError": "Error de mapeo de datos.", "MemoryLimitExceeded": "Límite de memoria excedido.", "MessageExpired": "El mensaje ha expirado.", "NetworkTimeout": "Error de tiempo de espera de red.", "NotFound": "El recurso solicitado no fue encontrado.", "NumericOverflow": "Error de desbordamiento numérico.", "OperationFailed": "Falló la operación", "OperationNotAllowed": "Esta operación no está permitida.", "OperationSuccessful": "Operación completada exitosamente", "OverflowError": "<PERSON><PERSON>r de desbordam<PERSON>o.", "ParsingError": "<PERSON><PERSON><PERSON> de aná<PERSON> de <PERSON>.", "PartialFailure": "Operación completada con fallas parciales.", "PasswordMismatch": "Las contraseñas no coinciden.", "PermissionDenied": "No tiene permisos para realizar esta acción", "PleaseAgreeToAllRequiredConsents": "Por favor, acepte todos los consentimientos requeridos antes de enviar.", "PleaseEnterCorrectURL": "Por favor, ingrese la URL correcta", "PleaseFilTheInput": "Por favor, complete la entrada", "PleaseReviewAllQuestions": "¡Por favor, revise todas las preguntas!", "PortClosed": "El puerto está cerrado o bloqueado.", "PrecisionLoss": "Error de pérdida de precisión.", "ProcessCompleted": "¡Proceso completado exitosamente!", "Processing": "Procesando", "ProcessingEllipsis": "Procesando...", "ProcessingIn": "Procesando en...", "ProgressBarError": "¡Error de barra de progreso!", "ProtocolError": "Error de protocolo.", "ProxyError": "Error del servidor proxy.", "QuestionDeletedSuccessfully": "La pregunta ha sido eliminada exitosamente", "QueueFull": "La cola de mensajes está llena.", "QuotaExceeded": "Cuota excedida. Por favor, actualice su plan.", "RangeError": "<PERSON><PERSON><PERSON> de rango matem<PERSON>.", "RateLimited": "Tasa de solicitudes limitada.", "RequiredFieldsMissing": "Por favor, complete todos los campos requeridos", "ResourceExhausted": "Recursos del sistema agotados.", "ResourceLocked": "El recurso está actualmente bloqueado por otro usuario.", "RetryLimitExceeded": "Límite de reintentos excedido.", "ReviewSavedSuccessfully": "Revisión guardada exitosamente", "RoundingError": "<PERSON><PERSON><PERSON> de redondeo.", "SSLError": "Error de conexión SSL/TLS.", "SavedSuccessfully": "¡Guardado exitosamente!", "SendingOnboardingDataFailed": "¡Falló el envío de datos de incorporación!", "SerializationError": "Error de serialización de datos.", "ServerError": "Ocurrió un error del servidor. Por favor, inténtelo de nuevo más tarde.", "ServiceDegraded": "El servicio está funcionando en modo degradado.", "ServiceUnavailable": "El servicio no está disponible temporalmente.", "SessionTimeout": "Su sesión ha expirado. Por favor, inicie sesión de nuevo.", "SignatureError": "Error de firma digital.", "SomethingWentWrong": "Algo salió mal", "SpecialCharacterNotAllowed": "no se permiten caracteres especiales", "StorageQuotaExceeded": "Cuota de almacenamiento excedida.", "Success": "¡Éxito!", "TextFieldAdded": "Campo de texto agregado", "TimeoutError": "Tiempo de espera de solicitud agotado. Por favor, inténtelo de nuevo.", "TooManyRequests": "<PERSON><PERSON><PERSON><PERSON> solicitudes. Por favor, inténtelo de nuevo más tarde.", "TransformationError": "Error de transformación de datos.", "UnderflowError": "Error de desbordamiento inferior.", "UnsavedChanges": "Tiene cambios sin guardar. ¿Está seguro de que desea salir?", "UserAssignedSuccessfully": "¡Usuario asignado exitosamente!", "UserNotFound": "Usuario no encontrado.", "ValidationError": "Ocurrió un error de validación.", "VendorAddedSuccessfully": "<PERSON>ve<PERSON><PERSON> ag<PERSON>gado exitosamente", "VendorUpdatedSuccessfully": "Proveedor actualizado exitosamente", "VersionMismatch": "Error de discrepancia de versión.", "WeakPassword": "La contraseña es muy débil. Por favor, elija una contraseña más fuerte."}, "FrontEndErrorMessage": {"AccountSetup": {"CompanyStructure": {"BusinessUnitCreatedSuccessfully": "Unidad de negocio creada exitosamente", "DepartmentCreatedSuccessfully": "Departamento creado exitosamente", "DepartmentsCreatedSuccessfully": "Departamentos creados exitosamente.", "FailedToCreateBusinessUnit": "Falló la creación de la unidad de negocio", "FailedToCreateDepartment": "Falló la creación del departamento", "FailedToCreateProcess": "Falló la creación del proceso", "GroupIdRequired": "Se requiere ID de grupo pero no se pudo determinar", "NameIsRequired": "El nombre es requerido", "PleaseFillInField": "Por favor, complete el {field}", "PleaseSelectRegionField": "Por favor, seleccione el campo de región", "PleaseUploadCSVFile": "Por favor, cargue un archivo CSV", "ProcessCreatedSuccessfully": "Proceso creado exitosamente"}, "RoleManagement": {"FailedToAddRole": "Falló la adición del rol", "FailedToDeleteRole": "Falló la eliminación del rol", "FailedToUpdateRole": "Falló la actualización del rol", "RoleAddedSuccessfully": "¡Rol agregado exitosamente!", "RoleDeletedSuccessfully": "¡Rol eliminado exitosamente!", "RoleUpdatedSuccessfully": "¡Rol actualizado exitosamente!"}, "UserManagement": {"FailedToAddUser": "Falló la adición del usuario", "FailedToDeleteUser": "Falló la eliminación del usuario", "FailedToUpdateUser": "Falló la actualización del usuario", "Processing": "Procesando...", "ProcessingIn": "Procesando en...", "UserAddedSuccessfully": "¡Usuario agregado exitosamente!", "UserDeletedSuccessfully": "¡Usuario eliminado exitosamente!", "UserUpdatedSuccessfully": "¡Usuario actualizado exitosamente!"}}, "ApiErrors": {"AccessDenied": "Acceso denegado. Es posible que no tenga permisos para acceder a este recurso.", "AnErrorOccurred": "Ocurrió un error", "BadRequest": "Solicitud incorrecta", "ErrorReportSentSuccessfully": "Reporte de error enviado exitosamente.", "ForbiddenError": "Error 403 Prohibido", "InternalServerError": "Error interno del servidor", "NetworkError": "Error de red.", "NotFound": "Recurso no encontrado", "ServiceUnavailable": "Servicio no disponible", "SomethingWentWrong": "Algo salió mal.", "UnexpectedErrorOccurred": "Ocurrió un error inesperado.", "SomeUpdatesFailed": "Algunas actualizaciones fallaron", "FailedToFetchData": "Error al obtener datos"}, "AssessmentManagement": {"AssessmentCreatedSuccessfully": "Evaluación creada exitosamente", "AssessmentDeletedSuccessfully": "Evaluación eliminada exitosamente", "AssessmentUpdatedSuccessfully": "Evaluación actualizada exitosamente", "ErrorInCreatingQuestion": "Error al crear la pregunta", "FailedToCreateAssessment": "Falló la creación de la evaluación", "FailedToDeleteAssessment": "Falló la eliminación de la evaluación", "FailedToUpdateAssessment": "Falló la actualización de la evaluación"}, "Authentication": {"CodeSentSuccessfully": "¡Código enviado exitosamente!", "FailedToResendOTP": "Falló el reenvío del OTP", "FailedToVerifyOTP": "Falló la verificación del OTP. Por favor, inténtelo de nuevo.", "InvalidCredentials": "Credenciales inválidas", "InvalidOTPPleaseTryAgain": "OTP inválido. Por favor, inténtelo de nuevo.", "LoggingIn": "Iniciando se<PERSON>...", "LoginSuccessful": "Inicio de sesión exitoso", "LogoutSuccessful": "Cierre de sesión exitoso", "OTPVerifiedSuccessfully": "¡OTP verificado exitosamente!", "PasswordUpdatedSuccessfully": "¡Contraseña actualizada exitosamente!", "PasswordsDoNotMatch": "¡Las contraseñas no coinciden!", "PleaseAgreeTermsConditions": "Por favor, acepte nuestros términos y condiciones", "PleaseEnterValidOTP": "Por favor, ingrese un OTP válido", "PleaseProvidePassword": "¡Por favor, proporcione una contraseña!", "SendingCode": "Enviando código...", "SessionExpired": "Sesión expirada", "UnauthorizedAccess": "Acceso no autorizado", "VerificationCodeResentSuccessfully": "¡Código de verificación reenviado exitosamente!"}, "CookieManagement": {"DefaultBannerError": "Debe haber exactamente un banner predeterminado. Por favor, ajuste su selección.", "FailedToAddPolicy": "Falló la adición de la política", "FailedToUpdatePolicy": "Falló la actualización de la política", "PolicyAddedSuccessfully": "Política agregada exitosamente", "PolicyUpdatedSuccessfully": "Política actualizada exitosamente", "SavingTranslation": "<PERSON><PERSON><PERSON> t<PERSON>...", "ScanFailed": "Falló el escaneo", "ScanStartedSuccessfully": "Escaneo iniciado exitosamente", "TranslationSavedSuccessfully": "Traducción guardada exitosamente", "UnableToFetchData": "No se pueden obtener los datos", "UnableToTranslateData": "No se pueden traducir los datos", "AutoScanRequired": "Se requiere el escaneo automático.", "NoGTMConfigurationToApply": "No hay configuración GTM para aplicar", "AdobeLaunchSettingsAppliedToAll": "Adobe Launch Settings Applied To All"}, "DSR": {"FailedToCreateRequest": "Falló la creación de la solicitud", "FailedToDeleteRequest": "Falló la eliminación de la solicitud", "FailedToPublishForm": "Falló la publicación del formulario", "FailedToUpdateRequest": "Falló la actualización de la solicitud", "FormPublishedSuccessfully": "Formulario publicado exitosamente", "RequestCreatedSuccessfully": "Solicitud creada exitosamente", "RequestDeletedSuccessfully": "Solicitud eliminada exitosamente", "RequestUpdatedSuccessfully": "Solicitud actualizada exitosamente", "DocumentsAddedSuccessfully": "¡Documentos añadidos con éxito!", "DocumentDeletedSuccessfully": "¡Documento eliminado con éxito!", "FailedToDeleteDocument": "No se pudo eliminar el documento. Por favor, inténtelo de nuevo.", "FailedToDownloadFile": "No se pudo descargar el archivo. Por favor, inténtelo de nuevo.", "SubjectIsRequired": "El asunto es obligatorio", "EmailContentIsRequired": "El contenido del correo electrónico es obligatorio", "SubmittingForm": "Enviando formulario...", "FormSubmittedSuccessfully": "Formulario enviado con éxito", "FailedToSubmitForm": "Falló el envío del formulario", "UploadingLogo": "Subiendo el logotipo...", "LogoUploadedSuccessfully": "Logotipo subido con éxito", "FailedToUploadLogo": "Falló la subida del logotipo", "TemplateDeletedSuccessfully": "¡Plantilla eliminada con éxito!", "DocumentUploadedSuccessfully": "¡Documento subido con éxito!", "FailedToUploadDocument": "Falló la subida del documento", "DocumentsDeletedSuccessfully": "¡Documentos eliminados con éxito!", "RequestApprovedSuccessfully": "¡Solicitud aprobada con éxito!", "RequestRejectedSuccessfully": "¡Solicitud rechazada con éxito!", "DeadlineExtendedSuccessfully": "¡La fecha límite se ha ampliado con éxito!", "AuditLogDownloading": "Descargando registro de auditoría...", "ProcessingEllipsis": "Procesando...", "UpdatingTask": "Actualizando tarea...", "TaskUpdatedSuccessfully": "Tarea actualizada con éxito", "ErrorUpdatingTask": "Error al actualizar la tarea", "ErrorLoadingData": "Error al cargar los datos", "MessageCannotBeEmpty": "El mensaje no puede estar vacío.", "RequestUnderVerification": "Tu solicitud está en verificación", "ErrorFetchingAssignees": "Se produjo un error al obtener los asignados", "PleaseSelectAssignee": "Por favor, selecciona un asignado", "FailedToAssignUser": "No se pudo asignar el usuario", "ErrorAssigningUser": "Se produjo un error al asignar el usuario", "PleaseEnterEmail": "Por favor, introduce un correo electrónico.", "VerificationCodeSent": "¡Código de verificación enviado con éxito!", "SomethingWrong": "¡Algo salió mal!", "FailedToResendVerificationCode": "No se pudo reenviar el código de verificación.", "FailedToResendVerificationCodeTryAgain": "No se pudo reenviar el código. Intenta nuevamente.", "InvalidOTPTryAgain": "OTP inválido. Intenta nuevamente.", "BusinessUnitUpdated": "Unidad de negocio actualizada con éxito", "Success": "Éxito", "FailedToUpdateTask": "No se pudo actualizar la tarea", "CannotDeleteLastStep": "No se puede eliminar el último paso del flujo de trabajo", "WorkflowStepDeletedSuccessfully": "Paso del flujo de trabajo eliminado con éxito", "FailedToDeleteWorkflowStep": "No se pudo eliminar el paso del flujo de trabajo", "ErrorDeletingWorkflowStep": "Error al eliminar el paso del flujo de trabajo", "StepTitleCannotBeEmpty": "El título del paso no puede estar vacío", "WorkflowIdMissing": "Falta el ID del flujo de trabajo", "Processing": "Procesando...", "WorkflowUpdatedSuccessfully": "Flujo de trabajo actualizado con éxito", "InvalidAPIRoute": "Ruta de API no válida.", "FailedToUpdateWorkflow": "No se pudo actualizar el flujo de trabajo. Intenta más tarde.", "TitleCannotBeEmpty": "El título no puede estar vacío", "ControlUpdatedSuccessfully": "Control actualizado con éxito", "FailedToUpdateControl": "No se pudo actualizar el control", "TitleRequired": "El título es obligatorio", "FailedToAddQuestion": "No se pudo agregar la pregunta", "QuestionAddedSuccessfully": "Pregunta agregada con éxito", "FailedToAddWorkflow": "No se pudo agregar el flujo de trabajo", "WorkflowAddedSuccessfully": "Flujo de trabajo agregado con éxito", "AddingWorkflow": "Agregando flujo de trabajo", "DuplicateWorkflow": "Flujo de trabajo duplicado", "PleaseEnterWorkflowType": "Por favor, introduce un tipo de flujo de trabajo", "WorkflowCreatedSuccessfully": "Flujo de trabajo creado con éxito", "FailedToCreateWorkflow": "No se pudo crear el flujo de trabajo", "ErrorCreatingWorkflow": "Error al crear el flujo de trabajo", "SubmittedSuccessfully": "Enviado con éxito", "ProcessingRequest": "<PERSON>ces<PERSON><PERSON> solicitud", "FiltersAppliedSuccessfully": "Filtros aplicados con éxito", "AddingTask": "Agregando tarea", "TaskAddedSuccessfully": "Tarea agregada con éxito", "FailedToAddTask": "No se pudo agregar la tarea", "UserUnauthaorized": "User unauthorized"}, "DataMapping": {"FailedToAddPii": "Falló la adición de PII", "FailedToDeletePii": "Falló la eliminación de PII", "FailedToUpdatePii": "Falló la actualización de PII", "PiiAddedSuccessfully": "PII agregado exitosamente", "PiiAlreadyExists": "PII ya existe", "PiiDeletedSuccessfully": "PII eliminado exitosamente", "PiiUpdatedSuccessfully": "PII actualizado exitosamente", "PIIAddedSuccessfully": "PII agregado exitosamente", "PIIEditedSuccessfully": "PII editado exitosamente", "PIIDeletedSuccessfully": "PII eliminado exitosamente", "TaskTriggeredSuccessfully": "Tarea activada exitosamente"}, "DataValidation": {"DataValidationFailed": "Falló la validación de datos", "DuplicateEntryFound": "Entrada duplicada encontrada", "InvalidDataFormat": "Formato de datos inválido", "InvalidDateFormat": "Formato de fecha inválido", "InvalidNumberFormat": "Formato de número inválido", "RequiredDataMissing": "<PERSON><PERSON><PERSON> datos requeridos", "ValueOutOfRange": "El valor está fuera de rango"}, "ErrorBoundary": {"ErrorCaughtByErrorBoundary": "Error capturado por ErrorBoundary", "ErrorDetails": "Detalles del error", "PageCrashed": "Esta página se ha bloqueado", "RefreshPage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "ReportIssue": "Reportar problema", "SomethingWentWrong": "Algo salió mal", "UnexpectedError": "Ocurrió un error inesperado"}, "FileUpload": {"FailedToUploadFile": "Falló la carga del archivo", "FileSizeTooLarge": "El tamaño del archivo es muy grande", "FileUploadFailed": "Falló la carga del archivo", "FileUploadedSuccessfully": "Archivo cargado exitosamente", "InvalidFileType": "Tipo de archivo inválido", "MaxFileSizeExceeded": "Tamaño máximo de archivo excedido", "PleaseSelectFile": "Por favor, seleccione un archivo", "UploadInProgress": "Carga en progreso...", "UploadingFile": "Cargando archivo...", "FailedToDownloadSampleFile": "Error al descargar archivo de muestra", "FailedToImportFile": "Error al importar archivo", "ErrorDeletingFile": "Error al eliminar archivo", "PleaseSelectAtLeastOneDocument": "Por favor, selecciona al menos un documento para subir", "FailedToUploadDocuments": "Error al subir documentos"}, "FormValidation": {"AddressRequired": "La dirección es requerida", "ChooseCategory": "Por favor, elija al menos una categoría", "QuestionScope": "Por favor, seleccione el alcance de la pregunta", "AdminNameRequired": "El nombre del administrador es requerido", "AuthenticationTypeRequired": "El tipo de autenticación es requerido cuando la verificación está habilitada", "BusinessUnitRequired": "Este campo es requerido", "CountryRequired": "El país es requerido", "CustomerNameRequired": "El nombre del cliente es requerido", "ExpiryIsRequired": "¡La expiración es requerida!", "FormNameRequired": "Este campo es requerido", "FrequencyValueRequired": "¡El valor de frecuencia es requerido cuando la frecuencia está habilitada!", "IndustryRequired": "La industria es requerida", "InvalidAdminEmail": "Dirección de correo electrónico del administrador inválida", "InvalidEmailAddress": "Dirección de correo electrónico inválida", "InvalidPhoneNumber": "Número de teléfono inválido", "MustAgreeToTerms": "Debe aceptar los términos y condiciones", "PhoneNumberIsRequired": "El número de teléfono es requerido", "PleaseEnterValidEmailAddress": "Por favor, ingrese una dirección de correo electrónico válida", "PleaseProvideEmailAndPassword": "Por favor, proporcione tanto el correo electrónico como la contraseña.", "PleaseProvidePassword": "Por favor, proporcione una contraseña.", "RegulationRequired": "Se debe seleccionar al menos una regulación", "ResourceRequired": "Se debe seleccionar al menos un recurso", "TenDigitsRequired": "Se requieren 10 dígitos", "ThisFieldIsRequired": "¡Este campo es requerido!", "UsernameMinLength": "El nombre de usuario debe tener al menos 2 caracteres.", "EnterName": "Ingrese Nombre", "EnterDescription": "Ingrese Descripción", "Error": "Error", "ErrorDot": "Error.", "PleaseAgreeToAllRequiredConsents": "Por favor, acepta todos los consentimientos requeridos.", "ErrorSubmittingForm": "Error al enviar el formulario", "PleaseProvideYourConsentFirst": "Por favor, proporciona tu consentimiento primero.", "PleaseInputYourEmail": "Por favor, ingresa tu correo electrónico.", "PleaseEnterValidEmail": "Por favor, ingresa una dirección de correo electrónico válida.", "PleaseEnterValidPhoneNumber": "Por favor, ingresa un número de teléfono válido de 10 dígitos.", "InvalidInputTypeSpecified": "Tipo de entrada inválido especificado.", "PleaseEnterValidOTP": "Por favor, ingresa un OTP válido de 6 dígitos", "FailedToChangePreferences": "Error al cambiar las preferencias", "InvalidOTP": "OTP inválido", "PreferenceDataNotAvailable": "Datos de preferencia no disponibles.", "PleaseSaveChanges": "¡Por favor, guarda los cambios!", "PleaseSelectAnyOneService": "¡Por favor, selecciona cualquier servicio!", "PleaseSelectAnyOneMaturityLevel": "¡Por favor, selecciona cualquier nivel de madurez!", "PleaseSelectAnyOneQuestionnaire": "¡Por favor, selecciona cualquier cuestionario!"}, "HttpClient": {"ForbiddenError": "Error 403 Prohibido", "NoAccessTokenAvailable": "No hay token de acceso disponible para la solicitud", "RefreshTokenExpired": "Token de actualización expirado", "RequestFailed": "Falló la solicitud", "ResponseError": "Error de respuesta", "TokenRefreshFailed": "Falló la actualización del token"}, "Onboarding": {"OnboardingFailed": "Falló la incorporación", "OnboardingSuccessful": "Incorporación completada exitosamente", "ProcessCompletedSuccessfully": "¡Proceso completado exitosamente!", "SendingOnboardingDataFailed": "¡Falló el envío de datos de incorporación!"}, "PrivacyOps": {"FailedToAddRegulation": "Falló la adición de la regulación", "FailedToCreateRisk": "Falló la creación del riesgo", "FailedToDeleteRisk": "Falló la eliminación del riesgo", "FailedToUpdateRisk": "Falló la actualización del riesgo", "RegulationAddedSuccessfully": "Regulación agregada exitosamente", "RiskCreatedSuccessfully": "R<PERSON>go creado exitosamente", "RiskDeletedSuccessfully": "Riesgo eliminado exitosamente", "RiskUpdatedSuccessfully": "Riesgo actualizado exitosamente"}, "Profile": {"FailedToSaveChanges": "Falló el guardado de cambios.", "PasswordUpdateFailed": "¡Falló la actualización de la contraseña!"}, "ROPA": {"PleaseAnswerAllQuestions": "Por favor, responda todas las preguntas antes de enviar.", "ProgressBarError": "¡Error de barra de progreso!", "RopaSubmittedSuccessfully": "ROPA ha sido enviado exitosamente", "TentativeDateUpdatedSuccessfully": "Fecha tentativa actualizada exitosamente", "RecurrenceDateUpdatedSuccessfully": "Fecha de recurrencia actualizada exitosamente"}, "Unauthorized": {"AccessDenied": "Acceso denegado", "ContactAdministrator": "Por favor, contacte a su administrador", "InsufficientPermissions": "No tiene permisos suficientes", "LoginRequired": "Se requiere inicio de sesión para acceder a esta página", "RoadBlockAhead": "Bloqueo de carretera adelante", "Whoops": "¡Ups!"}, "VRM": {"AccurateInformationRequired": "Se requiere información precisa.", "AssessmentReviewedSuccessfully": "¡Evaluación revisada exitosamente!", "AssessmentSubmittedSuccessfully": "¡Evaluación enviada exitosamente!", "CommentRequired": "El comentario es requerido.", "CouldntSaveReview": "No se pudo guardar la revisión", "PleaseAnswerAllQuestions": "¡Por favor, responda todas las preguntas!", "PleaseReviewAllQuestions": "¡Por favor, revise todas las preguntas!", "ReviewSavedSuccessfully": "Revisión guardada exitosamente", "RiskScoreRequired": "Se requiere puntuación de riesgo.", "ProcessingIn": "Procesando...", "CouldntStartAssessment": "No se pudo iniciar la evaluación", "CouldntAddVendor": "No se pudo agregar el proveedor", "CouldntUpdateVendor": "No se pudo actualizar el proveedor", "VendorAddedSuccessfully": "<PERSON>ve<PERSON><PERSON> ag<PERSON>gado exitosamente", "VendorUpdatedSuccessfully": "Proveedor actualizado exitosamente", "PlanSavedSuccessfully": "Plan guardado exitosamente", "CouldntSaveMitigationPlan": "No se pudo guardar el plan de mitigación.", "AnErrorOccurred": "Ocurrió un error", "DeletedSuccessfully": "Eliminado exitosamente", "UploadedSuccessfully": "Subido exitosamente", "DownloadLinkSent": "El enlace de descarga ha sido enviado a su dirección de correo electrónico. Por favor, revise su bandeja de entrada", "Success": "Éxito", "NewVendor": "+ <PERSON> proveedor", "UserAssignedSuccessfully": "¡Usuario asignado exitosamente!", "AssigningUserFailed": "¡Falló la asignación del usuario!", "AnErrorOccurredCouldNotUpdate": "Ocurrió un error, no se pudo actualizar", "MitigationPlanSubmittedSuccessfully": "Plan de mitigación enviado exitosamente.", "QuestionUpdatedSuccessfully": "Pregunta actualizada exitosamente.", "QuestionDeletedSuccessfully": "La pregunta ha sido eliminada exitosamente", "Error": "Error", "ErrorExclamation": "¡Error!", "UploadValidFile": "Subir archivo válido", "ControlAndDescriptionRequired": "Se requieren control y descripción.", "UploadFilesOrEnterURL": "Subir archivo(s) o ingresar URL", "EnterValidURL": "Ingresar URL válida", "FailedToOpenWindow": "Falló al abrir la ventana. Puede haber sido bloqueada por el navegador.", "EnterTemplateName": "Ingresar nombre de plantilla", "PleaseAttachDocumentOrURL": "¡Por favor, adjunta documento o URL!"}, "VendorManagement": {"CouldntAddVendor": "No se pudo agregar el proveedor", "FailedToDeleteVendor": "Falló la eliminación del proveedor", "FailedToUpdateVendor": "Falló la actualización del proveedor", "VendorAddedSuccessfully": "<PERSON>ve<PERSON><PERSON> ag<PERSON>gado exitosamente", "VendorDeletedSuccessfully": "Proveedor eliminado exitosamente", "VendorUpdatedSuccessfully": "Proveedor actualizado exitosamente"}}, "ucm": {"PII Label": "Etiqueta PII", "Consent Purpose": "Finalidad del Consentimiento", "Processing Purpose": "Finalidad del Tratamiento", "Processing Category": "Categoría de Tratamiento", "Dashboard": {"ConsentStatus": "Estado del Consentimiento", "PurposeConsentStatus": "Estado del Consentimiento por Propósito", "ConsentByResidency": "Consentimiento por Residencia", "TotalUsers": "Usuarios Totales", "TotalGrantedConsent": "Consent<PERSON><PERSON><PERSON>", "TotalDeclinedConsent": "Consentimientos <PERSON>", "WithdrawnConsent": "Consentimient<PERSON>"}, "PrivacyNotice": {"PrivacyNoticeName": "Nombre del Aviso de Privacidad", "Version": "Versión", "UpdatedOn": "Actualizado el", "Action": "Acción", "Create": "<PERSON><PERSON><PERSON>", "SaveDetails": "Guardar Detalles", "EnterPrivacyNoticeTitle": "Ingrese el Título del Aviso de Privacidad", "AddSection": "<PERSON>g<PERSON><PERSON>", "NoSectionsAvailable": "No hay secciones disponibles", "AddContent": "Agregar <PERSON>", "SelectToViewContent": "Seleccione una sección para ver su contenido", "NewContent": "Nuevo Contenido", "NewSectionTitle": "Título de Nueva Sección", "Sections": "Secciones"}, "ProcessingCategory": {"ProcessingCategoryName": "Nombre de la Categoría de Procesamiento", "Description": "Descripción", "Action": "Acción", "UpdateCategory": "Actualizar Categoría", "Name": "Nombre", "AddCategory": "Agregar Categoría", "ProcessingCategory": "Categoría de Procesamiento", "AddProcessingCategory": "Agregar Categoría de Procesamiento", "ProcessingPurpose": "Propósito de Procesamiento", "DeleteCategory": "Eliminar Categoría de Procesamiento", "SureToDelete": "¿Está seguro de que desea eliminar esta categoría de procesamiento?", "No": "No", "YesProceed": "<PERSON><PERSON>, Proceder", "EnterCategoryName": "Ingrese el Nombre de la Categoría", "EnterDescription": "Ingrese la Descripción de la Categoría"}, "ProcessingPurpose": {"AddProcessingPurpose": "Agregar Propósito de Procesamiento", "ProcessingPurpose": "Propósito de Procesamiento", "Description": "Descripción", "ProcessingCategory": "Categoría de Procesamiento", "Action": "Acción", "UpdateProcessingPurpose": "Actualizar Propósito de Procesamiento", "Name": "Nombre", "DeleteProcessingPurpose": "Eliminar Propósito de Procesamiento", "SureToDelete": "¿Está seguro de que desea eliminar este propósito de procesamiento?", "EnterPurposeName": "Ingrese el Nombre del Propósito", "EnterDescription": "Ingrese la Descripción del Propósito", "EnterCategoryName": "Ingrese el Nombre de la Categoría", "EnterCategoryDescription": "Ingrese la Descripción de la Categoría"}, "ConsentPurpose": {"AddConsentPurpose": "Agregar Propósito de Consentimiento", "ConsentPurpose": "Propósito de Consentimiento", "EnterConsentName": "Ingrese el Nombre del Consentimiento", "EnterDescription": "Ingrese la Descripción del Consentimiento", "Description": "Descripción", "ProcessingPurpose": "Propósito de Procesamiento", "Action": "Acción", "UpdateConsentPurpose": "Actualizar Propósito de Consentimiento", "Name": "Nombre", "EnterCategoryDescription": "Ingrese la Descripción de la Categoría", "EnterCategoryName": "Ingrese el Nombre de la Categoría", "DeleteConsent": "Eliminar Propósito de Consentimiento", "SureToDelete": "¿Está seguro de que desea eliminar este propósito de consentimiento?", "RetentionType": "Tipo de retención", "RetentionPeriod": "Período de retención", "ExpiryPeriod": "Período de expiración"}, "PII": {"AddPII": "Agregar PII", "PIILabel": "Etiqueta PII", "EnterPIIName": "Ingrese el Nombre de la Etiqueta PII", "EnterDescription": "Ingrese la Descripción de la Etiqueta PII", "Description": "Descripción", "UniversalIdentifier": "Identificador Universal", "Action": "Acción", "DeletePIILabel": "Eliminar Etiqueta PII", "SureToDelete": "¿Está seguro de que desea eliminar esta etiqueta PII?", "UpdatePIILabel": "Actualizar Etiqueta PII", "Name": "Nombre", "EnterPIILabelName": "Ingrese el Nombre de la Etiqueta PII", "EnterPIIDescription": "Ingrese la descripción de la etiqueta PII", "Yes": "Sí", "No": "No"}, "CollectionBuilder": {"TemplateName": "Nombre de la Plantilla", "DataIdentifierType": "Tipo de Identificador de Datos", "EntityName": "Nombre de la Entidad", "Owner": "Propietario", "CreatedDate": "Fecha de Creación", "ModifiedDate": "Fecha de Modificación", "FormURL": "URL del Formulario de Consentimiento", "CenterURL": "URL del Centro de Preferencias", "Status": "Estado", "Action": "Acción", "CreateTemplate": "Crear Plantilla de Recopilación de Consentimiento"}, "PreferenceCenter": {"AddPreferenceCenter": "Agregar Centro de Preferencias", "PreferenceCenterName": "Nombre del Centro de Preferencias", "DateandTime": "Fecha y Hora del Centro de Preferencias", "URL": "URL", "Owner": "Propietario", "Status": "Estado", "Actions": "Acciones", "BasicInfo": "Información Básica", "Customize": "Personalizar", "Code": "Código", "TemplateName": "Nombre de la Plantilla", "Description": "Descripción", "SubjectIdentityType": "Tipo de Identidad del Sujeto", "Template": "Plantilla", "OwnerEmail": "Correo del Propietario", "OwnerName": "Nombre del Propietario", "PrivacyPolicyLink": "Enlace a la Política de Privacidad"}, "SubjectConsentManager": {"Templates": "Plantillas", "Lists": "Listas", "ConsentDistributionChartbyTemplate": "Distribución de Consentimientos por Nombre de Plantilla de Recopilación", "ConsentSourceDistribution": "Distribución de Fuentes de Consentimiento", "CollectionTemplateName": "Nombre de la Plantilla de Recopilación", "TotalUserConsents": "Total de Consentimientos de Usuarios", "Status": "Estado", "Action": "Acción", "WebForm": "Formulario Web", "Form": "Formulario", "WebPreferenceCenter": "Centro de Preferencias Web", "Manually": "Manual", "PrefereceCenter": "Centro de Preferencias", "API": "API", "SubjectIdentity": "Identidad del Sujeto", "Source": "Fuente", "GeoLocation": "Geolocalización", "ConsentedAt": "Consentido En", "PIILabelName": "Nombre de la Etiqueta PII", "TotalConsents": "Total de Consentimientos", "WebConsents": "Consentimientos Web", "MobileConsents": "Consentimientos Móviles", "ApiConsents": "Consentimientos API", "ConsentStatusTypes": "Tipos de Estado de Consentimiento", "Granted": "Concedido", "Declined": "<PERSON><PERSON><PERSON><PERSON>", "Withdrawn": "<PERSON><PERSON><PERSON>", "ConsentDistributionByProcessing": "Distribución de Consentimientos por Propósito de Procesamiento", "SubjectPreferenceList": "Lista de Preferencias del Sujeto", "UserLogs": "Registros de Usuario", "ConsentName": "Nombre del Consentimiento", "Frequency": "Frecuencia", "ConsentExpiration": "Expiración del Consentimiento", "ConsentStatus": "Estado del Consentimiento", "Filter": "Filtrar", "UserDetails": "Detalles del Usuario", "ConsentFlowVisualization": "Visualización del Flujo de Consentimiento", "NoDataAvailable": "No hay datos disponibles", "LoadingData": "Cargando datos...", "ListOfTemplateTable": "Lista de la Tabla de Plantillas"}}, "DPO": {"Dashboard": {"Risk": {"EntityRisks": "Riesgos de la Entidad", "DataMappingRisks": "Riesgos del Mapeo de Datos", "VendorRisks": "Riesgos del Proveedor", "AssessmentRisks": "Riesgos de Evaluación", "RiskByCategory": "Riesgo por Categoría", "RiskByStages": "Riesgo por Etapas", "MonetaryRisk": "Riesgo Monetario", "RiskByImpactProbability": "Riesgo por Impacto y Probabilidad", "RegulatoryComplianceBreakdown": "Desglose del Cumplimiento Normativo", "ControlByFramework": "Control por Marco", "RecentRisks": "Riesgos Recientes", "RiskID": "ID de Riesgo", "RiskTitle": "Título del Riesgo", "RiskCategory": "Categoría de Riesgo", "SourceOfRisk": "Fuente del Riesgo", "DateIdentified": "Fecha de Identificación", "RiskDescription": "Descripción del Riesgo", "Identification": "Identificación", "Evaluation": "Evaluación", "Mitigation": "Mitigación", "Closure": "Cierre", "Compliance": "Cumplimiento", "DataBreach": "Violación de Datos", "Legal": "Legal", "Likelihood": "Probabilidad", "Severity": "Gravedad", "GDPR": "RGPD", "CCPA": "CCPA", "NIST": "NIST", "DPDPA": "DPDPA", "UAEPDPL": "PDPL EAU", "UAE": "EAU", "DPD": "LPD", "EUG": "UEG", "Controls": "Controles", "Completed": "Completado", "Pending": "Pendiente", "Score": "<PERSON><PERSON><PERSON>"}, "Compliance": {"ComplianceScoreByCategories": "Puntaje de Cumplimiento por Categorías", "OverAllScore": "Puntaje General", "YourScore": "Tu Puntaje de Cumplimiento", "DataProtection": "Protección de Datos", "OrganizationControls": "Controles Organizacionales", "TechnologyControls": "Controles Tecnológicos", "SystemSecurity": "Seguridad del Sistema", "RegulatoryReporting": "Informe Regulatorio", "Policies": "Políticas", "KeyImprovementActions": "Acciones Clave de Mejora", "ImprovementAction": "Acciones de Mejora", "Impact": "Impacto", "Status": "Estado", "Group": "Grupo", "ActionType": "Tipo de Acción", "Completed": "Completado", "NotCompleted": "No Completado", "OutOfScope": "Fuera de Alcance", "ViewImprovementActions": "Ver Acciones de Mejora", "ViewAll": "<PERSON><PERSON>", "PointAchieved": "Punto Logrado", "ComplianceScore": "El puntaje de cumplimiento mide tu progreso hacia la finalización de las acciones recomendadas que ayudan a reducir los riesgos relacionados con la protección de datos y los estándares regulatorios.", "DataProtectionDescription": "Habilita y configura el cifrado, controla el acceso a la información y evita la fuga o extracción de datos.", "OrganizationControlsDescription": "Define políticas de seguridad, responsabilidades y marcos, asegurando que los activos de información estén protegidos.", "TechnologyControlsDescription": "Emplea defensas automatizadas, endurecimiento del sistema y medidas de integridad de datos para aplicar las políticas de seguridad.", "SystemSecurityDescription": "Evalúa las medidas de seguridad implementadas en los sistemas informáticos.", "RegulatoryReportingDescription": "Garantiza el cumplimiento de las regulaciones de protección de datos y los requisitos de reporte.", "PoliciesDescription": "Evalúa la adecuación y actualización de las políticas de privacidad."}}, "ControlHandbook": {"AddCategory": "Agregar Categoría", "ControlCategory": "Categoría de Control", "AddRegulation": "Agregar Regulación", "CreatedOn": "Creado el", "UpdatedOn": "Actualizado el", "Actions": "Acciones", "ControlNo": "Número de Control", "ControlDescription": "Descripción del Control", "SummaryOfInScopeRegulations": "Resumen de Regulaciones en Alcance", "LoadingRegulations": "Cargando regulaciones", "data_submitted_successfully": "Datos enviados con éxito", "please_fill_all_fields": "Por favor, complete todos los campos", "please_add_business_requirement": "Por favor, agregue el requisito empresarial", "category_name_required": "El nombre de la categoría es obligatorio", "control_number_required": "El número de control es obligatorio", "category_number_required": "El número de categoría es obligatorio", "control_description_required": "La descripción del control es obligatoria", "AddControlCategory": {"CategoryName": "Nombre de la Categoría", "ControlDescription": "Descripción del Control", "ControlNo": "Número de Control", "CategoryNo": "Número de Categoría", "EnterCategoryName": "Ingrese el Nombre de la Categoría", "EnterControlDescription": "Ingrese la Descripción del Control", "EnterControlNo": "Ingrese el Número de Control", "EnterCategoryNo": "Ingrese el Número de Categoría", "Reference": "Referencia", "EnterReference": "Ingrese el ID de Referencia", "BusinessRequirement": "Requisito Empresarial", "EnterBusinessRequirement": "Ingrese los Requisitos Empresariales", "EnterCommaSeparatedValues": "Ingrese valores separados por comas", "SaveBusinessRequirement": "Guardar Requisito Empresarial"}, "AddRegulations": {"AddRegulation": "Agregar nueva regulación", "FillInDetailsToAddRegulation": "Complete los detalles para agregar una nueva regulación.", "Geography": "Geografía", "MappingColumnHeader": "Encabezado de columna de mapeo", "Source": "Fuente", "AuthoritativeSource": "Fuente autorizada", "Version": "Versión", "URL": "URL", "Available": "Disponible", "GroupIDs": "IDs de grupo", "ISRegulationAvailable": "¿Está disponible actualmente esta regulación?", "SelectAssignee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RegulationAddedSuccessfully": "Regulación agregada con éxito", "FailedToAddRegulation": "No se pudo agregar la regulación"}}, "Regulation": {"RegulationName": "Regulación", "CreatedOn": "Creado el", "UpdatedOn": "Actualizado el", "ComplianceStatus": "Estado de cumplimiento", "ViewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "SelectEntity": "Seleccionar entidad", "importSign": "importar signo", "RepositoryDocumentUpload": "Carga de documentos del repositorio de Privacidad-Ops", "AddControlDialog": {"AddControl": "Agregar control", "ControlNumber": "Número de control", "ControlDescription": "Descripción del control", "ArticleNumber": "Número de artículo", "RegulationSummary": "Resumen de la regulación"}, "RegulationDetails": {"ComplianceStatus": "Estado de cumplimiento", "PartialComplianceStatus": "Estado de cumplimiento parcial", "NonComplianceStatus": "Estado de no cumplimiento"}, "NavHeadings": {"Controls": "Controles", "Documents": "Documentos", "Duties": "<PERSON><PERSON><PERSON>", "Actions": "Acciones", "Improvements": "<PERSON><PERSON><PERSON>"}, "EditControl": {"EditControl": "Editar control", "Applicable": "Aplicable", "Document": "Documento", "SelectDocument": "Seleccionar documento", "NotApplicable": "No aplicable", "ComplianceStatus": "Estado de cumplimiento", "Observation": "Observación", "AddObservation": "Agregar observación", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Compliant": "<PERSON><PERSON>le", "PartiallyCompliant": "Parcialmente cumple", "NonCompliant": "No cumple", "AddYourObservationHere": "Agregar su observación aquí..."}, "Controls": {"ControlNumber": "Número de control", "ControlDescription": "Descripción del control", "RegulationSummary": "Resumen de las regulaciones aplicables", "NoDataAvailable": "No hay datos disponibles"}, "ExtendedControlTabel": {"ReferenceId": "ID de referencia", "BusinessRequirement": "Requisito empresarial", "ArticleNumber": "Número de artículo", "Applicability": "Aplicabilidad", "Document": "Documento", "ComplianceStatus": "Estado de cumplimiento", "Observation": "Observación", "Action": "Acción", "AddToAction": "Agregar a acción", "AddToDuty": "Agregar a deber", "AddToImprovement": "Agregar a mejora", "Action Added Successfully": "Acción agregada exitosamente"}, "Documents": {"Document": "Documento", "Description": "Descripción", "Category": "Categoría", "CreatedOn": "Creado el", "CreatedBy": "<PERSON><PERSON>o por", "Attachment": "Adjunto"}, "Duties": {"Overdue": "V<PERSON>cid<PERSON>", "NoOverdueData": "No hay datos vencidos", "Open": "<PERSON>bie<PERSON>o", "NoOpenData": "No hay datos abiertos"}, "Actions": {"ActionTitle": "Título de la acción", "AssignedTo": "Asignado a", "AssignedBy": "<PERSON><PERSON><PERSON> por", "AssignedDate": "Fecha de asignación", "Deadline": "<PERSON><PERSON> lí<PERSON>", "Status": "Estado", "NoResult": "No hay resultados"}, "Improvements": {"Overdue": "V<PERSON>cid<PERSON>", "NoOverdueDuties": "No hay deberes vencidos", "NoCompletedDuties": "No hay deberes completados", "Open": "<PERSON>bie<PERSON>o"}}, "RiskRegister": {"NavHeadings": {"Activity": "Actividad", "Audit Log": "Registro de auditoría", "RiskEvaluationUpdatedSuccessfully": "Evaluación de riesgo actualizada exitosamente"}, "Activity": {"RiskTitle": "Tí<PERSON>lo del riesgo", "RiskCategory": "Categoría de riesgo", "SourceOfRisk": "Fuente del riesgo", "DateIdentified": "Fecha de identificación", "RiskDescription": "Descripción del riesgo", "Regulation": "Regulación", "Module": "<PERSON><PERSON><PERSON><PERSON>", "Entity": "Entidad", "CreateRisk": "<PERSON><PERSON><PERSON> riesgo"}, "RiskForm": {"RiskTitle": "Tí<PERSON>lo del riesgo", "Write": "Escribir", "DescriptionOfRisk": "Descripción del riesgo", "EnterDescription": "Ingrese una descripción...", "Module": "<PERSON><PERSON><PERSON><PERSON>", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RiskCategory": "Categoría de riesgo", "TentativeDate": "<PERSON><PERSON>", "PickADate": "<PERSON><PERSON> una fecha", "SourceOfRisk": "Fuente del riesgo", "Threat": "<PERSON><PERSON><PERSON>", "Vulnerability": "Vulnerabilidad", "Regulation": "Regulación", "Entity": "Entidad", "RiskCreatedSuccessfully": "R<PERSON>go creado exitosamente"}, "Steeper": {"SteeperHeadings": {"Identified": "Identificado", "Evaluation": "Evaluación", "Mitigation": "Mitigación", "Closure": "Cierre", "Monitoring": "Monitoreo"}, "EvaluationStep": {"Evaluation": "Evaluación", "Category": "Categoría", "Compliance": "Cumplimiento", "DateClosed": "<PERSON><PERSON>", "PickADate": "Seleccionar una fecha", "DateCreated": "Fecha de Creación", "Deadline": "Fecha <PERSON>", "Description": "Descripción", "RiskId": "ID de Riesgo", "InherentRiskLevel": "<PERSON><PERSON> de Riesgo Inherente", "ResidualRiskLevel": "Nivel de Riesgo Residual", "Reminder": "Recordatorio", "Organization": "Organización", "Result": "<PERSON><PERSON><PERSON><PERSON>", "RiskApprover": "Aprobador del Riesgo", "RiskName": "Nombre del Riesgo", "RiskOwner": "Propietario del Riesgo", "RiskTemplate": "Plantilla de Riesgo", "Source": "Fuente", "TargetRiskLevel": "Nivel de Riesgo Objetivo", "Treatment": "Tratamiento", "TreatmentPlan": "Plan de Tratamiento", "TreatmentStatus": "Estado del Tratamiento", "Threat": "<PERSON><PERSON><PERSON>", "Type": "Tipo", "Vulnerability": "Vulnerabilidad"}, "MitigationStep": {"Strategy": "Estrategia", "Write": "Escribir", "YourProgress": "Tu progreso", "AddAction": "Agregar acción", "ActionTitle": "Título de la acción", "AssignedTo": "Asignado a", "AssignedBy": "<PERSON><PERSON><PERSON> por", "AssignedDate": "Fecha de asignación", "Deadline": "<PERSON><PERSON> lí<PERSON>", "Status": "Estado", "ActionAddedSuccessfully": "Acción agregada exitosamente", "ProceedToNextStep": "Proceder al siguiente paso", "ConfirmationText": "¿Estás satisfecho con la migración", "AddActionForm": {"AddAction": "Agregar acción", "Category": "Categoría", "Entity": "Entidad", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Description": "Descripción", "AssignedBy": "<PERSON><PERSON><PERSON> por", "AssignedTo": "Asignado a", "AssignDate": "Fecha de asignación", "Deadline": "<PERSON><PERSON> lí<PERSON>", "PickADate": "Elegir una fecha"}}, "ClosureStep": {"ClosureReview": "Revisión de cierre", "Write": "Escribir", "AssociateDuty": "Asociar deber", "Duty": "<PERSON><PERSON>", "AssociatePolicyProcedure": "Asociar política y procedimiento", "PolicyProcedure": "Política y procedimiento", "AssociateImprovement": "<PERSON><PERSON><PERSON> mejora", "Improvement": "<PERSON><PERSON><PERSON>"}, "MonitoringStep": {"Title": "<PERSON><PERSON><PERSON><PERSON>", "Comment": "Comentario", "Standard": "<PERSON><PERSON><PERSON><PERSON>", "CreatedDate": "Fecha de creación", "DueDate": "<PERSON><PERSON>nc<PERSON>o", "Status": "Estado"}}, "RiskEvaluationUpdatedSuccessfully": "Risk evaluation updated successfully"}, "Activities": {"Duty": {"TOTAL": "TOTAL", "OPEN": "ABIERTO", "ARCHIVE": "ARCHIVO", "NoArchiveData": "Sin datos de archivo", "Archieved": "Archivado", "Active": "Activo", "Open": "<PERSON>bie<PERSON>o", "Overdue": "V<PERSON>cid<PERSON>", "NoOpenData": "Sin datos abiertos", "AddNew": "AÑADIR NUEVO", "DutyDetails": "Detalles del deber", "Fulfilment": "Cumplimiento", "AuditLog": "Registro de auditoría", "Description": "Descripción", "Name": "Nombre", "Date": "<PERSON><PERSON>", "Time": "<PERSON><PERSON>", "AddDutyForm": {"AddDuty": "Agregar deber", "AddTag": "Agregar etiqueta", "DutyTitle": "<PERSON><PERSON><PERSON><PERSON>ber", "AddAssignee": "Agregar responsable", "SelectAssignee": "Seleccionar responsable", "DueDate": "<PERSON><PERSON>nc<PERSON>o", "PickADate": "Elegir una fecha", "Entity": "Entidad", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Standards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EnterYourStandardsHere": "Introduce tus estándares aquí...", "Comment": "Comentario", "EnterYourCommentsHere": "Introduce tus comentarios aquí...", "Attachment": "Adjunto", "AddDocument": "Agregar documento", "SelectedFiles": "Archivos seleccionados:"}, "DutyDetailForm": {"DutyTitle": "<PERSON><PERSON><PERSON><PERSON>ber", "Dummy": "dummy", "AssignedTo": "Asignado a", "SelectAssignee": "Seleccionar responsable", "Status": "Estado", "Open": "ABIERTO", "Completed": "COMPLETADO", "StartDate": "Fecha de inicio", "Owner": "Propietario", "DueDate": "<PERSON><PERSON>nc<PERSON>o", "DueDateValue": "7 de mayo de 2025", "Frequency": "Frecuencia", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Standards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Criteria": "Criterios", "DefinitionOfEvidence": "Definición de evidencia", "Comments": "Comentarios", "Attachment": "Adjunto", "AddDocument": "Agregar documento", "SelectedFiles": "Archivos seleccionados:", "Annually": "Anualmente", "BiAnnual": "Bianual", "TriMonthly": "Trimestral", "Monthly": "<PERSON><PERSON><PERSON>"}, "DutyFulfilmentForm": {"DutyInterval": "Intervalo de deber", "PickDate": "Elige una fecha", "Assigned": "<PERSON><PERSON><PERSON>", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FulfilledBy": "Cumplido por", "Write": "Escribir", "Status": "Estado", "FulfilmentProof": "Prueba de cumplimiento", "VerificationQuestion": "¿Se ha verificado la efectividad del deber?", "Yes": "Sí", "No": "No", "Attachments": "Archivos adjuntos", "Vector": "vector", "AddDocuments": "Agregar documentos", "URLLink": "Enlace URL", "LandingPageForm": "Formulario de página de aterrizaje", "WebsiteSignup": "Registro en el sitio web", "SocialMedia": "Redes sociales", "Comment": "Comentario", "EnterURL": "Introducir URL", "Finalized": "Finalizado"}}, "Action": {"AddActionForm": {"AddAction": "Agregar acción", "Category": "Categoría", "Entity": "Entidad", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Description": "Descripción", "AssignedBy": "<PERSON><PERSON><PERSON> por", "AssignedTo": "Asignado a", "AssignDate": "Fecha de asignación", "PickDate": "Elige una fecha", "Deadline": "<PERSON><PERSON> lí<PERSON>"}, "ActionActivity": {"TotalActionItems": "Total de elementos de acción", "OpenActionItems": "Elementos de acción abiertos", "ClosedActionItems": "Elementos de acción cerrados", "AddNew": "AGREGAR NUEVO", "ActionAddedSuccessfully": "Acción agregada exitosamente", "ActionTitle": "Título de la Acción", "AssignedTo": "Asignado a", "AssignedBy": "<PERSON><PERSON><PERSON> por", "AssignedDate": "Fecha de asignación", "Deadline": "<PERSON><PERSON> lí<PERSON>", "Status": "Estado"}}, "Improvement": {"TOTAL": "TOTAL", "INPROGRESS": "EN PROCESO", "ARCHIVE": "ARCHIVO", "Open": "<PERSON>bie<PERSON>o", "Completed": "Completado", "Overdue": "<PERSON><PERSON><PERSON>", "ImprovementDetails": "Detalles de la Mejora", "Evaluation": "Evaluación", "AuditLog": "Registro de auditoría", "NoCompletedData": "Sin datos completados", "ImprovementDetailsForm": {"DutyTitle": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> Tarea", "DummyValue": "pkjw", "AssignedTo": "Asignado a", "SelectAssignee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Status": "En Progreso", "Completed": "Completado", "DueDate": "<PERSON><PERSON> Vencimiento", "DueDateValue": "10 de marzo de 2025", "Owner": "Propietario", "Regulation": "Regulación", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Finding": "Otras áreas afectadas por el mismo hallazgo", "RootCause": "<PERSON><PERSON><PERSON>", "TreatmentPlan": "Plan de Tratamiento", "Attachment": "Adjunto", "AddDocument": "Agregar Documento", "TechTeamInvolved": "¿Está involucrado un equipo técnico en la implementación?", "Yes": "Sí", "No": "No", "SelectedFiles": "Archivos Seleccionados:", "AddProgress": "Agrega tu progreso"}, "AddNewImprovementForm": {"AddImprovement": "<PERSON><PERSON><PERSON><PERSON>", "AddTag": "<PERSON><PERSON><PERSON><PERSON>", "ImprovementActionTitle": "Título de la Acción de Mejora", "Title": "<PERSON><PERSON><PERSON><PERSON>", "AddAssignee": "<PERSON><PERSON><PERSON><PERSON>", "SelectAssignee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DueDate": "<PERSON><PERSON> Vencimiento", "PickDate": "Selecciona una fecha", "Regulation": "Regulación", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Entity": "Entidad", "Comment": "Comentario", "Comments": "Comentarios", "Attachment": "Adjunto", "AddDocument": "Agregar Documento", "SelectedFiles": "Archivos Seleccionados:"}, "EvaluationForm": {"ReviewedBy": "Revisado por", "ReviewedByPlaceholder": "Escribir", "EffectOnFinding": "¿Tiene un efecto sobre el hallazgo?", "EffectOnFindingPlaceholder": "Escribir", "OtherAreasAffected": "Otras áreas afectadas por el mismo hallazgo", "OtherAreasAffectedPlaceholder": "Escribir", "ManagementReviewTopic": "Tema de la revisión de gestión", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ManagementReviewText": "Texto para la revisión de gestión", "LandingPageForm": "Formulario de página de aterrizaje", "WebsiteSignup": "Registro en el sitio web", "SocialMedia": "Redes sociales", "Source": "Fuente"}}}, "Repository": {"DocumentRepository": {"Total": "Total", "Creation": "Creación", "In Progress": "En Progreso", "In Use": "En Uso", "Document": "Documento", "Description": "Descripción", "Category": "Categoría", "CreatedOn": "Creado el", "CreatedBy": "<PERSON><PERSON>o por", "Attachments": "<PERSON><PERSON><PERSON>"}, "AssesmentRepository": {"Total": "Total", "In Progress": "En Progreso", "Completed": "Completado", "Department": "Departamento", "AssessmentName": "Nombre de la Evaluación", "AssessmentType": "Tipo de Evaluación", "CreatedDate": "Fecha de Creación", "CreatedBy": "<PERSON><PERSON>o por", "Action": "Acción"}, "RecordOfProcessingActivitiesRepository": {"Total": "Total", "In Progress": "En Progreso", "Completed": "Completado", "Department": "Departamento", "Process": "Proceso", "CreatedDate": "Fecha de Creación", "CreatedBy": "<PERSON><PERSON>o por", "Version": "Versión", "Action": "Acción"}}}, "AuditLog": {"RequestStatusChangedToAcknowledgement": "El estado de la solicitud cambió a Reconocimiento", "RequestStatusChangedToDataGathering": "El estado de la solicitud cambió a Recolección de datos"}, "ToastMessages": {"Authentication": {"OTPVerifiedSuccessfully": "¡OTP verificado exitosamente!", "OTPSentSuccessfully": "OTP enviado exitosamente", "OTPResentSuccessfully": "OTP reenviado exitosamente", "VerificationCodeSentSuccessfully": "Código de verificación enviado exitosamente", "VerificationCodeResentSuccessfully": "Código de verificación reenviado exitosamente", "PasswordUpdatedSuccessfully": "Contraseña actualizada exitosamente", "CodeSentSuccessfully": "Código enviado exitosamente", "FailedToSendOTP": "Error al enviar OTP. Por favor, intenta de nuevo."}, "CookieConsent": {"CategorySavedSuccessfully": "Categoría guardada exitosamente", "ServiceSavedSuccessfully": "Servicio guardado exitosamente", "CookieSavedSuccessfully": "<PERSON>ie guardado exitosamente", "DomainDetailsUpdatedSuccessfully": "Los detalles del dominio se actualizaron correctamente.", "CookieCreatedSuccessfully": "<PERSON><PERSON> creada exitosamente"}, "Forms": {"FormSubmittedSuccessfully": "Formulario enviado exitosamente", "FormTranslatedSuccessfully": "Formulario traducido exitosamente", "DataSavedSuccessfully": "Datos guardados exitosamente", "DataUpdatedSuccessfully": "Datos actualizados exitosamente", "OperationSuccessful": "Operación exitosa", "ProcessCompletedSuccessfully": "Proceso completado exitosamente", "SubmittedSuccessfully": "Enviado exitosamente", "AllChangesSaved": "Todos los cambios guardados", "SavedSuccessfully": "Guardado exitosamente"}, "UCM": {"ProcessingPurposeUpdatedSuccessfully": "Propósito de procesamiento actualizado exitosamente"}, "Files": {"FileUploadedSuccessfully": "Archivo subido exitosamente", "FileImportedSuccessfully": "Archivo importado exitosamente", "FileProcessedSuccessfully": "Archivo procesado exitosamente", "DocumentUploadedSuccessfully": "Documento subido exitosamente", "DocumentDeletedSuccessfully": "Documento eliminado exitosamente", "DocumentsAddedSuccessfully": "Documentos agregados exitosamente", "DocumentsDeletedSuccessfully": "Documentos eliminados exitosamente", "UploadedSuccessfully": "Subido exitosamente", "TemplateDownloadedSuccessfully": "Plantilla descargada exitosamente"}, "Assessment": {"AssessmentCreatedSuccessfully": "Evaluación creada exitosamente", "AssessmentSubmittedSuccessfully": "Evaluación enviada exitosamente", "AssessmentReviewedSuccessfully": "Evaluación revisada exitosamente", "AnswerSavedSuccessfully": "Respuesta guardada exitosamente", "ReviewSavedSuccessfully": "Revisión guardada exitosamente", "PleaseAnswerAllQuestions": "¡Por favor, responde todas las preguntas!", "ProgressBarError": "¡Error de barra de progreso!", "ErrorInUpdatingTheQuestion": "Error al actualizar la pregunta", "FailedToCreateAssessment": "Error al crear la evaluación", "PleaseReviewAllQuestions": "¡Por favor, revisa todas las preguntas!", "FailedToLoadCategoryTypes": "Error al cargar tipos de categoría"}, "ROPA": {"ROPASubmittedSuccessfully": "ROPA enviado exitosamente", "ROPAStartedSuccessfully": "ROPA iniciado exitosamente", "ROPADataUpdatedSuccessfully": "Datos de ROPA actualizados exitosamente", "TentativeDateUpdatedSuccessfully": "Fecha tentativa actualizada exitosamente"}, "Users": {"UserAssignedSuccessfully": "Usuario asignado exit<PERSON>e", "UserAddedSuccessfully": "Usuario agregado exitosamente", "UserUpdatedSuccessfully": "Usuario actualizado exitosamente", "PasswordResetEmailSentSuccessfully": "Correo de restablecimiento de contraseña enviado exitosamente"}, "Vendors": {"VendorAddedSuccessfully": "<PERSON>ve<PERSON><PERSON> ag<PERSON>gado exitosamente", "VendorUpdatedSuccessfully": "Proveedor actualizado exitosamente"}, "Cookies": {"CookieUpdatedSuccessfully": "<PERSON>ie actual<PERSON> exitosamente", "TranslationSavedSuccessfully": "Traducción guardada exitosamente", "PolicyAddedSuccessfully": "Política agregada exitosamente", "PolicyUpdatedSuccessfully": "Política actualizada exitosamente"}, "Consent": {"ConsentPurposeUpdatedSuccessfully": "Propósito de consentimiento actualizado exitosamente", "ConsentPurposeDeletedSuccessfully": "Propósito de consentimiento eliminado exitosamente", "ProcessingPurposeAddedSuccessfully": "Propósito de procesamiento agregado exitosamente", "ProcessingPurposeUpdatedSuccessfully": "Propósito de procesamiento actualizado exitosamente", "ProcessingPurposeDeletedSuccessfully": "Propósito de procesamiento eliminado exitosamente"}, "Workflow": {"WorkflowCreatedSuccessfully": "Flujo de trabajo creado exitosamente", "WorkflowUpdatedSuccessfully": "Flujo de trabajo actualizado exitosamente", "WorkflowAddedSuccessfully": "Flujo de trabajo agregado exitosamente", "TaskAddedSuccessfully": "Tarea agregada exitosamente", "TaskUpdatedSuccessfully": "Tarea actualizada exitosamente", "TaskAutomationUpdatedSuccessfully": "Automatización de tarea actualizada exitosamente", "AutomationRemovedSuccessfully": "Automatización eliminada exitosamente", "StepAddedSuccessfully": "Paso agregado exitosamente", "WorkflowStepDeletedSuccessfully": "Paso de flujo de trabajo eliminado exitosamente", "WorkflowStepRenamedSuccessfully": "Paso de flujo de trabajo renombrado exitosamente"}, "PII": {"PIIAddedSuccessfully": "PII agregado exitosamente", "PIIEditedSuccessfully": "PII editado exitosamente", "PIIUpdatedSuccessfully": "PII actualizado exitosamente", "PIIDeletedSuccessfully": "PII eliminado exitosamente", "PIIsUpdatedSuccessfully": "PIIs actualizados exitosamente"}, "DataCatalogue": {"ServiceAddedSuccessfully": "<PERSON><PERSON><PERSON> a<PERSON> exit<PERSON>e", "TaskTriggeredSuccessfully": "Tarea activada exitosamente", "CredentialsSavedSuccessfully": "Credenciales guardadas exitosamente", "FailedToSaveCredentials": "Error al guardar credenciales. Por favor, verifica tu entrada e intenta de nuevo."}, "General": {"CopiedToClipboard": "¡Copiado al portapapeles!", "DashboardUpdatedSuccessfully": "Dashboard actualizado exitosamente", "DashboardDeletedSuccessfully": "Dashboard eliminado exitosamente", "URLCopiedToClipboard": "¡URL copiada al portapapeles!", "ScanStartedSuccessfully": "Escaneo iniciado exitosamente", "ErrorReportSentSuccessfully": "Reporte de error enviado exitosamente", "TicketCreatedSuccessfully": "Ticket creado exitosamente", "BusinessUnitUpdatedSuccessfully": "Unidad de negocio actualizada exitosamente", "DepartmentUpdatedSuccessfully": "Departamento actualizado exitosamente", "ProcessUpdatedSuccessfully": "Proceso actualizado exitosamente", "QuestionAddedSuccessfully": "Pregunta agregada exitosamente", "QuestionUpdatedSuccessfully": "Pregunta actualizada exitosamente", "QuestionDeletedSuccessfully": "Pregunta eliminada exitosamente", "TextFieldAdded": "Campo de texto agregado", "DeletedSuccessfully": "Eliminado exitosamente", "Success": "¡Éxito!", "RequestApprovedSuccessfully": "Solicitud aprobada exitosamente", "RequestRejectedSuccessfully": "Solicitud rechazada exitosamente", "RequestArchivedSuccessfully": "Solicitud archivada exitosamente", "RequestUnarchivedSuccessfully": "Solicitud desarchivada exitosamente", "RequestCompletedSuccessfully": "Solicitud completada exitosamente", "MailTemplateSentSuccessfully": "Plantilla de correo enviada exitosamente", "TemplateDeletedSuccessfully": "Plantilla eliminada exitosamente", "FiltersAppliedSuccessfully": "Filtros aplicados exitosamente", "RulesAppliedSuccessfully": "Reglas aplicadas exitosamente", "OptionRemovedSuccessfully": "Opción eliminada exitosamente", "LogoUploadedSuccessfully": "Logo subido exitosamente", "URLCopiedSuccessfully": "URL copiada exitosamente", "FormCreatedSuccessfully": "Formulario creado exitosamente", "QuestionOrderUpdatedSuccessfully": "Orden de preguntas actualizado exitosamente", "QuestionRemovedSuccessfully": "Pregunta eliminada exitosamente", "FormSavedSuccessfully": "Formulario guardado exitosamente", "FormVersionCreatedSuccessfully": "Versión del formulario creada exitosamente", "FailedToFetchControls": "Error al obtener controles", "RejectMessageCannotBeEmpty": "El mensaje de rechazo no puede estar vacío", "FailedToSendMailTemplate": "Error al enviar plantilla de correo", "DataSubjectEmailNotAvailable": "Email del sujeto de datos no disponible", "FailedToFetchReviewers": "Error al obtener revisores", "AutoScanRequired": "Se requiere escaneo automático", "FailedToUpdateCookie": "Error al actualizar cookie", "FailedToSaveRetentionRule": "Error al guardar regla de retención", "UploadFilesOrEnterURL": "Subir archivo(s) o ingresar URL", "EnterValidURL": "Ingresar URL válida", "UploadValidFile": "Subir archivo válido", "EnterTemplateName": "Ingresar nombre de plantilla", "DuplicateStepsChooseDifferentName": "Pasos duplicados. Por favor, elija un nombre diferente", "StepAddedSuccessfully": "Paso agregado exitosamente", "FailedToAddWorkflowStep": "Error al agregar paso del flujo de trabajo", "ErrorWhileAddingWorkflow": "Error al agregar flujo de trabajo", "TentativeDateUpdatedSuccessfully": "Fecha tentativa actualizada exitosamente", "CouldntUpdateTentativeDate": "No se pudo actualizar la fecha tentativa", "PublicURLGenerated": "URL pública generada", "DeadlineExtendedSuccessfully": "Fecha límite extendida exitosamente", "BusinessUnitUpdated": "Unidad de negocio actualizada", "ActionAddedSuccessfully": "Acción agregada exitosamente", "MitigationPlanSubmittedSuccessfully": "Plan de mitigación enviado exitosamente", "PlanSavedSuccessfully": "Plan guardado exitosamente", "RegulationAddedSuccessfully": "Regulación agregada exitosamente", "RiskCreatedSuccessfully": "R<PERSON>go creado exitosamente", "ImportedSuccessfully": "Importado exitosamente", "RecordUpdatedSuccessfully": "Registro actualizado exitosamente", "PrivacyNoticeSavedSuccessfully": "Aviso de privacidad guardado exitosamente", "ProcessingCategoryUpdatedSuccessfully": "Categoría de procesamiento actualizada exitosamente", "ProcessingCategoryDeletedSuccessfully": "Categoría de procesamiento eliminada exitosamente", "PIILabelUpdatedSuccessfully": "Etiqueta PII actualizada exitosamente", "PIILabelDeletedSuccessfully": "Etiqueta PII eliminada exitosamente", "TemplateActivatedSuccessfully": "Plantilla activada exitosamente", "TemplateInactivatedSuccessfully": "Plantilla desactivada exitosamente", "TranslatedDataFetched": "<PERSON>tos traducidos obtenidos", "FormTranslated": "Formulario traducido", "GTMSettingsAppliedToAll": "Configuraciones GTM aplicadas a todos", "PIIAnalysisCompletedSuccessfully": "Análisis PII completado exitosamente", "RetentionRuleSavedSuccessfully": "Regla de retención guardada exitosamente", "MappingDeletedSuccessfully": "Mapeo eliminado exitosamente", "BreachEvaluationUpdatedSuccessfully": "Evaluación de violación actualizada exitosamente", "BreachAddedSuccessfully": "Violación agregada exitosamente", "BreachResolutionDoneSuccessfully": "Resolución de violación completada exitosamente", "PreventiveActionAddedSuccessfully": "Acción preventiva agregada exitosamente", "NotificationSavedSuccessfully": "Notificación guardada exitosamente", "CustomerAddedSuccessfully": "Cliente agregado exitosamente", "CustomerUpdatedSuccessfully": "Cliente actualizado exitosamente", "ResourceAddedSuccessfully": "Recurso agregado exitosamente", "CollaboratorAddedSuccessfully": "Colaborador agregado exitosamente", "PreferencesChangedSuccessfully": "Preferencias cambiadas exitosamente", "AddedSuccessfully": "{{type}} agregado exitosamente", "AssessmentStartedSuccessfully": "Evaluación iniciada con éxito"}}}