import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { Alert<PERSON>riangle, Refresh<PERSON>w, UserCheck, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Card, CardContent, CardHeader, CardTitle } from '../../../@/components/ui/card';
import SearchableSelect from '../../../@/components/ui/Common/Elements/Select/SearchableSelect';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../@/components/ui/tabs';
import httpClient from '../../../api/httpClient';
import { useUcmDashboard } from '../../../hooks/universal-consent-management/useUcmDashboard';
import { RootState } from '../../../redux/store';
import {
  ConsentRecordsTableProperties,
  EntityProperties,
} from '../../../types/universal-consent-management';
import { FETCH_ENTITIES } from '../../common/api';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import { ChartCardLoader } from '../../common/LoadingUI';
import {
  fetchDashboardConsentRecordsTable,
  get_ucm_collection_builder_dashboard_data,
  get_ucm_collection_builder_detailed_dashboard_data,
} from '../../common/services/universal-consent-management';
import AnalyticsTab from './AnalyticsTab';
import CollectionTemplatesTab from './CollectionTemplatesTab';
// import { FlippableCard } from './FlippableCard';

export interface AnalyticsTabProps {
  common_metrics: {
    total_consents: ConsentMetric;
    active_consents: ConsentMetric;
    declined_consents: ConsentMetric;
    withdrawal_consents: ConsentMetric;
    renewal_consents: ConsentMetric;
  };
  collection_template_metrics: CollectionTemplateMetrics;
  analytics_metrics: {
    consent_rate: PercentageMetric;
    opt_out_rate: PercentageMetric;
  };
  consent_funnel_analysis_metrics: ConsentFunnelMetrics;
  multi_channel_performance_metrics: MultiChannelPerformanceMetric[];
  geographical_insight_metrics: GeographicalInsightMetric[];
  collectionBuilderDetailedDashboardData: ConsentTemplateDetails[];
  isLoadingCollectionBuilderDashboardData: boolean;
}

export interface ConsentTemplateDetails {
  template_id: number;
  customer_id: number;
  entity_id: number;
  entity_name: string;
  subject_identity_type_id: number;
  pii_name: string;
  template_name: string;
  active_status: boolean;
  template_owner_id: number;
  owner_name: string;
  owner_email: string;
  created_at: string; // ISO Date string
  updated_at: string; // ISO Date string
  form_id: number;
  form_title: string;
  form_source_id: string;
  form_url: string;
  preference_center_id: number;
  preference_center_title: string;
  preference_center_source_id: string;
  preference_center_url: string;
  consent_sources: any[]; // Or use a more specific type if you know the structure
  total_mappings: number;
  mappings: number;
  basic_step: 'completed' | 'not_started' | 'in_progress'; // could be enum
  retention_and_source: 'completed' | 'not_started' | 'in_progress';
  user_communication: 'completed' | 'not_started' | 'in_progress';
  localization_setup: 'completed' | 'not_started' | 'in_progress';
  consent_rate: number; // percentage (0–100)
  submission: number;
}

// Shared Subtypes

export interface ConsentMetric {
  total_value: number;
  current_month_value: number;
  percentage_change_from_last_month: number;
}

export interface PercentageMetric {
  total_value: number; // e.g. 93%
  current_month_value: number; // e.g. 86%
  percentage_change_from_last_month: number;
}

export interface CollectionTemplateMetrics {
  customer_id: number;
  entity_id: number | null;
  total_templates: number;
  active_templates: number;
  inactive_templates: number;
  unique_owners: number;
  total_consent_source: number;
  all_filtered_mapping_count: number;
  active_ct_filtered_mapping_count: number;
  inactive_ct_filtered_mapping_count: number;
  total_mapping_count: number;
  active_ct_total_mapping_count: number;
  inactive_ct_total_mapping_count: number;
}

export interface ConsentFunnelMetrics {
  drop_off_rate: number;
  consent_form_view_count: number;
  consent_form_submission_count: number;
  preference_center_view_count: number;
  preference_center_submission_count: number;
}

export interface MultiChannelPerformanceMetric {
  channel_name: string;
  consent_rate: number;
  opt_out_rate: number;
  total_submissions: number;
}

export interface GeographicalInsightMetric {
  continent: string;
  total_count: number;
  consent_rate: number;
  drop_out_rate: number;
  total_contribution: number;
}

const Dashboard: React.FC = () => {
  const lastUpdatedDate = new Date();
  lastUpdatedDate.setDate(lastUpdatedDate.getDate() - 1);

  //! Variables
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const [selectedEntityId, setSelectedEntityId] = useState<string>('0');
  const [reloadCollectionTemplate, setReloadCollectionTemplate] = useState(false);

  // CUSTOM HOOK CALL

  const fetchCollectionBuilderDetailedDashboardData = async () => {
    const response = await get_ucm_collection_builder_detailed_dashboard_data(
      customer_id,
      Number(selectedEntityId)
    );
    console.log(response.result.data, 'response123');
    return response.result.data;
  };

  const {
    data: collectionBuilderDetailedDashboardData,
    isLoading: isLoadingCollectionBuilderDetailedDashboardData,
  } = useQuery({
    queryKey: [
      'collectionBuilderDetailsDashboardData',
      customer_id,
      selectedEntityId,
      reloadCollectionTemplate,
    ],
    queryFn: () => fetchCollectionBuilderDetailedDashboardData(),
    enabled: !!customer_id,
  });

  console.log(collectionBuilderDetailedDashboardData, 'collectionBuilderDetailedDashboardData');

  const {
    data: collectionBuilderDashboardData,
    isLoading: isLoadingCollectionBuilderDashboardData,
  } = useQuery({
    queryKey: [
      'collectionBuilderDashboardData',
      customer_id,
      selectedEntityId,
      reloadCollectionTemplate,
    ],
    queryFn: () => fetchCollectionBuilderDashboardData(),
    enabled: !!customer_id,
  });

  console.log(collectionBuilderDashboardData, 'collectionBuilderDashboardData123');

  const fetchCollectionBuilderDashboardData = async () => {
    const response = await get_ucm_collection_builder_dashboard_data(
      customer_id,
      Number(selectedEntityId)
    );
    console.log(response, 'response123');
    return response.result.data;
  };

  const { cardsData, isCardDataLoading } = useUcmDashboard();
  console.log(cardsData, ' card Data');

  //! STATES
  const [consentRecordsTable, setConsentRecordsTable] = useState<ConsentRecordsTableProperties[]>(
    []
  );
  // const [entities, setEntities] = useState<EntityProperties[]>([]);
  const [timeFrame, setTimeFrame] = useState<string>('90');
  const [isLoadingStats, setIsLoadingStats] = useState<boolean>(true);

  const { t } = useTranslation();

  // fetch consent records table
  useEffect(() => {
    const fetchConsentRecordsTable = async () => {
      try {
        // Send the data to the API and await the response
        const response = await fetchDashboardConsentRecordsTable(
          customer_id,
          timeFrame,
          selectedEntityId
        );

        if (response?.status_code === 200) {
          setConsentRecordsTable(response?.result?.data);
        }
      } catch (error) {
        // eslint-disable-next-line unicorn/prefer-ternary
        if (axios.isAxiosError(error)) {
          setConsentRecordsTable([]);
          // Axios specific error handling
          toast.dismiss(); // Clear any existing toasts
          // const status = error?.response?.data?.status_code;
          // const statusText = error?.response?.data?.message;
          const errorMessage = error?.response?.data?.result?.error || error.message;
          toast.error(`${errorMessage}`);
          console.error('Axios Error:', error);
        } else {
          // Generic error handling
          toast.dismiss();
          toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
          console.error('Unexpected Error:', error);
        }
      }
    };

    if (customer_id) fetchConsentRecordsTable();
  }, [customer_id, selectedEntityId]);

  //fetching entities

  const { data: entities } = useQuery<EntityProperties[]>({
    queryKey: ['entities', customer_id],
    queryFn: async () => {
      const responseData = await httpClient.get(`${FETCH_ENTITIES}${customer_id}`);
      const result = responseData?.data?.result?.rows;
      result.unshift({
        id: 0,
        name: 'All Entities',
        user_id: 0,
        spoc_id: 0,
        status: 'active',
        createdAt: '',
        updatedAt: '',
        deletedAt: null,
        parent_id: 0,
        customer_id: 0,
      });
      return result;
    },
    enabled: !!customer_id,
  });

  //IMPORTS FOR NEW DASHBOARD
  const [activeTab, setActiveTab] = useState('analytics');

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Universal Consent Management</h1>
          </div>
          <div className="flex justify-between">
            <div className="flex items-center justify-center gap-3 rounded-md bg-yellow-50 p-1 font-medium shadow-sm">
              <AlertTriangle className="size-6 text-yellow-600" />
              <div>
                <p className="text-sm text-yellow-700">
                  The data displayed reflects information collected up to{' '}
                  {convertDateToHumanView(lastUpdatedDate.toString())}
                </p>
              </div>
            </div>

            <SearchableSelect
              placeholder="All Entities"
              options={
                entities?.map((entity: any) => ({ value: entity.name, id: entity.id })) || []
              }
              value={selectedEntityId.toString()}
              onChange={(value) => setSelectedEntityId(value)}
              className="ml-6 h-10 w-48"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          {/* <FlippableCard
            title="Active Consents"
            icon={<UserCheck className="h-4 w-4 text-slate-400" />}
            data={cardsData?.active_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Total Consents</CardTitle>
                <AlertTriangle className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.total_consents?.total_value ??
                    '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.total_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {collectionBuilderDashboardData?.common_metrics?.total_consents
                    ?.percentage_change_from_last_month !== null ? (
                    <>
                      <span>
                        {collectionBuilderDashboardData?.common_metrics?.total_consents
                          ?.percentage_change_from_last_month ?? '-'}
                      </span>
                      <span
                        className={`${
                          collectionBuilderDashboardData?.common_metrics?.total_consents
                            ?.percentage_change_from_last_month >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        % from last month
                      </span>
                    </>
                  ) : (
                    <></>
                  )}
                </p>
              </CardContent>
            </Card>
          )}

          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Active Consents</CardTitle>
                <UserCheck className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.active_consents?.total_value ??
                    '-'}
                </div>

                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.active_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {collectionBuilderDashboardData?.common_metrics?.active_consents
                    ?.percentage_change_from_last_month !== null ? (
                    <>
                      <span>
                        {collectionBuilderDashboardData?.common_metrics?.active_consents
                          ?.percentage_change_from_last_month ?? '-'}
                      </span>
                      <span
                        className={`${
                          collectionBuilderDashboardData?.common_metrics?.active_consents
                            ?.percentage_change_from_last_month >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        % from last month
                      </span>
                    </>
                  ) : (
                    <></>
                  )}
                </p>
              </CardContent>
            </Card>
          )}
          {/* <FlippableCard
            title="Total Consents"
            icon={<Users className="h-4 w-4 text-slate-400" />}
            data={cardsData?.total_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Declined Consents</CardTitle>
                <Users className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.declined_consents?.total_value ??
                    '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.declined_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {collectionBuilderDashboardData?.common_metrics?.declined_consents
                    ?.percentage_change_from_last_month !== null ? (
                    <>
                      <span>
                        {collectionBuilderDashboardData?.common_metrics?.declined_consents
                          ?.percentage_change_from_last_month ?? '-'}
                      </span>
                      <span
                        className={`${
                          collectionBuilderDashboardData?.common_metrics?.declined_consents
                            ?.percentage_change_from_last_month >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        % from last month
                      </span>
                    </>
                  ) : (
                    <></>
                  )}
                </p>
              </CardContent>
            </Card>
          )}
          {/* <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
              <Shield className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent className="px-5">
              <div className="text-2xl font-bold text-green-600">96.8%</div>
              <p className="text-xs text-slate-600">Across all frameworks</p>
            </CardContent>
          </Card> */}
          {/* <FlippableCard
            title="Withdrawal Consents"
            icon={<AlertTriangle className="h-4 w-4 text-slate-400" />}
            data={cardsData?.withdrawal_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Withdrawal Consents</CardTitle>
                <AlertTriangle className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                    ?.total_value ?? '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                    ?.percentage_change_from_last_month !== null ? (
                    <>
                      <span>
                        {collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                          ?.percentage_change_from_last_month ?? '-'}
                      </span>
                      <span
                        className={`${
                          collectionBuilderDashboardData?.common_metrics?.withdrawal_consents
                            ?.percentage_change_from_last_month >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        % from last month
                      </span>
                    </>
                  ) : (
                    <></>
                  )}
                </p>
              </CardContent>
            </Card>
          )}
          {/* <FlippableCard
            title="Renewal Consents"
            icon={<RefreshCw className="h-4 w-4 text-slate-400" />}
            data={cardsData?.renewal_consents}
          /> */}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" />
            </div>
          ) : (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
                <CardTitle className="text-sm font-medium">Renewal Consents</CardTitle>
                <RefreshCw className="h-4 w-4 text-slate-400" />
              </CardHeader>
              <CardContent className="px-5 pb-6">
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.common_metrics?.renewal_consents?.total_value ??
                    '-'}
                </div>
                <p
                  className={`text-xs ${
                    collectionBuilderDashboardData?.common_metrics?.renewal_consents
                      ?.percentage_change_from_last_month >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {collectionBuilderDashboardData?.common_metrics?.renewal_consents
                    ?.percentage_change_from_last_month !== null ? (
                    <>
                      <span>
                        {collectionBuilderDashboardData?.common_metrics?.renewal_consents
                          ?.percentage_change_from_last_month ?? '-'}
                      </span>
                      <span
                        className={`${
                          collectionBuilderDashboardData?.common_metrics?.renewal_consents
                            ?.percentage_change_from_last_month >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        % from last month
                      </span>
                    </>
                  ) : (
                    <></>
                  )}
                </p>
              </CardContent>
            </Card>
          )}
          {/* <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 px-5 pb-2">
              <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
              <Zap className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent className="px-5 pb-6">
              <div className="text-2xl font-bold">180ms</div>
              <p className="text-xs text-slate-600">Avg response time</p>
            </CardContent>
          </Card> */}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="templates">Collection Templates</TabsTrigger>
            {/* <TabsTrigger value="ucm-lab">UCM Lab Foundation</TabsTrigger> */}
            {/* <TabsTrigger value="lifecycle">Consent Lifecycle</TabsTrigger> */}
            {/* <TabsTrigger value="audit">Audit Trail</TabsTrigger> */}
            {/* <TabsTrigger value="workflows">Workflows</TabsTrigger> */}
            {/* <TabsTrigger value="settings">Settings</TabsTrigger> */}
          </TabsList>

          {/* UCM Lab Foundation Setup */}
          {/* <TabsContent value="ucm-lab" className="space-y-6">
            <UcmLabFoundationTab
              setUcmLabSetupComplete={setUcmLabSetupComplete}
            />
          </TabsContent> */}

          {/* Collection Templates Management */}
          <TabsContent value="templates" className="space-y-6">
            <CollectionTemplatesTab
              collectionBuilderDetailedDashboardData={collectionBuilderDetailedDashboardData}
              isLoadingCollectionBuilderDetailedDashboardData={
                isLoadingCollectionBuilderDetailedDashboardData
              }
              collectionBuilderDashboardData={collectionBuilderDashboardData}
              selectedEntityId={selectedEntityId}
              isLoadingCollectionBuilderDashboardData={isLoadingCollectionBuilderDashboardData}
              onReloadTemplates={setReloadCollectionTemplate}
            />
          </TabsContent>

          {/* Consent Lifecycle Management */}
          {/* <TabsContent value="lifecycle" className="space-y-6">
            <ConsentLifecycleTab />
          </TabsContent> */}

          {/* Compliance Dashboard */}
          {/* <TabsContent value="compliance" className="space-y-8">
            <ComplianceDashboardTab />
          </TabsContent> */}

          {/* Audit Trail */}
          {/* <TabsContent value="audit" className="space-y-8">
            <AuditTrailTab />
          </TabsContent> */}

          {/* Workflow Management */}
          {/* <TabsContent value="workflows" className="space-y-6">
            <WorkflowsTab />
          </TabsContent> */}

          {/* Analytics */}
          <TabsContent value="analytics" className="space-y-8">
            <AnalyticsTab
              collectionBuilderDetailedDashboardData={collectionBuilderDetailedDashboardData}
              isLoadingCollectionBuilderDashboardData={isLoadingCollectionBuilderDashboardData}
              common_metrics={collectionBuilderDashboardData?.common_metrics}
              collection_template_metrics={
                collectionBuilderDashboardData?.collection_template_metrics
              }
              analytics_metrics={collectionBuilderDashboardData?.analytics_metrics}
              consent_funnel_analysis_metrics={
                collectionBuilderDashboardData?.consent_funnel_analysis_metrics
              }
              multi_channel_performance_metrics={
                collectionBuilderDashboardData?.multi_channel_performance_metrics
              }
              geographical_insight_metrics={
                collectionBuilderDashboardData?.geographical_insight_metrics
              }
            />
          </TabsContent>

          {/* Settings */}
          {/* <TabsContent value="settings" className="space-y-6">
            <SettingsTab />
          </TabsContent> */}
        </Tabs>
      </div>
    </>
  );
};

export default Dashboard;
