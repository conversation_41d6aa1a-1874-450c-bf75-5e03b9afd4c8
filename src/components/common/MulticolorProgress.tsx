import React from 'react';

interface MultiColorProgressProps {
  segments: { color: string; value: number; label?: string }[];
}

export const MultiColorProgress: React.FC<MultiColorProgressProps> = ({ segments }) => {
  return (
    <div className="flex h-4 w-full overflow-hidden rounded-full bg-gray-200">
      {segments.map((segment, idx) => (
        <div
          key={idx}
          className={`${segment.color} h-full`}
          style={{ width: `${segment.value}%` }}
          title={segment.label || `${segment.value}`}
        />
      ))}
    </div>
  );
};
