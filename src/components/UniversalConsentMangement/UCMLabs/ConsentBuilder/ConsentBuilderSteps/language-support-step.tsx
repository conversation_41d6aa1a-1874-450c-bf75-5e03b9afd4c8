import axios from 'axios';
import React, { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Badge } from '../../../../../@/components/ui/badge';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../@/components/ui/Common/Elements/Select/Select';
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from '../../../../../@/components/ui/Common/Elements/Tabs/Tabs';
import { RootState } from '../../../../../redux/store';
import { BasicInfo } from '../../../../../types/universal-consent-management';
import {
  get_default_or_translated_data,
  get_languages_universal,
  get_supported_languages,
  post_default_or_translated_data,
} from '../../../../common/services/universal-consent-management';
import { useStepper } from '../../../../common/Stepper';
import { StepperFormActions } from './consent-builder-stepper';
import DynamicSection from './Language-Support/dynamic-section';

type DataType = Record<string, any> | Array<any>;

const LanguageSupport = () => {
  const allowedKeys = [
    'collection_template',
    'name',
    'processing_purpose_categories',
    'processing_purposes',
    'consent_purposes',
    'pii_labels',
    'description',
    'title',
    'pii_input_heading',
    'preference_input_heading',
    'consent_flow_heading',
    'form_footer_content',
    'dsr_content',
    'dsrContent',
    'privacy_notice_heading',
    'preference_center_heading',
    'dsr_center_heading',
    'privacy_notes',
    'content',
    'form_configurations',
    'form',
    'heading',
    'text',
    'pii_section',
    'consent_collection_section',
    'form_footer',
    'submit_button',
    'preference_center_configuration',
    'user_input_configuration',
    'before_email',
    'after_email',
    'verify_input_configuration',
    'consent_purpose_configuration',
  ];
  const { nextStep } = useStepper();
  const dispatch = useDispatch();
  const customer_id = useSelector(
    (state: RootState) => state.auth.login.login_details?.customer_id
  );

  const collection_template_id = useSelector(
    (state: RootState) => state?.UCM?.CollectionTemplateData?.id
  );

  const textareaRefs = useRef<Record<string, HTMLTextAreaElement | null>>({});

  const tabData = ['Basic Info & Consent', 'Consent Form', 'Preference Form'];

  //! States
  const [activeTab, setActiveTab] = useState<string>('0');
  const [supportedLanguages, setSupportedLanguages] = useState<
    { language_code: string; language: string }[]
  >([]);
  const [languages, setLanguages] = useState([]);
  const [basicInfo, setBasicInfo] = useState<BasicInfo>();
  const [consentForm, setConsentForm] = useState();
  const [consentFormData, setConsentFormData] = useState<DataType>();
  const [preferenceForm, setPreferenceForm] = useState();
  const [basicInfoFormData, setBasicInfoFormData] = useState<DataType | BasicInfo>();
  const [preferenceFormData, setPreferenceFormData] = useState<DataType>();
  const [showLanguageModal, setShowLanguageModal] = useState<boolean>(true);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [sourceLanguage, setSourceLanguage] = useState<string>('en');
  const [disableLanguageModalButton, setDisableLanguageModalButton] = useState<boolean>(false);
  const [basicInfoApiCallMethod, setBasicInfoApiCallMethod] = useState<'post' | 'put'>('post');
  const [consentFormApiCallMethod, setConsentFormApiCallMethod] = useState<'post' | 'put'>('post');
  const [preferenceFormApiCallMethod, setPreferenceFormApiCallMethod] = useState<'post' | 'put'>(
    'post'
  );
  const [refetchSupportedLanguages, setRefetchSupportedLanguages] = useState<boolean>(false);
  const [showAddMoreLanguageButton, setShowAddMoreLanguageButton] = useState<boolean>(false);

  //! Logs

  //! Effects

  // fetching Languages
  // useEffect(() => {
  //   const fetchLanguages = async () => {
  //     try {
  //       // Call the returned function to fetch data
  //       const responseData = await get_supported_languages('universal', customer_id);
  //       setLanguages(responseData?.result?.data);
  //     } catch (error) {
  //       console.error(error);
  //     }
  //   };

  //   fetchLanguages();
  // }, [customer_id]);

  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        const response = await get_languages_universal();
        setLanguages(response?.result?.data);
      } catch (error) {
        console.log(error);
      }
    };

    fetchLanguages();
  }, []);

  // fetching Supported Languages
  useEffect(() => {
    const fetchSupportedLanguages = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_supported_languages(
          'collection_template',
          customer_id,
          collection_template_id
        );
        setSupportedLanguages(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchSupportedLanguages();
  }, [customer_id, refetchSupportedLanguages]);

  // fetching Basic Info
  useEffect(() => {
    const fetchDefaultOrTranslatedData = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_default_or_translated_data(
          customer_id,
          collection_template_id,
          'defaults',
          'basics'
        );
        setBasicInfo(responseData?.result?.data);
        setBasicInfoFormData(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchDefaultOrTranslatedData();
  }, [customer_id, collection_template_id]);

  // fetching Form
  useEffect(() => {
    const fetchDefaultOrTranslatedData = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_default_or_translated_data(
          customer_id,
          collection_template_id,
          'defaults',
          'form'
        );
        setConsentForm(responseData?.result?.data?.form);
        setConsentFormData(responseData?.result?.data?.form);
      } catch (error) {
        console.error(error);
      }
    };

    fetchDefaultOrTranslatedData();
  }, [customer_id, collection_template_id]);

  // fetching Preference Form
  useEffect(() => {
    const fetchDefaultOrTranslatedData = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_default_or_translated_data(
          customer_id,
          collection_template_id,
          'defaults',
          'preference_center'
        );
        setPreferenceForm(responseData?.result?.data?.preference_forms);
        setPreferenceFormData(responseData?.result?.data?.preference_forms);
      } catch (error) {
        console.error(error);
      }
    };

    fetchDefaultOrTranslatedData();
  }, [customer_id, collection_template_id]);

  useEffect(() => {
    handleLanguage();
  }, [selectedLanguage]);

  const adjustTextareaHeight = (key: string) => {
    const el = textareaRefs.current[key];
    if (el) {
      el.style.height = 'auto';
      el.style.height = `${el.scrollHeight}px`;
    }
  };

  //! Google Language Translation API Integration

  // Helper function to chunk array into groups of specified size
  const chunkArray = <T,>(array: T[], chunkSize: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  };
  function setNestedValue(obj: any, path: string, value: any): void {
    // 1) convert [n] → .n, 2) trim any leading '.', 3) split and drop empty strings
    const pathParts = path
      .replace(/\[(\w+)\]/g, '.$1')
      .replace(/^\./, '')
      .split('.')
      .filter((p) => p !== '');

    let current: any = obj;

    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      // detect numeric indices
      const key = /^\d+$/.test(part) ? Number(part) : part;

      // if missing, create either an array or object depending on next part
      if (current[key] === undefined) {
        const nextPart = pathParts[i + 1];
        current[key] = /^\d+$/.test(nextPart) ? [] : {};
      }

      current = current[key];
    }

    // final assignment
    const lastPart = pathParts[pathParts.length - 1];
    const lastKey = /^\d+$/.test(lastPart) ? Number(lastPart) : lastPart;
    current[lastKey] = value;
  }

  function updateCookieDataWithTranslations(
    cookieData: any | null,
    mappingPaths: string[],
    translatedTexts: string[]
  ) {
    const updatedData = JSON.parse(JSON.stringify(cookieData)); // deep clone
    mappingPaths.forEach((path, index) => {
      setNestedValue(updatedData, path, translatedTexts[index]);
    });
    return updatedData;
  }

  function flattenForTranslation(
    data: any,
    allowedKeys: string[],
    parentKey = ''
  ): { texts: string[]; paths: string[] } {
    let texts: string[] = [];
    let paths: string[] = [];

    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        const { texts: t, paths: p } = flattenForTranslation(
          item,
          allowedKeys,
          `${parentKey}[${index}]`
        );
        texts = texts.concat(t);
        paths = paths.concat(p);
      });
    } else if (typeof data === 'object' && data !== null) {
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          const fullPath = parentKey ? `${parentKey}.${key}` : key;
          // Only add string values if the key is allowed.
          if (typeof data[key] === 'string' && allowedKeys.includes(key)) {
            texts.push(data[key]);
            paths.push(fullPath);
          } else if (typeof data[key] === 'object' && data[key] !== null) {
            const { texts: t, paths: p } = flattenForTranslation(data[key], allowedKeys, fullPath);
            texts = texts.concat(t);
            paths = paths.concat(p);
          }
        }
      }
    }
    return { texts, paths };
  }

  function createTranslateRequestBody(
    cookieData: any | null,
    allowedKeys: string[],
    targetLanguage = 'fr'
  ) {
    const { texts, paths } = flattenForTranslation(cookieData, allowedKeys);
    const requestBody = {
      q: texts,
      target: targetLanguage,
    };
    return { requestBody, mappingPaths: paths };
  }

  const translateCookieData = async (translateData: any | null, setData: (data: any) => void) => {
    // Step 1: Create the request body using only allowed keys.
    const { requestBody, mappingPaths } = createTranslateRequestBody(
      translateData,
      allowedKeys,
      selectedLanguage
    );

    try {
      // setTranslationLoading(true);

      // Get the texts to translate
      const textsToTranslate = requestBody.q;

      // Chunk the texts into groups of 100
      const textChunks = chunkArray(textsToTranslate, 100);

      // Array to hold all translated texts
      let allTranslatedTexts: string[] = [];

      // Process each chunk
      for (let i = 0; i < textChunks.length; i++) {
        const chunk = textChunks[i];

        // Create a request body for this chunk
        const chunkRequestBody = {
          q: chunk,
          target: selectedLanguage?.trim(),
        };

        // Call the Google Translate API for this chunk
        const response = await fetch(
          `https://translation.googleapis.com/language/translate/v2?key=${import.meta.env.VITE_GOOGLE_TRANSLATE_API_KEY}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(chunkRequestBody),
          }
        );
        const data = await response.json();

        // Extract the translated texts from this chunk
        const chunkTranslatedTexts: string[] = data.data.translations.map(
          (item: { translatedText: string }) => item.translatedText
        );

        // Add to our collection of all translated texts
        allTranslatedTexts = [...allTranslatedTexts, ...chunkTranslatedTexts];
      }

      // Step 4: Update the original cookieData with all the translated texts.
      const updatedCookieData = updateCookieDataWithTranslations(
        translateData,
        mappingPaths,
        allTranslatedTexts
      );

      // Update state with the translated data.
      // setTranslatedData(updatedCookieData);

      console.log('updated lang data: ', updatedCookieData);
      setData(updatedCookieData);
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      // setTranslationLoading(false);
    }
  };

  //! Helper functions

  const convertPostBasicInfoData = (basicInfoFormData: BasicInfo, collectionTemplateId: number) => {
    const mapProcessingData = (data: Array<any>, type: string) =>
      data.map((item) => {
        const mappedItem: Record<string, any> = {
          [`${type}_id`]: item.id,
          [`${type}_trans_name`]: item.name || 'name',
          [`${type}_trans_description`]: item.description || 'description',
        };

        if (item.translation_record_id) {
          mappedItem[`${type}_translation_record_id`] = item.translation_record_id;
        }

        return mappedItem;
      });

    const result: Record<string, any> = {
      data: {
        ct_id: collectionTemplateId,
        ct_trans_name: basicInfoFormData?.collection_template?.name || 'default name',
        ppc_data: mapProcessingData(basicInfoFormData?.processing_purpose_categories || [], 'ppc'),
        pp_data: mapProcessingData(basicInfoFormData?.processing_purposes || [], 'pp'),
        cp_data: mapProcessingData(basicInfoFormData?.consent_purposes || [], 'cp'),
        pii_data: mapProcessingData(basicInfoFormData?.pii_labels || [], 'pii'),
      },
    };

    // Conditionally add ct_translation_record_id if translation_record_id is present
    if (basicInfoFormData?.collection_template?.translation_record_id) {
      result.data.ct_translation_record_id =
        basicInfoFormData.collection_template.translation_record_id;
    }

    return result;
  };

  const convertPreferenceFormData = (preferenceFormData: Array<any>) => {
    const mapPreferenceData = (data: Array<any>) =>
      data.map((item) => {
        const mappedItem: any = {
          pc_id: item.id,
          pc_trans_name: item.title || 'name',
          pc_trans_description: item.description || 'description',
          // pc_trans_privacy_note: item.privacy_note
          //   ? item.privacy_note.map((note: any) => ({
          //       id: note.id,
          //       title: note.title,
          //       content: note.content,
          //     }))
          //   : 'privacy_note',
          pc_trans_dsr_content: item.dsr_content || 'dsr_content',
          pc_trans_preference_center_configuration: item.preference_center_configuration,
        };

        // Add pc_translation_record_id if translation_record_id exists
        if (item.translation_record_id) {
          mappedItem.pc_translation_record_id = item.translation_record_id;
        }

        return mappedItem;
      });

    return {
      data: mapPreferenceData(preferenceFormData),
    };
  };

  const convertPrivacyNoteData = (preferenceFormData: Array<any>) => {
    const mapPreferenceData = (data: Array<any>) =>
      data.map((item) => {
        const mappedItem: any = {
          privacy_note_id: item?.privacy_notes?.id,
          pn_name: item?.privacy_notes?.name,
          pn_content: item?.privacy_notes?.content,
        };

        // Add pn_translation_record_id if translation_record_id exists
        if (item?.privacy_notes?.translation_record_id) {
          mappedItem.privacy_note_translation_record_id = item.privacy_notes.translation_record_id;
        }

        return mappedItem;
      });

    return {
      data: mapPreferenceData(preferenceFormData),
    };
  };

  //! Handler
  const handleNext = () => {
    nextStep();
  };

  const handleLanguage = async (): Promise<void> => {
    const isSupportedLanguage = supportedLanguages.some(
      (lang: { language_code: string; language: string }) => lang.language_code === selectedLanguage
    );

    const handleError = (
      error: unknown,
      defaultMessage = 'An unexpected error occurred.'
    ): void => {
      toast.dismiss();
      const errorMessage = axios.isAxiosError(error)
        ? error?.response?.data?.result?.error || error.message
        : defaultMessage;
      toast.error(errorMessage);
      console.error('Error:', error);
    };

    const fetchData = async (
      type: string,
      setData: (data: any) => void,
      setMethod: (method: 'put' | 'post') => void,
      key: string | null
    ): Promise<void> => {
      try {
        const responseData = await get_default_or_translated_data(
          customer_id,
          collection_template_id,
          'translated',
          type,
          selectedLanguage
        );
        setData(key ? responseData?.result?.data?.[key] : responseData?.result?.data);
        setMethod('put');
      } catch (error) {
        setMethod('post');
        handleError(error);
      }
    };

    const translateData = async (
      sourceData: any,
      setData: (data: any) => void,
      setMethod: (method: 'put' | 'post') => void
    ): Promise<void> => {
      try {
        console.log('sourceData: ', sourceData);
        translateCookieData(sourceData, setData);

        // setData(translateFormData(sourceData, translationMap));
        setMethod('post');
      } catch (error) {
        handleError(error);
        setMethod('post');
      }
    };

    toast.dismiss();
    toast.loading(t('CommonErrorMessages.ProcessingEllipsis'));
    setDisableLanguageModalButton(true);

    try {
      if (isSupportedLanguage) {
        await fetchData('basics', setBasicInfoFormData, setBasicInfoApiCallMethod, null);
        await fetchData('form', setConsentFormData, setConsentFormApiCallMethod, 'form');
        await fetchData(
          'preference_center',
          setPreferenceFormData,
          setPreferenceFormApiCallMethod,
          'preference_forms'
        );
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.UniversalConsentManagement.TranslatedDataFetched'));
      } else {
        await translateData(basicInfo, setBasicInfoFormData, setBasicInfoApiCallMethod);
        await translateData(consentForm, setConsentFormData, setConsentFormApiCallMethod);
        await translateData(preferenceForm, setPreferenceFormData, setPreferenceFormApiCallMethod);
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.UniversalConsentManagement.formTranslated'));
        setSourceLanguage(selectedLanguage);
      }
      setShowLanguageModal(false);
    } catch (error) {
      toast.dismiss();
      handleError(error);
    } finally {
      toast.dismiss();
      setDisableLanguageModalButton(false);
    }
  };
  const { t } = useTranslation();

  const handleSaveTranslationForBasicInfo = async (basicInfo: BasicInfo) => {
    const data = convertPostBasicInfoData(basicInfo, basicInfo?.collection_template?.id);
    const payload = {
      customer_id,
      language_code: selectedLanguage,
      mode_type: 'basics',
      data: data?.data,
    };

    try {
      const response = await post_default_or_translated_data(payload, basicInfoApiCallMethod);
      toast.dismiss();
      toast.success(response?.message);
      setBasicInfoApiCallMethod('put');
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.ApiErrors.UnexpectedErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }
  };

  const handleSaveTranslationForPreferenceForm = async (preferenceForm: any) => {
    const data = convertPreferenceFormData(preferenceForm);
    const privacyNoteData = convertPrivacyNoteData(preferenceForm);
    const preferenceFormPayload = {
      customer_id,
      language_code: selectedLanguage,
      mode_type: 'preference_center',
      data: { pc_data: data?.data, privacy_note_data: privacyNoteData?.data },
    };

    try {
      const response = await post_default_or_translated_data(
        preferenceFormPayload,
        preferenceFormApiCallMethod
      );
      toast.dismiss();
      toast.success(response?.message);
      setPreferenceFormApiCallMethod('put');
      setRefetchSupportedLanguages((prev) => !prev);
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }
  };

  const handleSaveTranslationForConsentForm = async (consentForm: any) => {
    const consentFormPayload = {
      customer_id,
      language_code: selectedLanguage,
      mode_type: 'form',
      data: {
        form_id: consentForm?.id,
        form_trans_name: consentForm?.title,
        form_trans_description: consentForm?.description,
        form_trans_pii_input_heading: consentForm?.pii_input_heading,
        form_trans_preference_input_heading: consentForm?.preference_input_heading,
        form_trans_form_footer_content: consentForm?.form_footer_content,
        ...(consentForm?.translation_record_id && {
          form_translation_record_id: consentForm.translation_record_id,
        }),
        form_trans_form_configurations: consentForm?.form_configurations,
      },
    };

    try {
      const response = await post_default_or_translated_data(
        consentFormPayload,
        consentFormApiCallMethod
      );
      toast.dismiss();
      toast.success(response?.message);
      setConsentFormApiCallMethod('put');
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.ApiErrors.UnexpectedErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }
  };

  // Recursively traverses the object and returns a JSX element tree.
  const updateNestedValue = (obj: any, keyPath: string[], newValue: any): any => {
    if (keyPath.length === 0) return newValue;

    const [firstKey, ...restKeys] = keyPath;

    return {
      ...obj,
      [firstKey]: updateNestedValue(obj?.[firstKey] ?? {}, restKeys, newValue),
    };
  };

  const traverseAndCreateDiv = (
    obj: any,
    allowedKeys: string[],
    isTranslated: boolean = false,
    translatedData?: any | null,
    setTranslatedData?: React.Dispatch<React.SetStateAction<any | null>>,
    path: string[] = [] // ✅ Track path
  ): React.ReactElement => {
    if (obj === null || typeof obj !== 'object') return <></>;

    if (Array.isArray(obj)) {
      return (
        <>
          {obj.map((item, index) => (
            <React.Fragment key={index}>
              {traverseAndCreateDiv(
                item,
                allowedKeys,
                isTranslated,
                translatedData,
                setTranslatedData,
                [...path, String(index)] // ✅ Track array index path
              )}
            </React.Fragment>
          ))}
        </>
      );
    }

    return (
      <div className="flex h-fit w-full flex-col gap-4">
        {Object.keys(obj).map((key) => {
          const value = obj[key as keyof any];

          // 🧠 Get translated value safely
          let translatedValue: string = '';
          let nestedTranslated = translatedData?.[key as keyof any];
          if (translatedData && key in translatedData) {
            const raw = translatedData[key as keyof any];
            if (typeof raw === 'string') {
              translatedValue = raw;
            } else if (typeof raw === 'number') {
              translatedValue = raw.toString();
            } else {
              translatedValue = JSON.stringify(raw);
            }
          }

          const textareaValue = translatedValue || String(value);

          // ✅ Handle nested objects
          let translatedSubObject: any | null = null;
          if (
            nestedTranslated &&
            typeof nestedTranslated === 'object' &&
            !Array.isArray(nestedTranslated)
          ) {
            translatedSubObject = nestedTranslated as unknown as any;
          }

          const uniqueKey = [...path, key].join('.'); // Unique key for ref map

          return allowedKeys.includes(key) && typeof value === 'string' && value.trim() !== '' ? (
            <div key={uniqueKey} className="grid w-full grid-cols-2 gap-4">
              {/* Original (Left) */}
              <div className="flex flex-col gap-1">
                <p className="font-semibold">{formatKey(key)}</p>
                <p className="rounded-lg border border-primary-border bg-primary-background p-2">
                  {value}
                </p>
              </div>

              {/* Translated (Right) */}
              <div className="flex flex-col gap-1">
                <p className="font-semibold">{formatKey(key)}</p>
                {isTranslated && translatedData && setTranslatedData ? (
                  <textarea
                    ref={(el) => {
                      textareaRefs.current[uniqueKey] = el;
                      if (el) {
                        setTimeout(() => {
                          el.style.height = 'auto';
                          el.style.height = `${el.scrollHeight}px`;
                        }, 0);
                      }
                    }}
                    className="h-fit w-full resize-none overflow-hidden rounded-lg border border-primary-border bg-primary-background p-2"
                    value={textareaValue}
                    onChange={(e) => {
                      textareaRefs.current[uniqueKey] = e.target;
                      adjustTextareaHeight(uniqueKey);

                      setTranslatedData((prev: any) =>
                        updateNestedValue(prev || {}, [...path, key], e.target.value)
                      );
                    }}
                  />
                ) : (
                  <p className="rounded-lg border border-primary-border bg-primary-background p-2">
                    {translatedValue || ''}
                  </p>
                )}
              </div>
            </div>
          ) : typeof value === 'object' && value !== null && Object.keys(value).length > 0 ? (
            <div key={uniqueKey} className="col-span-2">
              {traverseAndCreateDiv(
                value,
                allowedKeys,
                isTranslated,
                translatedSubObject,
                setTranslatedData,
                [...path, key]
              )}
            </div>
          ) : null;
        })}
      </div>
    );
  };

  // Formats the key for a more user-friendly output.
  function formatKey(key: string): string {
    let formatted: string;
    if (key.includes('_')) {
      // Handle snake_case by replacing underscores with spaces.
      formatted = key.split('_').join(' ');
    } else {
      // Handle camelCase by inserting a space before each uppercase letter.
      formatted = key.replace(/([a-z])([A-Z])/g, '$1 $2');
    }
    formatted = formatted.trim().toLowerCase();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  }

  return (
    <>
      <StepperFormActions handleClick={handleNext} />
      <main className="mt-2.5 h-[600px] w-full rounded-lg border border-primary-border bg-primary-background font-primary-text">
        <section className={`flex size-full flex-col gap-4 p-4`}>
          <nav className="flex w-full flex-shrink flex-row items-center justify-between gap-3 font-poppins">
            <div className="flex w-2/3 flex-row items-center gap-2">
              {supportedLanguages?.length > 0 && (
                <p className="w-fit text-xs">Translated Language(s): </p>
              )}
              <div className="flex w-fit flex-row flex-wrap items-center gap-2">
                {supportedLanguages?.map(
                  (language: { language_code: string; language: string }) => (
                    <Badge
                      onClick={() => setSelectedLanguage(language?.language_code)}
                      key={language?.language_code}
                      className={`cursor-pointer ${
                        selectedLanguage === language?.language_code
                          ? 'bg-primary text-white'
                          : 'bg-gray-300 text-black'
                      }`}
                    >
                      {language?.language}
                    </Badge>
                  )
                )}
              </div>
            </div>

            <div className="flex h-full w-1/3 max-w-[350px] flex-row items-center gap-2">
              <Select
                value={selectedLanguage}
                onValueChange={(value) => setSelectedLanguage(value)}
              >
                <SelectTrigger className={`border border-solid border-border`}>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent className="font-primary-text">
                  <SelectGroup>
                    <SelectItem value="-1" disabled>
                      Select
                    </SelectItem>
                    {languages.length > 0 ? (
                      languages.map((item: { language_code: string; language: string }) => (
                        <SelectItem
                          key={item.language_code}
                          value={item.language_code}
                          id={item.language_code}
                        >
                          {item.language}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="-2" disabled>
                        No Data
                      </SelectItem>
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <Button
                className="h-full text-primary-background"
                onClick={() => {
                  if (activeTab === '0') {
                    handleSaveTranslationForBasicInfo(basicInfoFormData as BasicInfo);
                  } else if (activeTab === '1') {
                    handleSaveTranslationForConsentForm(consentFormData);
                  } else if (activeTab === '2') {
                    handleSaveTranslationForPreferenceForm(preferenceFormData);
                  }
                }}
              >
                Save Translation
              </Button>
            </div>
          </nav>
          <Tabs
            value={activeTab}
            onValueChange={(currentTab) => setActiveTab(currentTab)}
            className="h-12 min-w-[500px] max-w-[700px] items-center rounded-lg bg-[#f6f6f6] p-2"
          >
            <TabsList className="flex size-full flex-row items-center justify-between">
              {tabData?.map((item, index) => (
                <Button variant="ghost" className="w-full p-0">
                  <TabsTrigger
                    value={index?.toString()}
                    className={`${activeTab === index?.toString() ? 'text-custom-primary' : ''} w-full`}
                  >
                    {item}
                  </TabsTrigger>
                </Button>
              ))}
            </TabsList>
          </Tabs>

          <section className={`flex h-full w-full flex-row overflow-auto`}>
            <section className="flex h-full w-full flex-row items-center justify-between gap-2">
              {/* <div className="w-1/2">{basicInfo?.collection_template?.name}</div>
              <div className="w-1/2">
                <input></input>
              </div> */}
              {activeTab === '0' && (
                <DynamicSection
                  data={basicInfo ? basicInfo : []}
                  setFormData={setBasicInfoFormData}
                  formData={basicInfoFormData}
                  allowedKeys={allowedKeys}
                />
                // {traverseAndCreateDiv(
                //   basicInfo,
                //   allowedKeys,
                //   true,
                //   basicInfoFormData,
                //   setBasicInfoFormData
                // )}
              )}
              {activeTab === '1' && (
                <DynamicSection
                  data={consentForm ? consentForm : []}
                  setFormData={setConsentFormData}
                  formData={consentFormData}
                  allowedKeys={allowedKeys}
                />
              )}
              {activeTab === '2' && (
                <DynamicSection
                  data={preferenceForm ? preferenceForm : []}
                  setFormData={setPreferenceFormData}
                  formData={preferenceFormData}
                  allowedKeys={allowedKeys}
                />
              )}
            </section>
          </section>
        </section>
      </main>
    </>
  );
};

export default LanguageSupport;
