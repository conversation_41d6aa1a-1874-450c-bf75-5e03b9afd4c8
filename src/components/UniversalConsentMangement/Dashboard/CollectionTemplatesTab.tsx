import {
  Building,
  Calendar,
  Circle,
  Clock,
  Code,
  Database,
  Edit,
  Eye,
  FileText,
  Key,
  LinkIcon,
  Settings,
  Users,
  Workflow,
} from 'lucide-react';
import React, { useState } from 'react';
import { Badge } from '../../../@/components/ui/badge';

import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../../@/components/ui/Common/Elements/Select/DropDownMenu';
import httpClient from '../../../api/httpClientNew';
import { RootState } from '../../../redux/store';
import { UCM_REMOVE_COLLECTION_TEMPLATE } from '../../common/api';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import { ChartCardLoader } from '../../common/LoadingUI';
import { change_collection_template } from '../../common/services/universal-consent-management';
import ShadcnDialog from '../../common/shadcn-dialog';
import { AnalyticsTabProps, ConsentTemplateDetails } from './DashboardV2';

const CollectionTemplatesTab: React.FC<{
  collectionBuilderDashboardData: AnalyticsTabProps;
  selectedEntityId: string;
  isLoadingCollectionBuilderDashboardData: boolean;
  collectionBuilderDetailedDashboardData: ConsentTemplateDetails[];
  isLoadingCollectionBuilderDetailedDashboardData: boolean;
  onReloadTemplates: (value: boolean) => void;
}> = ({
  collectionBuilderDashboardData,
  selectedEntityId,
  isLoadingCollectionBuilderDashboardData,
  collectionBuilderDetailedDashboardData,
  isLoadingCollectionBuilderDetailedDashboardData,
  onReloadTemplates,
}) => {
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const { t } = useTranslation();

  const [deletingCollectionTemplateData, setDeletingCollectionTemplateData] = useState({});
  const [deletingCollectionTemplateDialogOpen, setDeletingCollectionTemplateDialogOpen] =
    useState(false);

  const { mutate: deleteCollectionTemplate } = useMutation({
    mutationFn: async (data: any) => {
      const response = await httpClient.put(
        UCM_REMOVE_COLLECTION_TEMPLATE,
        deletingCollectionTemplateData
      );
      return response;
    },
    onSuccess: (data) => {
      toast.dismiss();
      toast.success(t('ToastMessages.General.DeletedSuccessfully'));
      onReloadTemplates(true);
      setDeletingCollectionTemplateDialogOpen(false);
    },
    onError: (error) => {
      toast.dismiss();
      toast.error(t('Failed to delete'));
      setDeletingCollectionTemplateDialogOpen(false);
    },
  });

  // Handler

  const handleStatusChange = async (
    status: boolean,
    collectionTemplateId: number,
    event: React.MouseEvent
  ) => {
    event.stopPropagation(); // Prevent row click handler from being executed
    try {
      // Make the API call to update the status
      const requestBody = {
        record_id: collectionTemplateId,
        customer_id: customer_id,
        active_status: status,
      };

      const response = await change_collection_template(requestBody);
      if (response.success) {
        toast.success(
          t(
            status
              ? 'ToastMessages.General.TemplateActivatedSuccessfully'
              : 'ToastMessages.General.TemplateInactivatedSuccessfully'
          )
        );
        onReloadTemplates(true);
        // Optionally refresh the table data or set state to reflect changes
      } else {
        throw new Error(response.data.message || 'Failed to update status');
      }
    } catch (error: any) {
      console.error('Error updating status:', error);
      toast.error(error?.response?.data?.message || 'Failed to update status');
    }
  };

  return (
    <>
      {deletingCollectionTemplateDialogOpen && (
        <ShadcnDialog
          open={deletingCollectionTemplateDialogOpen}
          onOpenChange={setDeletingCollectionTemplateDialogOpen}
          title="Delete Collection Template"
          footer={
            <div className="flex w-full flex-row justify-end gap-2">
              <Button
                variant="outline"
                className="text-primary"
                onClick={() => setDeletingCollectionTemplateDialogOpen(false)}
              >
                No
              </Button>
              <Button
                variant="default"
                className="bg-custom-primary text-primary-background"
                onClick={deleteCollectionTemplate}
              >
                Yes
              </Button>
            </div>
          }
        >
          <p className="font-primary-text">{`Are you sure you want to delete this collection template`}</p>
        </ShadcnDialog>
      )}
      <div className="space-y-6">
        {/* Template Statistics */}
        <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-4">
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card className="p-3">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Templates</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.collection_template_metrics?.total_templates ??
                    '-'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {collectionBuilderDashboardData?.collection_template_metrics?.active_templates ??
                    '-'}{' '}
                  active
                </p>
              </CardContent>
            </Card>
          )}

          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card className="p-3">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Mappings</CardTitle>
                <LinkIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.collection_template_metrics
                    ?.all_filtered_mapping_count ?? '-'}
                </div>
                <p className="text-xs text-muted-foreground">Across all templates</p>
              </CardContent>
            </Card>
          )}

          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card className="p-3">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Collection Sources</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.collection_template_metrics
                    ?.total_consent_source ?? '-'}
                </div>
                <p className="text-xs text-muted-foreground">Unique source types</p>
              </CardContent>
            </Card>
          )}
          {isLoadingCollectionBuilderDashboardData ? (
            <div className="flex items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <Card className="p-3">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Template Owners</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {collectionBuilderDashboardData?.collection_template_metrics?.unique_owners ??
                    '-'}
                </div>
                <p className="text-xs text-muted-foreground">Responsible stakeholders</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Enhanced Collection Templates Grid */}
        {collectionBuilderDetailedDashboardData?.length > 0 && (
          <div className="ml-2 flex items-center justify-start gap-6">
            <div className="flex gap-2 text-sm font-semibold">
              <span>
                <Circle className="inline size-4 fill-green-500 text-green-500" />
              </span>
              <span>Completed</span>
            </div>
            <div className="flex gap-2 text-sm font-semibold">
              <span>
                <Circle className="inline size-4 fill-black" />
              </span>
              <span>In Progress</span>
            </div>
            <div className="flex gap-2 text-sm font-semibold">
              <span>
                <Circle className="inline size-4 fill-white" />
              </span>
              <span>Not Started</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
          {isLoadingCollectionBuilderDetailedDashboardData ? (
            <div className="flex w-[100vw] items-center justify-center">
              <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
            </div>
          ) : (
            <>
              {collectionBuilderDetailedDashboardData?.map((template: any) => (
                <Card
                  key={template?.template_id}
                  className="border-l-primary/20 border-l-4 px-2 py-4 transition-all duration-300 hover:border-l-primary hover:shadow-lg"
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="mb-2 flex items-center gap-2 text-xl">
                          <FileText className="h-5 w-5 text-primary" />
                          {template?.template_name ?? '-'}
                        </CardTitle>
                        <Badge
                          variant={template.active_status === true ? 'default' : 'secondary'}
                          className={template.active_status === true ? 'text-white' : 'text-black'}
                        >
                          {template?.active_status ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <CardDescription className="text-sm leading-relaxed">
                      {/* {template.description} */}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Template Details Grid */}
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="space-y-1">
                        <span className="flex items-center gap-1 text-muted-foreground">
                          <Building className="h-3 w-3" />
                          Entity
                        </span>
                        <div className="font-medium text-foreground">
                          {template?.entity_name ?? '-'}
                        </div>
                      </div>

                      <div className="space-y-1">
                        <span className="flex items-center gap-1 text-muted-foreground">
                          <LinkIcon className="h-3 w-3" />
                          Mappings
                        </span>
                        <div className="font-medium text-foreground">4</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="space-y-1">
                        <span className="flex items-center gap-1 text-muted-foreground">
                          <Users className="h-3 w-3" />
                          Owner Name
                        </span>
                        <div className="font-medium text-foreground">
                          {template?.owner_name ?? '-'}
                        </div>
                      </div>
                      <div className="space-y-1">
                        <span className="flex items-center gap-1 text-muted-foreground">
                          <Users className="h-3 w-3" />
                          Owner Email
                        </span>
                        <div className="font-medium text-foreground">
                          {template?.owner_email ?? '-'}
                        </div>
                      </div>
                    </div>

                    {/* Subject Identity */}
                    <div className="space-y-2">
                      <span className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Key className="h-3 w-3" />
                        Subject Identity
                      </span>
                      <Badge variant="outline" className="w-fit">
                        {template?.pii_name ?? '-'}
                      </Badge>
                    </div>

                    {/* Sources */}
                    <div className="space-y-2">
                      <span className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Database className="h-3 w-3" />
                        Collected Data Sources
                      </span>
                      <div className="flex flex-wrap gap-1">
                        {template?.consent_sources?.length > 0 ? (
                          template?.consent_sources?.map((source: any) => (
                            <Badge key={source} variant="secondary" className="text-xs">
                              {source}
                            </Badge>
                          ))
                        ) : (
                          <>-</>
                        )}
                      </div>
                    </div>

                    {/* Template Metadata */}
                    <div className="border-t border-border/50 pt-3">
                      <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                        <div>
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Created: {convertDateToHumanView(template?.created_at) ?? '-'}
                          </span>
                        </div>
                        <div>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Modified: {convertDateToHumanView(template?.updated_at) ?? '-'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="grid grid-cols-2 gap-2">
                      <DropdownMenu onOpenChange={(open) => {}}>
                        <DropdownMenuTrigger>
                          <Button size="sm" variant="outline" className="h-9 w-full font-medium">
                            <Settings className="mr-1 h-3 w-3" />
                            Configure
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="font-primary-text">
                          <DropdownMenuItem
                            onClick={(event) =>
                              handleStatusChange(true, template?.template_id, event)
                            }
                          >
                            Activate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(event) =>
                              handleStatusChange(false, template?.template_id, event)
                            }
                          >
                            Inactivate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600 hover:text-red-800"
                            onClick={(event) => {
                              event.stopPropagation();
                              setDeletingCollectionTemplateDialogOpen(true);
                              setDeletingCollectionTemplateData({
                                entity_id: template?.entity_id,
                                record_ids: [template?.template_id],
                                component: 'collection_template',
                                customer_id: customer_id,
                              });
                            }}
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>

                      <Button size="sm" variant="outline" className="h-9 font-medium">
                        <Code className="mr-1 h-3 w-3" />
                        Get Code
                      </Button>
                    </div>

                    {/* Template Workflow Progress */}
                    <div className="space-y-5">
                      <span className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Workflow className="h-3 w-3" />
                        Configuration Progress
                      </span>
                      <div className="flex items-center justify-around">
                        <div className="flex flex-col items-center text-center">
                          <Circle
                            className={`h-6 w-6 ${template?.basic_step === 'completed' ? 'fill-green-500 text-green-500' : template?.basic_step === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                          />
                          <span className="mt-1 text-xs text-muted-foreground">
                            Basic & Mapping
                          </span>
                        </div>
                        <div className="flex flex-col items-center text-center">
                          <Circle
                            className={`h-6 w-6 ${template?.retention_and_source === 'completed' ? 'fill-green-500 text-green-500' : template?.retention_and_source === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                          />
                          <span className="mt-1 text-xs text-muted-foreground">
                            Retention & Source
                          </span>
                        </div>
                        <div className="flex flex-col items-center text-center">
                          <Circle
                            className={`h-6 w-6 ${template?.user_communication === 'completed' ? 'fill-green-500 text-green-500' : template?.user_communication === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                          />
                          <span className="mt-1 text-xs text-muted-foreground">
                            User Communication
                          </span>
                        </div>
                        <div className="flex flex-col items-center text-center">
                          <Circle
                            className={`h-6 w-6 ${template?.localization_setup === 'completed' ? 'fill-green-500 text-green-500' : template?.localization_setup === 'not_started' ? 'fill-white' : 'fill-black text-black'}`}
                          />
                          <span className="mt-1 text-xs text-muted-foreground">Localization</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default CollectionTemplatesTab;
