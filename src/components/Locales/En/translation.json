{"Common": {"CreateNew": "Create New", "ClearFilters": "Clear Filters", "Search": "Search", "RequiredField": "This field is required!", "Select": "Select", "Submit": "Submit", "Cancel": "Cancel", "Save": "Save", "Saving": "Saving...", "SaveAndContinue": "Save and continue", "Delete": "Delete", "Edit": "Edit", "Next": "Next", "Prev": "Previous", "Create": "Create", "Add": "Add", "AddNew": "Add New", "Import": "Import", "All": "All", "Submitting": "Submitting...", "Deleting": "Deleting...", "Adding": "Adding...", "Updating": "Updating...", "Reset": "Reset", "True": "True", "False": "False", "Low": "Low", "Medium": "Medium", "High": "High", "Apply": "Apply", "Write": "Write", "Action": "Action", "Name": "Name", "NoResult": " No results.", "Update": "Update", "No": "No", "YesProceed": "Yes, Proceed", "Copy": "Copy", "Copied": "<PERSON>pied", "BackToList": "Back to List"}, "ErrorPage": {"Page": "Page", "NotFound": "Not Found", "NotExist": "Unfortunately, the page you are looking for does not exist.", "GoBack": "Go Back", "ReportError": "Report Error"}, "Breadcrumbs": {"ControlRoom": "Control Room", "Policy Management": "Policy Management", "All Policies": "All Policies", "Privacy Notice": "Privacy Notice", "Audit Log": "<PERSON>t Log", "Data Subject Rights Management": "Data Subject Rights Management", "Pending Requests": "Pending Requests", "View Request": "View Request", "Approved Requests": "Approved Requests", "Reject In Progress Requests": "Reject in Progress Requests", "Rejected Requests": "Rejected Requests", "Completed Requests": "Completed Requests", "Profile": "Profile", "Change Password": "Change Password", "Blogs": "Blogs", "View Blog": "View Blog", "About GoTrust": "About GoTrust", "Company Structure": "Company Structure", "Group Detail": "Group Detail", "Customer Management": "Customer Management", "Role Management": "Role Management", "Role Details": "Role Details", "Add Role": "Add Role", "Edit Role": "Edit Role", "Edit Role Details": "Edit Role Details", "User Management": "User Management", "Home": "Home", "Insights into Processing Activities": "Insights into Processing Activities", "Task Overview": "Task Overview", "ROPA": "ROPA", "ROPA Review": "ROPA Review", "Basic Information": "Basic Information", "Add Control": "Add Control", "PII Inventory": "PII Inventory", "Unstructured Data Inventory": "Unstructured Data Inventory", "Data Catalogue": "Data Catalogue", "Structured": "Structured", "Unstructured": "Unstructured", "Services": "Services", "Ingestion": "Ingestion", "Data Catalogue Dashboard": "Data Catalogue Dashboard", "PII List": "PII List", "File Classification": "File Classification", "DSR Form Repository": "DSR Form Repository", "DSR Form Builder": "DSR Form Builder", "Create Form": "Create Form", "Form Review": "Form Review", "Add Question": "Add Question", "Schema Entity": "<PERSON><PERSON><PERSON>", "Table Entity": "Table Entity", "Column Entity": "Column Entity", "Profile Entity": "Profile Entity", "Template": "Template", "Assessment Management": "Assessment Management", "Dashboard": "Dashboard", "Templates": "Templates", "Vendor Risk Management": "Vendor Risk Management", "Vendor List": "Vendor List", "Details": "Details", "Internal Assessment": "Internal Assessment", "Vendor Assessment": "Vendor Assessment", "Mitigation": "Mitigation", "Cookie Consent Management": "<PERSON><PERSON>", "Cookie Consent Domain": "<PERSON>ie <PERSON>", "Cookie Configuration": "<PERSON><PERSON> Configuration", "Universal Consent Management": "Universal Consent Management", "Preference Center": "Preference Center", "Add Preference Center": "Add Preference Center", "Update Preference Center": "Update Preference Center", "Consent Collection": "Consent Collection", "Consent Upload": "Consent Upload", "Custom Parameters": "Custom Parameters", "Subject Consent Types": "Subject Consent Types", "Subject Consent Detail": "Subject Consent Detail", "Form": "Form", "Consent Collection Templates": "Consent Collection Templates", "Create Consent Collection Template": "Create Consent Collection Template", "Processing Category": "Processing Category", "Processing Purpose": "Processing Purpose", "Consent Purpose": "Consent <PERSON>", "PII Label": "PII Label", "Privacy Notice Details": "Privacy Notice Details", "View Privacy Notice": "View Privacy Notice", "Subject Consent List": "Subject Consent List", "Customization": "Customization", "Requests": "Requests", "View Requests": "View Requests", "Tasks": "Tasks", "View Tasks": "View Tasks", "Reject in Progress Requests": "Reject in Progress Requests", "Create Request": "Create Request", "Email Templates": "Email Templates", "Create Email Template": "Create <PERSON><PERSON>", "Retention Schedule": "Retention Schedule", "Workflow": "Workflow", "Add Workflow": "Add Workflow", "Edit Workflow": "Edit Workflow", "My Request": "My Request", "My Request View": "My Request View", "View Workflow": "View Workflow", "Support": "Support", "Create Ticket": "Create Ticket", "View Ticket": "View Ticket", "Edit Ticket": "Edit Ticket", "Finance": "Finance", "Universal Control Framework": "Universal Control Framework", "Improvements": "Improvements", "Risk Dashboard": "Risk Dashboard", "Compliance Dashboard": "Compliance Dashboard", "Privacy Ops": "Privacy Ops", "Document Repository": "Document Repository", "Assessment Repository": "Assessment Repository", "Processing Activities": "Processing Activities", "Regulations": "Regulations", "Risk Register": "Risk Register", "Duty": "Duty", "Action": "Action", "Improvement Actions": "Improvement Actions", "Breaches": "Breaches", "Breach Details": "Breach Details", "Subject Consent Manager": "Subject Consent Manager"}, "SideBar": {"GroupLabel": {"Account Setup": "Account <PERSON><PERSON>", "Theme": "Theme", "Data Mapping": "Data Mapping", "Policy Management": "Policy Management", "Data Subject Rights Management": "Data Subject Rights Management", "Assessment Management": "Assessment Management", "Universal Consent Management": "Universal Consent Management", "Universal Control Framework": "Universal Control Framework", "Cookie Consent Management": "<PERSON><PERSON>", "Vendor Risk Management": "Vendor Risk Management", "Awareness Program": "Awareness Program", "Blogs": "Blogs", "Support": "Support", "Finance/Commercials": "Finance/Commercials", "Workflow Automation": "Workflow Automation", "Data Discovery": "Data Discovery", "DPO Runbook": "DPO Runbook", "Data Breach Management": "Data Breach Management", "Configurations": "Configurations", "Policy & Notice Management": "Policy & Notice Management"}, "SideBarData": {"Profile Configuration": "Profile Configuration", "On-boarding Questionnaire": "On-boarding Questionnaire", "Company Structure": "Company Structure", "Access Management": "Access Management", "Role Management": "Role Management", "User Management": "User Management", "Vendor Management": "Vendor Management", "About GoTrust": "About GoTrust", "Customization": "Customization", "Dashboard": "Dashboard", "Task Overview": "Task Overview", "Content Profiles": "Content Profiles", "Data Catalogue": "Data Catalogue", "Structure": "Structure", "Unstructure": "Unstructure", "Data Catalogue V0": "Data Catalogue V0", "File Classification": "File Classification", "DSR Lab": "DSR Lab", "DSR Form Builder": "DSR Form Builder", "DSR Report": "DSR Report", "DSR Form Repository": "DSR Form Repository", "DSR Email Templates": "DSR Email Templates", "DSR Retention Schedule": "DSR Retention Schedule", "Workflow": "Workflow", "Impact Assessment": "Impact Assessment", "Privacy Impact Assessment": "Privacy Impact Assessment", "Privacy by Design Assessment": "Privacy by Design Assessment", "Legitimate Interests Assessment": "Legitimate Interests Assessment", "Transfer Impact Assessment": "Transfer Impact Assessment", "EU AI Assessment": "EU AI Assessment", "Assessment Lab": "Assessment Lab", "Assessment Templates": "Assessment Templates", "Subject Consent Types": "Subject Consent Types", "Subject Consent List": "Subject Consent List", "Privacy Notice": "Privacy Notice", "Source": "Source", "Consent Upload": "Consent Upload", "UCM Lab": "UCM Lab", "Processing Category": "Processing Category", "Processing Purpose": "Processing Purspose", "Consent Purpose": "Consent <PERSON>", "Consent POC": "Consent POC", "PII Label": "PII Label", "Consent Collection Builder": "Consent Collection Builder", "Preference Form": "Preference Form", "Cookie Consent Domain": "<PERSON>ie <PERSON>", "VRM Lab": "VRM Lab", "Vendor Assessment Templates": "Vendor Assessment Templates", "Course Management": "Course Management", "Registration": "Registration", "Enrollment": "Enrollment", "Project View": "Project View", "Blogs": "Blogs", "Billing & Invoice": "Billing & Invoice", "Workflow Automation": "Workflow Automation", "Data Discovery": "Data Discovery", "Risk Dashboard": "Risk Dashboard", "Compliance Dashboard": "Compliance Dashboard", "Regulations": "Regulations", "Risk Register": "Risk Register", "Activities": "Activities", "Duties": "Duties", "Actions": "Actions", "Improvements": "Improvements", "Repository": "Repository", "Document Repository": "Document Repository", "Assessment Repository": "Assessment Repository", "Record of Processing Activities Repository": "Record of Processing Activities Repository", "User Guide": "User Guide", "Breach List": "Breach List", "Subject Consent Manager": "Subject Consent Manager", "Theme Customization": "Theme Customization", "Data Vizualization": "Data Vizualization", "Data Insights": "Data Insights", "Data Flow Diagram": "Data Flow Diagram"}}, "Home": {"Account Setup": "Account <PERSON><PERSON>", "Theme": "Theme", "Data Mapping": "Data Mapping", "Universal Control Framework": "Universal Control Framework", "Policy & Notice Management": "Policy & Notice Management", "Data Subject Rights Management": "Data Subject Rights Management", "Assessment Management": "Assessment Management", "Universal Consent Management": "Universal Consent Management", "Cookie Consent Management": "<PERSON><PERSON>", "Vendor Risk Management": "Vendor Risk Management", "Awareness Program": "Awareness Program", "Blogs": "Blogs", "Support": "Support", "Finance/Commercials": "Finance/Commercials", "Workflow Automation": "Workflow Automation", "Data Discovery": "Data Discovery", "DPO Runbook": "DPO Runbook", "Data Breach Management": "Data Breach Management", "Data Retention": "Data Retention"}, "CompanyStructure": {"GroupDetails": {"Name": "Name", "Parent": "Parent", "NoOfUsers": "No. Of Users", "CreatedOn": "Created On", "UpdatedOn": "Updated On", "FirstName": "First Name", "LastName": "Last Name"}, "CompanyTree": {"BusinessUnit": "Business Unit", "DepartmentUnit": "Department Unit", "ProcessUnit": "Process Unit"}, "CompanyView": {"Name": "Name", "Added On": "Added On", "Last Updated": "Last Updated", "No. Of Users": "Number Of Users", "Actions": "Actions"}}, "RoleManagement": {"AddRole": {"AddRole": "Add Role", "Heading": "Role Details", "RoleName": "Role Name", "EnterRoleName": "Enter role name", "GivenAccess": "Accesses given to user"}, "RoleTable": {"RoleName": "Role Name", "CreatedBy": "Created By", "Totaluser": "Total Users", "CreatedOn": "Created On", "UpdatedOn": "Updated On", "Action": "Action"}, "ActiveStatus": {"Active": "Active", "Inactive": "Inactive", "Archived": "Archived"}, "ViewRole": {"Heading": "Role Details", "RoleName": "Role Name", "EnterRoleName": "Enter role name", "CreatedBy": "Created By", "Status": "Status", "CreatedDate": "Created Date", "UpdatedDate": "Updated Date", "GivenAccess": "Accesses given to user", "EditRole": "Edit Role"}, "EditRole": {"Heading": "Role Details", "RoleName": "Role Name", "Status": "Status", "GivenAccess": "Accesses given to user"}}, "UserManagement": {"AddUser": {"Heading": "User Details", "FirstName": "First Name", "EnterFirstName": "Enter first name", "LastName": "Last Name", "EnterLastName": "Enter last name", "Email": "Email", "EnterEmail": "Enter email", "Phone": "Phone", "EnterPhoneNumber": "Enter phone number", "SelectRole": "Select Role", "GroupAssigned": "Group Assigned", "AccessesUser": "Accesses given to user", "SeeAccesses": "Select Role to see Accesses"}, "EditUser": {"Heading": "User Details", "FirstName": "First Name", "EnterFirstName": "Enter first name", "LastName": "Last Name", "EnterLastName": "Enter last name", "Email": "Email", "EnterEmail": "Enter email", "Phone": "Phone", "EnterPhoneNumber": "Enter phone number", "SelectRole": "Select Role", "Status": "Status", "GroupAssigned": "Group Assigned", "AccessesUser": "Accesses given to user", "SeeAccesses": "Select Role to see Accesses"}, "ViewUser": {"Heading": "User Details", "FirstName": "First Name", "EnterFirstName": "Enter first name", "LastName": "Last Name", "EnterLastName": "Enter last name", "Email": "Email", "EnterEmail": "Enter email", "Phone": "Phone", "EnterPhoneNumber": "Enter phone number", "SelectRole": "Select Role", "Status": "Status", "CreatedDate": "Created Date", "UpdatedDate": "Updated Date", "GroupAssigned": "Group Assigned", "AccessesUser": "Accesses given to user", "SeeAccesses": "Select Role to see Accesses", "EditUser": "Edit User"}, "UserTable": {"AddUser": "Add User", "FirstName": "First Name", "LastName": "Last Name", "Email": "Email", "Phone": "Phone", "AddedDate": "Added Date", "UpdatedDate": "Updated Date", "Group": "Group", "RoleName": "Role Name", "Action": "Action"}}, "CustomerManagement": {"AddCustomer": {"Heading": "Customer Details", "Email": "Email", "EnterEmail": "Enter email", "Address": "Address", "EnterAddress": "Enter address", "AdminDetails": "Admin Details", "AdminName": "Admin name", "EnterAdminName": "Enter admin name", "Phone": "Phone", "EnterPhoneNumber": "Enter phone number", "AccessesUser": "Accesses given to user", "SeeAccesses": "Select Role to see Accesses"}}, "CookieConsentManagement": {"ServiceSavedSuccessfully": "Service saved successfully", "DomainDetails": {"BusinessUnit_Tooltip": "The business unit this domain belongs to. Helps map cookie consent banners to the right team or function within your organization.", "DomainGroup_Tooltip": "The name of the domain where cookie scanning will be performed.", "DomainURL_Tooltip": "The full website URL where the cookie scan will run. Example: https://www.example.com.", "Owner_Tooltip": "The person or department responsible for managing cookie consent for this domain. Will be notified of any changes or issues.", "OwnerEmail_Tooltip": "Email address where cookie consent-related alerts and updates will be sent. Can be a team email or individual address.", "CookiePolicyLink_Tooltip": "Direct link to the domain’s cookie policy page. This will be shown on the consent banner to inform users about data practices.", "Entity_Tooltip": "The data protection regulations that are applicable to this specific domain URL.", "ConsentFramework_Tooltip": "The data protection regulations applicable to this specific domain URL.", "BusinessUnit": "Business Unit", "CookiePolicyLink": "<PERSON>ie <PERSON> Link", "GroupDomainName": "Group Domain Name", "Heading": "Domain Details", "DomainGroup": "Domain Group", "DomainGroup_Placeholder": "Select", "DomainName": "Domain Name", "DomainName_Placeholder": "Enter Domain Name", "URL": "URL", "DomainURL": "Domain URL", "URL_Placeholder": "Enter URL", "Owner": "Owner", "Owner_Placeholder": "Enter Owner Name", "OwnerEmail": "Owner <PERSON><PERSON>", "OwnerEmail_Placeholder": "Enter Owner <PERSON><PERSON>", "Entity": "Entity", "Entity_Placeholder": "Select", "CreatedOn": "Created On", "ConsentFramework": "Consent Framework", "CreatedOn_Placeholder": "Pick a date", "CompliancePolicyLink": "Compliance Policy Link", "CompliancePolicyLink_Placeholder": "Write"}}, "VendorRiskManagement": {"CreateNewVendor": {"name": "<PERSON><PERSON><PERSON>", "EnterName": "Enter vendor name", "SelectName": "Select vendor name", "entity": "Entity", "SelectEntity": "Select entity name", "department": "Department", "SelectDepartment": "Select department name", "assigned_to": "Assigned To", "SelectAssignee": "Select assignee name", "reviewer": "Reviewer", "SelectReviewer": "Select reviewer name", "template": "Template", "SelectTemplate": "Select template name", "DefaultTemplate": "Vendor Risk Scoring Framework(default)"}, "Assessment": {"next": "Next", "previous": "Previous", "controls": "Controls", "collaborator": "Collaborator", "upload": "Upload Attachment"}, "Lab": {"upload": "Upload Template", "DropHere": "Drop here to attach or", "Upload": "upload", "FileType": "CSV or XLSX File | Max size: 10MB", "name": "Template Name", "PasteURL": "Paste URL"}, "ViewDetails": {"CollaboratorsProgressInVRM": " Category Progress in Internal Assessment"}}, "AboutUs": {"OurMission": "Our Mission", "KeyFeature": "Key Features", "WhyChoose": "Why Choose GoTrust?"}, "Ropa": {"PiiHandbook": {"PIIHandBook": "PII Handbook", "AddPii": "Add PII", "PiiName": "PII Type", "Description": "Description", "Tags": "Tags", "TypeTag": "Enter a tag", "Status": "Status", "Cancel": "Cancel", "Save": "Save", "AddingPii": "Adding PII...", "PiiType": "PII Type", "PiiCategory": "PII Category", "PiiValue": "PII Value", "PiiDescription": "PII Description", "PiiTags": "PII Tags", "PiiTypeTag": "Type a tag and press Enter", "PiiStatus": "Status", "PiiCancel": "Cancel", "PiiSave": "Save", "PiiNamePlaceholder": "Enter PII Name", "PiiDescriptionPlaceholder": "Enter Description", "PiiTagsPlaceholder": "Type a tag and press Enter", "PiiStatusPlaceholder": "Select Status", "PiiTypePlaceholder": "Select PII Type", "PiiCategoryPlaceholder": "Select PII Category", "PiiValuePlaceholder": "Enter PII Value", "PiiSaving": "Saving...", "PiiEditing": "Editing...", "PiiAdding": "Adding...", "PiiDeleting": "Deleting...", "PiiEditPii": "Edit PII", "PiiPiiType": "PII Type", "PiiPiiTypePlaceholder": "Select PII Type", "PiiPiiDescription": "PII Description", "PiiPiiDescriptionPlaceholder": "Enter Description", "PiiPiiTags": "PII Tags", "PiiPiiTagsPlaceholder": "Type a tag and press Enter", "PiiPiiStatus": "Status", "PiiPiiStatusPlaceholder": "Select Status", "PiiPiiUpdatedSuccessfully": "PII updated successfully", "PiiPiiDeletedSuccessfully": "PII deleted successfully", "PiiPiiUpdateFailed": "Failed to update PII", "PiiPiiDeleteFailed": "Failed to delete PII", "PiiPiiAddedSuccessfully": "PII added successfully", "PiiPiiAddFailed": "Failed to add PII", "PiiPiiAlreadyExists": "PII already exists", "PiiPiiDeletePii": "Delete PII", "PiiPiiDeletePiiDescription": "Are you sure you want to delete the PII ", "withTag": " with tag ", "PiiPiiDelete": "Delete", "PiiPiiCancel": "Cancel", "PiiPiiDeleting": "Deleting..."}, "Dashboard": {"ByDepartment": {"Heading": "Processing Activities by Department", "Count": "Total count"}, "ByOrganization": {"Heading": "Processing Activities by Organization Role", "Count": "Total count"}, "ByLawfullBasis": {"Heading": "Processing Activities by Law<PERSON> Basis", "y-axis": "Lawful Basis Applied", "x-axis": "Number of Processing Activities"}, "ByPersonalData": {"Heading": "Department wise Personal & Sensitive Personal Data", "y-axis": "Departments", "x-axis": "Number of Personal Data Element"}, "ThirdPartiesList": {"Heading": "List of Third Parties", "TableHeading": {"Vendor": "<PERSON><PERSON><PERSON>", "Services": "Services", "Department": "Department", "Location": "Location", "PersonalData": "Personal Data Involved"}}, "DataSystemList": {"Heading": "List of Data Systems/Applications", "Count": "Total count", "TableHeading": {"Purpose": "Purpose", "InHouse": "In-House/Third Party", "ThirdPartyName": "Name of Third Party", "Location": "Location"}}, "DPIA": {"Heading": "DPIA Requirement", "y-axis": "Number of High-Risk Processing Activities", "x-axis": "Department"}, "ROPAV3": {"ROPA Management": "ROPA Management", "ROPA Dashboard": "ROPA Dashboard", "ROPA Assessments": "ROPA Assessments", "Overview of all processing activity assessments": "Overview of all processing activity assessments", "Comprehensive Records of Processing Activities management and compliance tracking": "Comprehensive Records of Processing Activities management and compliance tracking", "ROPA Management Dashboard": "ROPA Management Dashboard", "Records of Processing Activities (Article 30 GDPR Compliance)": "Records of Processing Activities (Article 30 GDPR Compliance)", "Select Entity": "Select Entity", "Active assessments": "Active assessments", "Total ROPAs": "Total ROPAs", "Yet to Start": "Yet to Start", "In Progress": "In Progress", "Completed": "Completed", "Processing Activities by Organization Role": "Processing Activities by Organization Role", "Total count": "Total count", "Loading organization role data...": "Loading organization role data...", "Failed to load organization role data": "Failed to load organization role data", "Controller": "Controller", "Joint-Controller": "Joint-Controller", "Processor": "Processor", "Sub-Processor": "Sub-Processor", "ROPAs": "ROPAs", "Processing Activities by Department": "Processing Activities by Department", "Total processes": "Total processes", "Departments": "Departments", "Error loading department data": "Error loading department data", "processes": "processes", "ROPA Progress": "ROPA Progress", "Total": "Total", "No department data available": "No department data available", "List of Third Parties by Department": "List of Third Parties by Department", "Loading...": "Loading...", "Failed to load data.": "Failed to load data.", "Vendor": "<PERSON><PERSON><PERSON>", "Services": "Services", "Department": "Department", "Location": "Location", "Personal Data Involved": "Personal Data Involved", "No data available": "No data available", "No records found.": "No records found.", "Active Collaborators": "Active Collaborators", "Team members working on ROPA assessments": "Team members working on ROPA assessments", "Loading collaborators...": "Loading collaborators...", "Failed to fetch collaborator data": "Failed to fetch collaborator data", "Failed to load collaborator data": "Failed to load collaborator data", "No active collaborators found": "No active collaborators found", "ROPAs assigned": "ROPAs assigned", "completed": "completed", "Complete": "Complete", "Progress": "Progress", "more": "more", "total ROPAs": "total ROPAs", "Recent ROPA Activities": "Recent ROPA Activities", "Latest updates on processing activity assessments": "Latest updates on processing activity assessments", "View All": "View All", "Loading recent activities...": "Loading recent activities...", "No recent ROPA activities found": "No recent ROPA activities found", "Assignee": "Assignee", "Importing a new document will result in the loss of all previous ROPA performed. Are you sure you want to continue?": "Importing a new document will result in the loss of all previous ROPA performed. Are you sure you want to continue?", "Manage Records of Processing Activities assessments": "Manage Records of Processing Activities assessments", "Department Level": "Department Level", "Process Level": "Process Level"}}, "Activity": {"TableHeading": {"Department": "Department", "Process": "Process Unit", "RecurrenceDate": "Recurrence Date", "Version": "Version", "SPOC": "SPOC", "StartDate": "Start Date", "UpdatedDate": "Updated date", "AssignedTo": "Assigned To", "is_assigned": "Assigned To Me", "Reviewer": "Reviewer", "progress": "Progress", "Review": "Review", "Risks": "Risk", "Status": "Status", "Action": "Action", "Import": "Import", "ImportNewFile": "Import New File", "DownloadSampleFile": " Download Sample File", "Filter": "Filter", "ClearFilter": "Clear Filter", "Start": "Start", "Re-Start": "Re-Start", "View": "View", "Assign": "Assign", "ROPA Details": "ROPA Details", "Type": "Type", "Assignment": "Assignment"}}, "ViewDetails": {"Reviewer": "Reviewer", "AssignedTo": "Assigned To", "Department": "Department", "Entity": "Entity", "UpdatedDate": "Updated Date", "SPOC": "SPOC", "Review": "Review", "Start": "Start", "Assign": "Assign", "CollaboratorsProgressInRopa": " Category Progress in ROPA", "RopaDetails": "Ropa <PERSON>", "TentativeCompletionDate": "Tentative Completion Date", "RecurrenceDate": "Recurrence Date", "PickaDate": "Select a range of dates", "SelectDate": "Select Date", "Category": "Category", "Collaborators": "Collaborators", "ROPADetails": "ROPA Details"}, "DataCatalogue": {"DashBoard": {"TabList": {"Structured": "Structured", "Unstructured": "Unstructured"}, "Structured": {"ServiceOwner": "Service Owner", "DataManagement": "Data Management", "DataDetails": "Data Details", "TotalPIIs": "Total PIIs", "TotalPIICategories": "Total PII Categories", "DistinctPIIsDetected": "Distinct PIIs Detected", "DataLocations": {"DataLocations": "Data Locations", "DataLocation": "Data Location", "DataElements": "Data Elements", "Databases": "Databases", "Schemas": "<PERSON><PERSON><PERSON>", "Tables": "Tables", "DataSystems": "Data Systems"}, "SankeyGraph": {"DisplayAllData": "Displaying All Data Elements", "NoDataAvailable": "No Data Available"}, "DataSensitivityCard": {"Data Sensitivity": "Data Sensitivity", "NoDataAvailable": "No Data Available"}, "PIICategories": "PII Categories", "PIIDistributions": {"PII": "PII", "PIICount": "PII Count", "PIIDistribution": "PII Distribution", "Columns": "Columns", "Schemas": "<PERSON><PERSON><PERSON>", "Database": "DataBase", "DataSystems": "DataSystems", "Location": "Location"}}, "Unstructured": {"DataService": "Data Service", "DataLocations": "Data Locations", "DataManagement": "Data Management", "ScannedDocuments": "Scanned Documents", "ScannedVolume": "Scanned Volume", "ScannedFileFormat": "Scanned File Format", "DataDetails": "Data Details", "TotalPIICategories": "Total PII Categories", "DistinctPIIsDetected": "Distinct PIIs Detected", "TotalDetectedPIIs": "Total Detected PIIs", "SankeyGraph": {"DisplayAllElements": "Displaying All Data Elements", "NoDataAvailable": "No data available"}, "DocumentCards": {"Documents": "Documents", "DocumentName": "Document Name", "DocumentType": "Document Type", "PIIsDetected": "PIIs Detected", "DocumentSize": "Document Size", "Location": "Location", "DataService": "Data Service", "SubService": "Sub-service"}, "DataLocationCard": {"DataLocation": "Data Location", "DataServiceLoc": "Data Service Location", "Document": "Document", "DocumentType": "Document Type", "DataService": "Data Service"}, "DataSensitivityCard": {"DataSensitivity": "Data Sensitivity", "NoDataAvailable": "No Data Available"}, "FileFormats": "File Formats", "PIICard": {"PII": "PII", "PIICount": "PII Count", "Documents": "Documents", "Location": "Location", "DataService": "Data Service"}, "Services": "Services", "ID": "ID", "ServiceName": "Service Name", "ServiceType": "Service Type", "Status": "Status", "Running": "Running", "Done": "Done", "Failed": "Failed", "NA": "NA", "Action": "Action", "AddNewService": "Add New Service", "SelectIngestion": "Please select an Ingestion", "Microsoft365": "Microsoft 365", "Aws": "AWS", "GoogleWorkspace": "Google Workspace", "AzureDataLake": "Azure Data Lake"}, "PIIList": {"AddNew": "Add New", "PIICategory": "PII Category", "PIIValue": "PII Value", "PIIDescription": "PII Description", "Applicability": "Applicability", "Sensitivity": "Sensitivity", "Actions": "Actions", "EnterDescription": "Enter your description here"}}, "TotalColumns": "Total Columns", "TotalTables": "Total Tables", "TotalDatabases": "Total Databases", "DistinctPIIsCount": "Distinct PIIs Count", "ColumnName": "Column Name", "TableName": "Table Name", "ServiceName": "Service Name", "DataCategory": "Data Category", "PIIDetected": "PII Detected", "Sensitivity": "Sensitivity", "DataSystem": "Data System", "DataSystemOwner": "Data System Owner", "DataServiceCount": "Data Service Count", "ScannedDocumentCount": "Scanned Documents Count", "TotalVolume": "Total Volume", "FileFormatCount": "File Format Count", "DocumentName": "Document Name", "DocumentType": "Document Type", "PIIsDetected": "PIIs Detected", "Size": "Size", "Location": "Location", "DataService": "Data Service", "SubService": "Sub-service", "FileLocation": "File Location", "DropHere": " Drop here to attach or", "Upload": "Upload", "SelectedFiles": "Selected Files", "MaxSize": "Max Size: 10MB", "AcceptedFormats": "Accepted formats", "Datatype": "Datatype", "Tags": "Tags", "PIIType": "PII Type", "Version": "Version", "TagDetails": "Tag Details", "DataSystemCount": "Data System Count", "DetectedData": "Detected Data", "ConfidenceScore": "Confidence Score"}, "ChartData": {"Controller": "Controller", "Joint-Controller": "Joint-Controller", "Processor": "Processor", "Sub-Processor": "Sub-Processor", "Finance": "Finance", "AI": "AI", "Cloud": "Cloud", "Frontend": "Frontend", "ML": "ML", "Consultancy": "Consultancy", "Backend": "Backend", "HR": "HR", "IT": "IT ", "DevOps": "DevOps", "Sales": "Sales", "Consent": "Consent", "PII": "PII", "SPI": "SPI", "Performance of a contract": "Performance of a contract", "A legitimate interest": "A legitimate interest", "A vital interest": "A vital interest", "A legal requirement": "A legal requirement"}}, "Policy": {"Dashboard": {"Total": "Total Policies", "InUse": "Total Policies in Use", "Expiring": "Total Policies Expiring in 45 Days", "Policies": "Policies", "NoOfPolicies": "Number of Policies", "WorkflowHeading": "Policies by the workflow stage", "Entities": "Entities", "EntitiesHeading": "Policies by Entities", "DepartmentsHeading": "Policies by Departments"}, "AllPolicies": {"PolicyName": "Policy Name", "PolicyCategory": "Policy Category", "Entity": "Entity", "Recurrence": "Recurrence", "RenewalDate": "Renewal Date", "WorkflowStage": "Workflow Stage", "Department": "Department", "Action": "Action", "CreationofPolicy": "Creation of Policy", "ReviewofPolicy": "Review of Policy", "ApprovalofPolicy": "Approval of Policy", "PolicyinUse": "Policy in Use"}, "NewPolicyModal": {"Heading": "Create New Policy", "PolicyName": "Enter Policy Name", "PolicyDescription": "Policy Description", "PolicyCategory": "Policy Category", "EntityName": "Entity Name", "Language": "Language", "PolicyAuthor": "Policy Author", "PolicyReviewer": "Policy Reviewer", "PolicyApprover": "Policy Approver", "EffectiveDate": "Effective Date", "RecurrencePeriod": "Recurrence Period", "Department": "Department", "VersionNumber": "Version Number", "PolicyID": "Policy ID", "RelevantStandard/Law": "Relevant Standard/Law", "Create": "CREATE"}, "PolicyRequirement": {"Heading": "Policy Requirement", "Reviewer": "Reviewer", "Approver": "Approver", "Department": "Department", "Entity": "Entity", "ReviewDate": "Review Date", "Recurrence": "Recurrence", "Collaborator": "Collaborator"}, "PolicyAttachment": {"Heading": "Attachment", "CreateWithAI": "Create Policy with AI", "UpdateWithAI": "Update Policy with AI", "UploadedAttachment": "Uploaded Attachment"}, "AddAttachment": {"Heading": "Create New Version", "DropHere": "Drop here to attach or", "Upload": "upload", "FileType": "PDF or Word File | Max size: 5MB", "URL": "URL", "PasteURL": "Paste URL"}}, "AssessmentManagement": {"Dashboard": {"TotalAssessments": "Total number of Assessments", "ByReadiness": "Distribution by Readiness", "ByRegulation": "By Regulation Type", "ByOwners": "Assessment by Owners", "RecentAssessments": "Recent Assessments", "ViewAll": "View All", "AssessmentName": "Assessment Name", "Owner": "Owner", "Entity": "Entity", "Department": "Department", "ProcessUnit": "Process Unit", "StartDate": "Start Date", "UpdatedDate": "Updated Date", "Reviewer": "Reviewer", "AssignedTo": "Assigned to", "Risks": "Risks", "Progress": "Progress", "Status": "Status"}, "ViewDetails": {"CollaboratorsProgressInRopa": " Category Progress in Assessment"}}, "DSR": {"Dashboard": {"Total": "Total Number of Requests", "Request": "Request", "RequestStats": "Request Stats", "Approved Requests": "Approved Requests", "Pending Requests": "Pending Requests", "Reject in Progress": "Reject in Progress", "Rejected Requests": "Rejected Requests", "Completed Requests": "Completed Requests", "Extended": "Extended Requests", "RequestsByRights": "Requests by Rights", "TotalCount": "Total Count ", "RequestStatistics": "Request Statistics", "MoreThan": "More than", "newRequests": "new requests", "RecentRequests": "Recent Requests", "Monthly": "Monthly", "Annually": "Annually", "NumberOfApprovedRequest": "Number of Approved Request", "RequestsByStages": "Requests by Stages", "Verify": "Verify", "Stages": "Stages", "days": "days", "NoData": "No Data", "ApproachingDeadline": "Approaching Deadline", "Acknowledgement": "Acknowledgement", "Count": "Count", "RequestByResidency": "Request by Residency", "RequestStatusPerOwner": "Request Status Per Owner", "Completed": "Completed", "Pending": "Pending Requests", "Last7days": "Last 7 days", "Last14days": "Last 14 days", "Last30days": "Last 30 days", "Last60days": "Last 60 days", "All": "All", "RequestTypes": "Request Types"}, "TaskOverView": {"CreateRequest": "Create Request", "CreateForm": "Create Form", "DueDays": "Days until task is due", "Automation Workflow": "Automation Workflow", "UpdateTask": "Update Task", "Approve": "Approve", "Reject": "Reject", "DataSubjectRequestDetailsID": "Data Subject Request Details ID", "RequestID": "Request ID", "NoResult": "No result", "Assign": "Assign", "View": "View", "EmailVerified": "<PERSON><PERSON>", "Region": "Region", "RequestDate": "Request Date", "RequestedBy": "Requested By", "RejectRequest": "Are you sure you want to reject this request?", "ConfirmApprove": "Are you sure you want to approve this request?", "Attachments": "Attachments", "NoAttachmentsAvailable": "No attachments available", "PostalCode": "PostalCode", "Location": "Location", "Email": "Email", "FirstName": "First Name", "LastName": "Last Name", "Phone": "Phone", "UserDetails": "User Details", "RequestDescription": "Request Description", "Status": "Status", "RelationshipWithUs": "Relationship With Us", "RequestType": "Request Type", "CreatedOn": "Created On", "RequestInformation": "Request Information", "AddNote": "Add Note", "Approved": "Approved", "COMPLETED": "COMPLETED", "APPROVED": "APPROVED", "Rejected": "Rejected", "UpdateNote": "Update Note", "RejectionInProgress": "Rejection in Progress", "Extended": "Extended", "Confirm": "Confirm", "ConfirmExtend": "Are you sure you want to extend the deadline?", "Approver": "Approver", "Deadline": "Deadline", "BusinessUnit": "Business Unit", "WebForm": "Web Form", "DSAR": "DSAR Form(V12)", "Form Preview": "Form Preview", "PreferredLanguage": "Preferred Language", "DSRID": "DSR ID", "SubjectType": "Subject Type", "FullName": "Full Name", "StepsStatus": "Steps Status", "Assigned": "Assigned", "WantToApprove": "Are you sure you want to approve?", "WantToReject": "Are you sure you want to reject?", "ReasonOfRejection": "Reason for Rejection", "Action": "Action", "UploadCompletionDocuments": "Upload Completion Documents", "ClickToUploadDocuments": "Click to upload documents", "FormPreview": "Form Preview", "TasksTitle": "Tasks Title", "EnterTaskTitle": "Enter Task Title", "NotStarted": "Not Started", "InProgress": "In Progress", "RejectTask": "Reject Task", "Steps": "Steps", "Attachment": "Attachment", "DownloadAuditLog": "Download", "Automate": "Automate"}, "EmailTemplate": {"ConfirmDelete": "Confirm Delete", "CreateTemplate": "Create Template", "ConfirmDeleteQuestion": "Are you sure you want to delete this template?", "CancelBtn": "Cancel", "DeleteBtn": "Delete", "EmailTemplate": "<PERSON>ail Te<PERSON>late", "Modified": "Modified", "CreatedAt": "Created At", "Email": "Email", "Name": "Name", "Action": "Action", "Preview": "Preview", "TemplateName": "Subject / Template Name", "CreateEmailTemplate": "Create <PERSON><PERSON>", "EmailTemplatePreview": "Email Template Preview"}, "FormBuilder": {"FormBuilder": "Form Builder", "FormName": "Form Name", "Regulations": "Regulations", "URLNotAvailableForDraftForms": "URL Not Available For Draft Forms", "VerificationMethod": "Verification Method", "ConfirmDeleteForm": "Are you sure you want to delete this", "Form": "form", "ActionVersion": "Action Version", "URL": "URL", "Action": "Action", "Entity": "Entity", "LastUpdated": "Last Updated", "Email": "Email", "Published": "Published", "UploadingLogo": "Uploading logo...", "LogoUploadedSuccessfully": "Logo uploaded successfully", "FailedToUploadLogo": "Failed to upload logo", "SaveFormBeforeCreatingRules": "Save form first to create rules", "FillAllRuleFields": "Please fill all rule fields", "SelectValidFieldsForRule": "Please select valid fields for rule", "ViewCodeSnippets": "View Code Snippets", "APICode": "API Code", "CopyAPIEndPoint": "Copy API End-Point", "ViewForm": "View Form", "ViewFormDescription": "View form details and integration code snippets", "FormDetails": "Form Details", "BusinessUnit": "Business Unit", "Status": "Status", "Draft": "Draft", "FormID": "Form ID", "CodeSnippets": "Code Snippets", "CodeSnippetsDescription": "Use the following code snippets to integrate this DSR form into your mobile application", "MobileSDKCode": "Mobile SDK Code", "WebLink": "Web URL link", "FormPreview": "Form Preview", "CodeSnippet": "Code Snippet", "RulesAppliedSuccessfully": "Rules applied successfully", "RuleRemovedSuccessfully": "Rule removed successfully", "OptionRemovedSuccessfully": "Option removed successfully!", "FailedToRemoveOption": "Failed to remove option", "CustomerIDAndFormIDRequired": "Customer ID And Form ID Required", "FailedToSaveFormContent": "Failed to save form content", "URLCopiedToClipboard": "URL Copied To Clipboard", "PublicURLGenerated": "Public URL Generated", "FailedToGeneratePublicURL": "Failed To Generate Public URL", "FormSubmissionFailed": "Form submission failed", "TranslateForm": "Translate Form", "DoYouWantToTranslateThisForm": "Do you want to add translations in this form?", "NoPublishNow": "No, publish now", "YesTranslateForm": "Yes, translate form", "SelectLanguage": "Select Language", "ChooseLanguage": "Choose a language", "FormURL": "Form URL", "Cancel": "Cancel", "Translating": "Translating...", "StartTranslation": "Start Translation", "FormPublishedSuccessfully": "Form published successfully"}, "WorkFlow": {"WorkFlow": "WorkFlow", "EnterFlowType": "Enter Flow Type", "AddWorkflow": "Add Workflow", "TaskTitle": "Task Title", "Department": "Department", "AddTask": "Add Task", "UpdateTask": "Update Task", "StartDate": "Start Date", "DueDate": "Due Date", "AddAssignee": "Add Assignee", "GuidanceText": "Guidance Text", "AddFiles": "Add Files", "TaskChecklist": "Task Checklist", "UploadedDocuments": "UploadedDocuments", "ConfirmDeleteFile": "Are you sure you want to delete this file?", "DeleteFile": "Delete File", "Yes": "Yes", "SelectEntity": "SelectEntity", "SelectRegulations": "Select Regulations"}, "AssigneeModal": {"Assignee": "Assignee", "FlowType": "Flow Type", "DSRForm": "Data Subject Request Form", "PublishForm": "Publish Form"}}, "Cookies": {"DownloadReport": "Download Report", "View": "View Banner", "Interaction Count": "Interaction Count", "ScanFinished": "Scan Finished", "IsThisTheDefaultBanner": "Is this the default banner?", "IfThisIsTheDefaultBanner": "If this is the default banner, it will be shown to all users who visit the domain when region is not specified.", "DefaultBanner": "De<PERSON><PERSON>", "PolicyAddedSuccessfully": "Policy added successfully", "PolicyUpdatedSuccessfully": "Policy updated successfully", "FailedToAddPolicy": "Failed to add policy", "FailedToUpdatePolicy": "Failed to update policy", "No Interaction Count": "No Interaction Count", "Last Interaction": "Last Interaction", "Cookie Name": "<PERSON><PERSON>", "Cookie Value": "<PERSON>ie <PERSON>", "Cookie Path": "<PERSON><PERSON>", "Total Users": "Total Users", "Consent Count": "Consent Count", "Granted Count": "Granted Count", "Declined Count": "Declined Count", "SubjectIdentity": "Subject Identity", "AllCategories": "All Categories", "GeoLocation": "Geolocation", "CookieCategory": "Cookie Category", "Consent Status": "Consent Status", "SelectCategory": "Select Category", "Consent Date": "Consent Date", "DomainURL": "Domain URL", "Domain/Subdomain": "Domain/Subdomain", "Domain Group": "Domain Group", "Last Scanned": "Last Scanned", "Scan Frequency": "<PERSON><PERSON>", "Next Scan": "Next <PERSON>an", "Total Cookies": "Total Cookies", "Source": "Source", "NoResults": "No results.", "Cookie Consent Domain": "<PERSON>ie <PERSON>", "Banner Published": "Banner Published", "Consent Policy": "Consent Policy", "DeleteDomain": "Delete", "Owner": "Owner", "NoData": "No Data", "Cookie Configuration": "<PERSON><PERSON> Configuration", "All Domains": "All Domains", "Cookie Policy": "<PERSON><PERSON>", "Basic Information": "Basic Information", "Website Scan": "Website Scan", "Categorize Cookie": "Categorize <PERSON>", "Customize Banner": "Customize Banner", "Language Support": "Language Support", "Consent Code": "Consent Code", "AutoScan": "Auto Scan", "ScanDescription": "Scan your website to get a detailed cookie report.", "ScanNow": "Scan Now", "Scanning": "Scanning...", "ScanStarted": "<PERSON><PERSON> Started Successfully", "ScanFailed": "Scan Failed", "SelectFrequency": "Select Frequency", "Weekly": "Weekly", "Monthly": "Monthly", "Yearly": "Yearly", "Daily": "Daily", "Category": "Category", "Service": "Service", "Cookie": "<PERSON><PERSON>", "AddCategory": "Add Category", "AddServices": "Add Services", "AddCookies": "Add Cookies", "Consent": "Consent", "Details": "Details", "About": "About", "ThisWebsiteUsesCookies": "This website uses cookies", "BannerDescription": "We use cookies to personalize content and ads, to provide social media features and to analyze our traffic. We also share information about your use of our site with our social media, advertising and analytics partners who may combine it with other information that you've provided to them or that they've collected from your use of their services.", "OnlyNecessary": "Only necessary", "AllowSelection": "Allow Selection", "AllowAll": "Allow all", "AboutSectionContent": "Cookies are small text files that can be used by websites to make a user experience more efficient. The law states that we can store cookies on your device if they are strictly necessary for the operation of this site. For all other types of cookies we need your permission. This site uses different types of cookies. Some cookies are placed by third-party services that appear on our pages. You can at any time change or withdraw your consent from the Cookie Declaration on our website. Learn more about who we are, how you can contact us, and how we process personal data in our Privacy Policy. Please state your consent ID and date when you contact us regarding your consent.", "DefaultBannerError": "There must be exactly one default banner. Please adjust your selection.", "ErrorOccurred": "An unexpected error occurred.", "ChooseRegulation": "Choose regulation", "CreateOrUseExisting": "Do you want to create a new", "Existing": "Existing", "New": "New", "SelectOrCreatePolicy": "Please select an existing cookie policy or choose to create a new one.", "SelectVersion": "Select a version", "SelectCookiePolicy": "Select a cookie policy", "Version": "Version", "TranslatedLanguages": "Translated Languages", "SavingTranslation": "Saving translation...", "TranslationSaved": "Translation saved successfully", "SaveTranslation": "Save Translation", "UnableToTranslate": "Unable to translate data", "UnableToFetchData": "Unable to fetch data", "TestingCode": "Testing Code", "Copied": "<PERSON>pied", "UseTestCodeInStaging": "Use this Following test code in your staging website before deploying it to your production website.", "AddCodeToHead": "Add the following code at the beginning of the <head> tag:", "DeployedCode": "Deployed Code", "WhenShouldICopy": "When should I copy this?", "CookiePolicyURL": "<PERSON>ie Policy URL", "DeviceInfo": "Device Info", "IntegrateCookiePolicy": "To integrate the cookie policy on your website, copy this URL and paste it into the footer section of your website."}, "DataBreach": {"Dashboard": {"TotalBreaches": "Total Breaches", "OpenBreaches": "Open Breaches", "ClosedBreaches": "Closed Breaches"}}, "CookiePolicy": {"CookiePolicyName": "<PERSON>ie Policy Name", "Version": "Version", "UpdatedOn": "Updated On", "Action": "Action", "CopiedToClipboard": "URL copied to clipboard!", "Search": "Search", "Create": "Create", "Cookie Policy": "<PERSON><PERSON>", "View Policy": "View Policy", "Edit Policy": "Edit Policy", "Create Policy": "Create Policy", "SelectEntity": "Select entity"}, "CookieCenter": {"Sections": "Sections", "NoSectionsAvailable": "No sections available", "NewSectionTitle": "New Section Title", "AddSection": "Add Section", "SelectSectionFromList": "Select a section from the list to edit its content", "NoContentYet": "No content yet. Click the edit button to add content.", "SaveChanges": "Save Changes", "UpdateChanges": "Update Changes", "Save": "Save", "ErrorSelectEntity": "Please select an entity to proceed.", "ErrorFillTitle": "Please fill the title.", "ErrorAddSection": "Please add at least one section.", "UpdatingPolicy": "Updating policy...", "AddingPolicy": "Adding policy...", "DoYouWantNewVersion": "Do you want to create a new version of this policy?", "No": "No", "Yes": "Yes", "DiscardChanges": "Discard Changes"}, "CookiePolicyEditor": {"EnterCookiePolicyTitle": "Enter cookie policy title", "SelectEntity": "Select entity"}, "ConsentCode": {"TestingCode": "Testing Code", "TestCodeDescription": "This test code is designed to be used on your staging website before deploying it to your production website.", "TestCodeAddCode": "Add the following code at the beginning of the <head> tag:", "DeployedCode": "Deployed Code", "Copied": "<PERSON>pied", "WhenShouldICopyThis": "When should I copy this?", "CookiePolicyURL": "<PERSON>ie Policy URL", "IntegrateCookiePolicy": "To integrate the cookie policy on your website, copy this URL and paste it into the footer section of your website."}, "ucm": {"PII Label": "PII Label", "Consent Purpose": "Consent <PERSON>", "Processing Purpose": "Processing Purpose", "Processing Category": "Processing Category", "Dashboard": {"ConsentStatus": "Consent Status", "PurposeConsentStatus": "<PERSON><PERSON><PERSON>sent<PERSON>", "ConsentByResidency": "Consent By Residency", "TotalUsers": "Total Users", "TotalGrantedConsent": "Total Granted Consent", "TotalDeclinedConsent": "Total Declined Consent", "WithdrawnConsent": "Withdrawn Consent"}, "PrivacyNotice": {"PrivacyNoticeName": "Privacy Notice Name", "Version": "Version", "UpdatedOn": "Updated On", "Action": "Action", "Create": "Create", "SaveDetails": "Save Details", "EnterPrivacyNoticeTitle": "Enter Privacy Notice Title", "AddSection": "Add Section", "NoSectionsAvailable": "No sections available", "AddContent": "Add Content", "SelectToViewContent": "Select a section to view its content", "NewContent": "New Content", "NewSectionTitle": "New Section Title", "Sections": "Sections"}, "ProcessingCategory": {"ProcessingCategoryName": "Processing Category Name", "Description": "Description", "Action": "Action", "UpdateCategory": "Update Category", "Name": "Name", "AddCategory": "Add Category", "ProcessingCategory": "Processing Category", "AddProcessingCategory": "Add Processing Category", "ProcessingPurpose": "Processing Purpose", "DeleteCategory": "Delete Processing Category", "SureToDelete": "Are you sure you want to delete this processing category?", "No": "No", "YesProceed": "Yes, Proceed", "EnterCategoryName": "Enter Category Name", "EnterDescription": "Enter Category Description"}, "ProcessingPurpose": {"AddProcessingPurpose": "Add Processing Purpose", "ProcessingPurpose": "Processing Purpose", "Description": "Description", "ProcessingCategory": "Processing Category", "Action": "Action", "UpdateProcessingPurpose": "Update Processing Purpose", "Name": "Name", "DeleteProcessingPurpose": "Delete Processing Purpose", "SureToDelete": "Are you sure you want to delete this processing purpose?", "EnterPurposeName": "Enter Purpose Name", "EnterDescription": "Enter Purpose Description", "EnterCategoryName": "Enter Category Name", "EnterCategoryDescription": "Enter Category Description"}, "ConsentPurpose": {"AddConsentPurpose": "Add Consent P<PERSON>pose", "ConsentPurpose": "Consent <PERSON>", "EnterConsentName": "Enter Consent Name", "EnterDescription": "Enter Consent Description", "Description": "Description", "ProcessingPurpose": "Processing Purpose", "Action": "Action", "UpdateConsentPurpose": "Update Consent Purpose", "Name": "Name", "EnterCategoryDescription": "Enter Category Description", "EnterCategoryName": "Enter Category Name", "DeleteConsent": "Delete Consent Purpose", "SureToDelete": "Are you sure you want to delete this consent purpose?", "RetentionType": "Retention Type", "RetentionPeriod": "Retention Period", "ExpiryPeriod": "Expiry Period"}, "PII": {"AddPII": "Add PII", "PIILabel": "PII Label", "EnterPIIName": "Enter PII Label Name", "EnterDescription": "Enter PII Label Description", "Description": "Description", "UniversalIdentifier": "Universal Identifier", "Action": "Action", "DeletePIILabel": "Delete PII Label", "SureToDelete": "Are you sure you want to delete this PII Label?", "UpdatePIILabel": "Update PII Label", "Name": "Name", "EnterPIILabelName": "Enter PII Label Name", "EnterPIIDescription": "Enter pii label description", "Yes": "Yes", "No": "No"}, "CollectionBuilder": {"TemplateName": "Template Name", "DataIdentifierType": "Data Identifier Type", "EntityName": "Entity Name", "Owner": "Owner", "CreatedDate": "Created Date", "ModifiedDate": "Modified Date", "FormURL": "Consent Form URL", "CenterURL": "Preference Center URL", "Status": "Status", "Action": "Action", "CreateTemplate": "Create Consent Collection Template"}, "PreferenceCenter": {"AddPreferenceCenter": "Add Preferece Center", "PreferenceCenterName": "Preference Center Name", "DateandTime": "Preferene Center Date & Time", "URL": "URL", "Owner": "Owner", "Status": "Status", "Actions": "Actions", "BasicInfo": "Basic Information", "Customize": "Customize", "Code": "Code", "TemplateName": "Template Name", "Description": "Description", "SubjectIdentityType": "Subject Identity Type", "Template": "Template", "OwnerEmail": "Owner <PERSON><PERSON>", "OwnerName": "Owner Name", "PrivacyPolicyLink": "Privacy Policy Link"}, "SubjectConsentManager": {"Templates": "Templates", "Lists": "Lists", "ConsentDistributionChartbyTemplate": "Consent Distribution by Collection Template Name", "ConsentSourceDistribution": "Consent Source Distribution", "CollectionTemplateName": "Collection Template Name", "TotalUserConsents": "Total User Consents", "Status": "Status", "Action": "Action", "WebForm": "Web Form", "Form": "Form", "WebPreferenceCenter": "Web Preference Center", "Manually": "Manually", "PrefereceCenter": "Preference Center", "API": "API", "SubjectIdentity": "Subject Identity", "Source": "Source", "GeoLocation": "Geolocation", "ConsentedAt": "Consented At", "PIILabelName": "PII Label Name", "TotalConsents": "Total Consents", "WebConsents": "Web Consents", "MobileConsents": "Mobile Consents", "ApiConsents": "<PERSON><PERSON> Consents", "ConsentStatusTypes": "Consent Status Types", "Granted": "Granted", "Declined": "Declined", "Withdrawn": "Withdrawn", "ConsentDistributionByProcessing": "Consent Distribution by Processing Purpose", "SubjectPreferenceList": "Subject Preference List", "UserLogs": "User Logs", "ConsentName": "Consent Name", "Frequency": "Frequency", "ConsentExpiration": "Consent Expiration", "ConsentStatus": "Consent Status", "Filter": "Filter", "UserDetails": "User Details", "ConsentFlowVisualization": "Consent Flow Visualization", "NoDataAvailable": "No Data Available", "LoadingData": "Loading Data...", "ListOfTemplateTable": "List of Template Table"}}, "DPO": {"Dashboard": {"Risk": {"EntityRisks": "Entity Risks", "DataMappingRisks": "Data Mapping Risks", "VendorRisks": "<PERSON><PERSON><PERSON>s", "AssessmentRisks": "Assessment Risks", "RiskByCategory": "Risk By Category", "RiskByStages": "Risk By Stages", "MonetaryRisk": "Monetary Risk", "RiskByImpactProbability": "Risk by Impact and Probability", "RegulatoryComplianceBreakdown": "Regulatory Compliance Breakdown", "ControlByFramework": "Control by FrameWork", "RecentRisks": "Recent Risks", "RiskID": "Risk ID", "RiskTitle": "Risk Title", "RiskCategory": "Risk Category", "SourceOfRisk": "Source of Risk", "DateIdentified": "Date Identified", "RiskDescription": "Risk Description", "Identification": "Identification", "Evaluation": "Evaluation", "Mitigation": "Mitigation", "Closure": "Closure", "Compliance": "Compliance", "DataBreach": "Data Breach", "Legal": "Legal", "Likelihood": "Likelihood", "Severity": "Severity", "GDPR": "GDPR", "CCPA": "CCPA", "NIST": "NIST", "DPDPA": "DPDPA", "UAEPDPL": "UAE PDPL", "UAE": "UAE", "DPD": "DPD", "EUG": "EUG", "Controls": "Controls", "Completed": "Completed", "Pending": "Pending", "Score": "Score"}, "Compliance": {"ComplianceScoreByCategories": "Compliance Score by Categories", "OverAllScore": "Overall Score", "YourScore": "Your Compliance Score", "DataProtection": "Data Protection", "OrganizationControls": "Organization Controls", "TechnologyControls": "Technology Controls", "SystemSecurity": "System Security", "RegulatoryReporting": "Regulatory Reporting", "Policies": "Policies", "KeyImprovementActions": "Key Improvement Actions", "ImprovementAction": "Improvement Actions", "Impact": "Impact", "Status": "Status", "Group": "Group", "ActionType": "Action Type", "Completed": "Completed", "NotCompleted": "Not Completed", "OutOfScope": "Out of Scope", "ViewImprovementActions": "View Improvement Actions", "ViewAll": "View All", "PointAchieved": "Point Achieved", "ComplianceScore": "Compliance score measures your progress towards completing recommended Action that helpreduce risks around data protection and regulatory standards.", "DataProtectionDescription": "Enable and configure encryption, control access to information, and prevent data leakage and ex-filtration.", "OrganizationControlsDescription": "Define security policies, responsibilities, and frameworks, ensuring information assets are protected.", "TechnologyControlsDescription": "Employ automated defenses, system hardening, and data integrity measures to enforce security policies.", "SystemSecurityDescription": "Evaluates the security measures implemented in IT systems.", "RegulatoryReportingDescription": "Ensure compliance with data protection regulations and reporting requirements.", "PoliciesDescription": "Assesses the adequacy and currency of privacy policies."}}, "ControlHandbook": {"AddCategory": "Add Category", "ControlCategory": "Control Category", "AddRegulation": "Add Regulation", "CreatedOn": "Created On", "UpdatedOn": "Updated On", "Actions": "Actions", "ControlNo": "Control Number", "ControlDescription": "Control Description", "SummaryOfInScopeRegulations": "Summary of In-scope Regulations", "LoadingRegulations": "Loading Regulations", "data_submitted_successfully": "Data submitted successfully", "please_fill_all_fields": "Please fill all the fields", "please_add_business_requirement": "Please add business requirement", "category_name_required": "Category Name is required", "control_number_required": "Control Number is required", "category_number_required": "Category Number is required", "control_description_required": "Control Description is required", "AddControlCategory": {"CategoryName": "Category Name", "ControlDescription": "Control Description", "ControlNo": "Control Number", "CategoryNo": "Category Number", "EnterCategoryName": "Enter Category Name", "EnterControlDescription": "Enter Control Description", "EnterControlNo": "Enter Control Number", "EnterCategoryNo": "Enter Category Number", "Reference": "Reference ID", "EnterReference": "Enter Reference ID", "BusinessRequirement": "Business Requirement", "EnterBusinessRequirement": "Enter Business Requirements", "EnterCommaSeparatedValues": "Enter comma-separated values", "SaveBusinessRequirement": "Save Business Requirement"}, "AddRegulations": {"AddRegulation": "Add New Regulation", "FillInDetailsToAddRegulation": "Fill in the details to add a new regulation.", "Geography": "Geography", "MappingColumnHeader": "Mapping Column <PERSON>", "Source": "Source", "AuthoritativeSource": "Authoritative Source", "Version": "Version", "URL": "URL", "Available": "Available", "GroupIDs": "Group IDs", "ISRegulationAvailable": "Is this regulation currently available?", "SelectAssignee": "Select Assignee", "RegulationAddedSuccessfully": "Regulation Added Successfully", "FailedToAddRegulation": "Failed to add regulation"}}, "Regulation": {"RegulationName": "Regulation", "CreatedOn": "Created On", "UpdatedOn": "Updated On", "ComplianceStatus": "Compliance Status", "ViewDetails": "View Details", "SelectEntity": "Select Entity", "importSign": "import sign", "RepositoryDocumentUpload": "Privacy-Ops repository document upload", "AddControlDialog": {"AddControl": "Add Control", "ControlNumber": "Control Number", "ControlDescription": "Control Description", "ArticleNumber": "Article Number", "RegulationSummary": "Summary of Regulation"}, "RegulationDetails": {"ComplianceStatus": "Compliance Status", "PartialComplianceStatus": "Partial Compliance Status", "NonComplianceStatus": "Non Compliance Status"}, "NavHeadings": {"Controls": "Controls", "Documents": "Documents", "Duties": "Duties", "Actions": "Actions", "Improvements": "Improvements"}, "EditControl": {"EditControl": "Edit Control", "Applicable": "Applicable", "Document": "Document", "SelectDocument": "Select Document", "NotApplicable": "Not Applicable", "ComplianceStatus": "Compliance Status", "Observation": "Observation", "AddObservation": "Add Observation", "Select": "Select", "Compliant": "Compliant", "PartiallyCompliant": "Partially Compliant", "NonCompliant": "Non-Compliant", "AddYourObservationHere": "Add your observation here..."}, "Controls": {"ControlNumber": "Control Number", "ControlDescription": "Control Description", "RegulationSummary": "Summary of In-scope Regulations", "NoDataAvailable": "No Data Available"}, "ExtendedControlTabel": {"ReferenceId": "Reference ID", "BusinessRequirement": "Business Requirement", "ArticleNumber": "Article Number", "Applicability": "Applicability", "Document": "Document", "ComplianceStatus": "Compliance Status", "Observation": "Observation", "Action": "Action", "AddToAction": "Add to Action", "AddToDuty": "Add to Duty", "AddToImprovement": "Add to Improvement", "Action Added Successfully": "Action Added Successfully"}, "Documents": {"Document": "Document", "Description": "Description", "Category": "Category", "CreatedOn": "Created On", "CreatedBy": "Created By", "Attachment": "Attachment"}, "Duties": {"Overdue": "Overdue", "NoOverdueData": "No overdue data", "Open": "Open", "NoOpenData": "No open data"}, "Actions": {"ActionTitle": "Action Title", "AssignedTo": "Assigned To", "AssignedBy": "Assigned By", "AssignedDate": "Assigned Date", "Deadline": "Deadline", "Status": "Status", "NoResult": "No result"}, "Improvements": {"Overdue": "Overdue", "NoOverdueDuties": "No Overdue Duties", "NoCompletedDuties": "No Completed Duties", "Open": "Open"}}, "RiskRegister": {"NavHeadings": {"Activity": "Activity", "Audit Log": "<PERSON>t Log"}, "Activity": {"RiskTitle": "Risk Title", "RiskCategory": "Risk Category", "SourceOfRisk": "Source of Risk", "DateIdentified": "Date Identified", "RiskDescription": "Risk Description", "Regulation": "Regulation", "Module": "<PERSON><PERSON><PERSON>", "Entity": "Entity", "CreateRisk": "Create Risk"}, "RiskForm": {"RiskTitle": "Risk Title", "Write": "Write", "DescriptionOfRisk": "Description of Risk", "EnterDescription": "Enter a description...", "Module": "<PERSON><PERSON><PERSON>", "Select": "Select", "RiskCategory": "Risk Category", "TentativeDate": "Tentative Date", "PickADate": "Pick a date", "SourceOfRisk": "Source of Risk ", "Threat": "Threat", "Vulnerability": "Vulnerability", "Regulation": "Regulation", "Entity": "Entity", "RiskCreatedSuccessfully": "Risk created successfully"}, "RiskEvaluationUpdatedSuccessfully": "Risk evaluation updated successfully", "Steeper": {"SteeperHeadings": {"Identified": "Identified", "Evaluation": "Evaluation", "Mitigation": "Mitigation", "Closure": "Closure", "Monitoring": "Monitoring"}, "EvaluationStep": {"Evaluation": "Evaluation", "Category": "Category", "Compliance": "Compliance", "DateClosed": "Date Closed", "PickADate": "Pick a date", "DateCreated": "Date Created", "Deadline": "Deadline", "Description": "Description", "RiskId": "Risk Id", "InherentRiskLevel": "Inherent Risk Level", "ResidualRiskLevel": "Residual Risk level", "Reminder": "Reminder", "Organization": "Organization", "Result": "Result", "RiskApprover": "Risk Approver", "RiskName": "Risk Name", "RiskOwner": "Risk Owner", "RiskTemplate": "Risk Template", "Source": "Source", "TargetRiskLevel": "Target Risk Level", "Treatment": "Treatment", "TreatmentPlan": "Treatment Plan", "TreatmentStatus": "Treatment Status", "Threat": "Threat", "Type": "Type", "Vulnerability": "Vulnerability"}, "MitigationStep": {"Strategy": "Strategy", "Write": "Write", "YourProgress": "Your Progress", "AddAction": "Add Action", "ActionTitle": "Action Title", "AssignedTo": "Assigned To", "AssignedBy": "Assigned By", "AssignedDate": "Assigned Date", "Deadline": "Deadline", "Status": "Status", "ActionAddedSuccessfully": "Action Added Successfully", "ProceedToNextStep": "Proceed to next step", "ConfirmationText": "Are you satisfied with the migration", "AddActionForm": {"AddAction": "Add Action", "Category": "Category", "Entity": "Entity", "Select": "Select", "Description": "Description", "AssignedBy": "Assigned By", "AssignedTo": "Assigned To", "AssignDate": "Assign Date", "Deadline": "Deadline", "PickADate": "Pick a date"}}, "ClosureStep": {"ClosureReview": "Closure Review", "Write": "Write", "AssociateDuty": "Associate duty", "Duty": "Duty", "AssociatePolicyProcedure": "Associate Policy Procedure", "PolicyProcedure": "Policy Procedure", "AssociateImprovement": "Associate Improvement", "Improvement": "Improvement"}, "MonitoringStep": {"Title": "Title", "Comment": "Comment", "Standard": "Standard", "CreatedDate": "Created Date", "DueDate": "Due Date", "Status": "Status"}}}, "Activities": {"Duty": {"TOTAL": "TOTAL", "OPEN": "OPEN", "ARCHIVE": "ARCHIVE", "NoArchiveData": "No archive data", "Archieved": "<PERSON><PERSON>", "Active": "Active", "Open": "Open", "Overdue": "Overdue", "NoOpenData": "No open data", "AddNew": "ADD NEW", "DutyDetails": "Duty Details", "Fulfilment": "Fulfilment", "AuditLog": "<PERSON>t Log", "Description": "Description", "Name": "Name", "Date": "Date", "Time": "Time", "AddDutyForm": {"AddDuty": "Add Duty", "AddTag": "Add Tag", "DutyTitle": "Duty Title", "AddAssignee": "Add Assignee", "SelectAssignee": "Select Assignee", "DueDate": "Due Date", "PickADate": "Pick a date", "Entity": "Entity", "Select": "Select", "Standards": "Standards", "EnterYourStandardsHere": "Enter your standards here...", "Comment": "Comment", "EnterYourCommentsHere": "Enter your comments here...", "Attachment": "Attachment", "AddDocument": "Add Document", "SelectedFiles": "Selected Files:"}, "DutyDetailForm": {"DutyTitle": "Duty Title", "Dummy": "dummy", "AssignedTo": "Assigned To", "SelectAssignee": "Select Assignee", "Status": "Status", "Open": "OPEN", "Completed": "COMPLETED", "StartDate": "Start Date", "Owner": "Owner", "DueDate": "Due Date", "DueDateValue": "May 7th, 2025", "Frequency": "Frequency", "Select": "Select", "Standards": "Standards", "Criteria": "Criteria", "DefinitionOfEvidence": "Definition of Evidence", "Comments": "Comments", "Attachment": "Attachment", "AddDocument": "Add Document", "SelectedFiles": "Selected Files:", "Annually": "Annually", "BiAnnual": "Bi-Annual", "TriMonthly": "Tri-Monthly", "Monthly": "Monthly"}, "DutyFulfilmentForm": {"DutyInterval": "Duty Interval", "PickDate": "Pick a date", "Assigned": "Assigned", "Select": "Select", "FulfilledBy": "Fulfilled By", "Write": "Write", "Status": "Status", "FulfilmentProof": "Fulfilment Proof", "VerificationQuestion": "Has the effective of the Duty been verified?", "Yes": "Yes", "No": "No", "Attachments": "Attachments", "Vector": "vector", "AddDocuments": "Add Documents", "URLLink": "URL Link", "LandingPageForm": "Landing Page Form", "WebsiteSignup": "Website Signup", "SocialMedia": "Social Media", "Comment": "Comment", "EnterURL": "Enter URL", "Finalized": "Finalized"}}, "Action": {"AddActionForm": {"AddAction": "Add Action", "Category": "Category", "Entity": "Entity", "Select": "Select", "Description": "Description", "AssignedBy": "Assigned By", "AssignedTo": "Assigned To", "AssignDate": "Assign Date", "PickDate": "Pick a date", "Deadline": "Deadline"}, "ActionActivity": {"TotalActionItems": "Total Action Items", "OpenActionItems": "Open Action Items", "ClosedActionItems": "Closed Action Items", "AddNew": "ADD NEW", "ActionAddedSuccessfully": "Action Added Successfully", "ActionTitle": "Action Title", "AssignedTo": "Assigned To", "AssignedBy": "Assigned By", "AssignedDate": "Assigned Date", "Deadline": "Deadline", "Status": "Status"}}, "Improvement": {"TOTAL": "TOTAL", "INPROGRESS": "INPROGRESS", "ARCHIVE": "ARCHIVE", "Open": "Open", "Completed": "Completed", "Overdue": "Overdue", "ImprovementDetails": "Improvement Details", "Evaluation": "Evaluation", "AuditLog": "<PERSON>t Log", "NoCompletedData": "No Completed Data", "ImprovementDetailsForm": {"DutyTitle": "Duty Title", "DummyValue": "pkjw", "AssignedTo": "Assigned To", "SelectAssignee": "Select Assignee", "Status": "In-Progress", "Completed": "Completed", "DueDate": "Due Date", "DueDateValue": "March 10th, 2025", "Owner": "Owner", "Regulation": "Regulation", "Select": "Select", "Finding": "Other areas affected by same finding", "RootCause": "Root Cause", "TreatmentPlan": "Treatment Plan", "Attachment": "Attachment", "AddDocument": "Add Document", "TechTeamInvolved": "Is a tech team involved in implementation?", "Yes": "Yes", "No": "No", "SelectedFiles": "Selected Files:", "AddProgress": "Add your progress"}, "AddNewImprovementForm": {"AddImprovement": "Add Improvement", "AddTag": "Add Tag", "ImprovementActionTitle": "Improvement Action Title", "Title": "Title", "AddAssignee": "Add Assignee", "SelectAssignee": "Select Assignee", "DueDate": "Due Date", "PickDate": "Pick a date", "Regulation": "Regulation", "Select": "Select", "Entity": "Entity", "Comment": "Comment", "Comments": "Comments", "Attachment": "Attachment", "AddDocument": "Add Document", "SelectedFiles": "Selected Files:"}, "EvaluationForm": {"ReviewedBy": "Reviewed by", "ReviewedByPlaceholder": "Write", "EffectOnFinding": "Does it have an effect on the finding?", "EffectOnFindingPlaceholder": "Write", "OtherAreasAffected": "Other areas affected by the same finding", "OtherAreasAffectedPlaceholder": "Write", "ManagementReviewTopic": "Management review topic", "Select": "Select", "ManagementReviewText": "Text for the management review", "LandingPageForm": "Landing Page Form", "WebsiteSignup": "Website Signup", "SocialMedia": "Social Media", "Source": "Source"}}}, "Repository": {"DocumentRepository": {"Total": "Total", "Creation": "Creation", "In Progress": "In Progress", "In Use": "In Use", "Document": "Document", "Description": "Description", "Category": "Category", "CreatedOn": "Created On", "CreatedBy": "Created By", "Attachments": "Attachments"}, "AssesmentRepository": {"Total": "Total", "In Progress": "In Progress", "Completed": "Completed", "Department": "Department", "AssessmentName": "Assessment Name", "AssessmentType": "Assessment Type", "CreatedDate": "Created Date", "CreatedBy": "Created By", "Action": "Action"}, "RecordOfProcessingActivitiesRepository": {"Total": "Total", "In Progress": "In Progress", "Completed": "Completed", "Department": "Department", "Process": "Process", "CreatedDate": "Created Date", "CreatedBy": "Created By", "Version": "Version", "Action": "Action", "Status": "Status"}}}, "FrontEndErrorMessage": {"AccountSetup": {"UserManagement": {"UserAddedSuccessfully": "User added successfully!", "ProcessingIn": "Processing in...", "Processing": "Processing...", "FailedToAddUser": "Failed to add user", "FailedToUpdateUser": "Failed to update user", "FailedToDeleteUser": "Failed to delete user", "UserUpdatedSuccessfully": "User updated successfully!", "UserDeletedSuccessfully": "User deleted successfully!", "AssigningUserFailed": "Assigning User failed!", "AnErrorOccurredCouldNotUpdate": "An error occurred, could not update"}, "RoleManagement": {"RoleAddedSuccessfully": "Role added successfully!", "RoleUpdatedSuccessfully": "Role updated successfully!", "RoleDeletedSuccessfully": "Role deleted successfully!", "FailedToAddRole": "Failed to add role", "FailedToUpdateRole": "Failed to update role", "FailedToDeleteRole": "Failed to delete role"}, "CompanyStructure": {"BusinessUnitCreatedSuccessfully": "Business Unit created successfully", "DepartmentCreatedSuccessfully": "Department created successfully", "DepartmentsCreatedSuccessfully": "Departments created successfully.", "ProcessCreatedSuccessfully": "Process created successfully", "FailedToCreateBusinessUnit": "Failed to create business unit", "FailedToCreateDepartment": "Failed to create department", "FailedToCreateProcess": "Failed to create process", "NameIsRequired": "Name is required", "PleaseSelectRegionField": "Please select the region field", "PleaseFillInField": "Please fill in the {field}", "PleaseUploadCSVFile": "Please upload a CSV file", "GroupIdRequired": "Group ID is required but could not be determined"}}, "Authentication": {"PleaseProvidePassword": "Please provide password!", "PasswordsDoNotMatch": "Passwords do not match!", "PasswordUpdatedSuccessfully": "Password updated successfully!", "PleaseAgreeTermsConditions": "Please agree on our terms and conditions", "InvalidCredentials": "Invalid credentials", "LoginSuccessful": "Login successful", "LogoutSuccessful": "Logout successful", "SessionExpired": "Session expired", "UnauthorizedAccess": "Unauthorized access", "OTPVerifiedSuccessfully": "OTP verified successfully!", "InvalidOTPPleaseTryAgain": "Invalid OTP. Please try again.", "FailedToVerifyOTP": "Failed to verify <PERSON><PERSON>. Please try again.", "PleaseEnterValidOTP": "Please enter a valid OTP", "VerificationCodeResentSuccessfully": "Verification code resent successfully!", "FailedToResendOTP": "Failed to resend OTP", "SendingCode": "Sending code...", "CodeSentSuccessfully": "Code sent successfully!", "LoggingIn": "Logging in..."}, "FormValidation": {"ThisFieldIsRequired": "This field is required!", "PleaseSelectRegion": "Please Select Region", "ChooseCategory": "Please choose atleast one category", "QuestionScope": "Please select question scope", "PleaseEnterValidEmailAddress": "Please enter a valid email address", "InvalidPhoneNumber": "Invalid phone number", "PhoneNumberIsRequired": "Phone number is required", "TenDigitsRequired": "10 digits are required", "ExpiryIsRequired": "Expiry is required!", "FrequencyValueRequired": "Frequency value is required when Frequency is enabled!", "UsernameMinLength": "Userna<PERSON> must be at least 2 characters.", "CustomerNameRequired": "Customer name is required", "InvalidEmailAddress": "Invalid email address", "AddressRequired": "Address is required", "CountryRequired": "Country is required", "IndustryRequired": "Industry is required", "RegulationRequired": "At least one regulation must be selected", "AdminNameRequired": "Admin name is required", "InvalidAdminEmail": "Invalid admin email address", "ResourceRequired": "At least one resource must be selected", "AuthenticationTypeRequired": "Authentication type is required when verification is enabled", "FormNameRequired": "This field is required", "BusinessUnitRequired": "This field is required", "PleaseProvideEmailAndPassword": "Please provide both email and password.", "PleaseProvidePassword": "Please provide password.", "MustAgreeToTerms": "You must agree to the terms and conditions", "EnterName": "Enter Name", "EnterDescription": "Enter Description", "Error": "Error", "ErrorDot": "Error.", "PleaseAgreeToAllRequiredConsents": "Please agree to all required consents.", "ErrorSubmittingForm": "Error submitting form", "PleaseProvideYourConsentFirst": "Please provide your consent first.", "PleaseInputYourEmail": "Please input your email.", "PleaseEnterValidEmail": "Please enter a valid email address.", "PleaseEnterValidPhoneNumber": "Please enter a valid 10-digit phone number.", "InvalidInputTypeSpecified": "Invalid input type specified.", "PleaseEnterValidOTP": "Please enter a valid 6-digit OTP", "FailedToChangePreferences": "Failed to change preferences", "InvalidOTP": "Invalid OTP", "PreferenceDataNotAvailable": "Preference data not available.", "PleaseSaveChanges": "Please save changes!", "PleaseSelectAnyOneService": "Please select any one service!", "PleaseSelectAnyOneMaturityLevel": "Please select any one maturity level!", "PleaseSelectAnyOneQuestionnaire": "Please select any one questionnaire!"}, "ApiErrors": {"SomethingWentWrong": "Something went wrong.", "NetworkError": "Network error.", "AnErrorOccurred": "An error occurred", "AccessDenied": "Access denied. You may not have permission to access this resource.", "UnexpectedErrorOccurred": "An unexpected error occurred.", "ErrorReportSentSuccessfully": "Error report sent successfully.", "ForbiddenError": "403 Forbidden error", "InternalServerError": "Internal server error", "BadRequest": "Bad request", "NotFound": "Resource not found", "ServiceUnavailable": "Service unavailable", "SomeUpdatesFailed": "Some updates failed", "FailedToFetchData": "Failed to fetch data"}, "CookieManagement": {"DefaultBannerError": "There must be exactly one default banner. Please adjust your selection.", "UnableToTranslateData": "Unable to translate data", "UnableToFetchData": "Unable to fetch data", "TranslationSavedSuccessfully": "Translation saved successfully", "SavingTranslation": "Saving translation...", "ScanStartedSuccessfully": "<PERSON><PERSON> Started Successfully", "ScanFailed": "Scan Failed", "PolicyAddedSuccessfully": "Policy added successfully", "PolicyUpdatedSuccessfully": "Policy updated successfully", "FailedToAddPolicy": "Failed to add policy", "FailedToUpdatePolicy": "Failed to update policy", "NoGTMConfigurationToApply": "No GTM Configuration To Apply", "NoAdobeLaunchConfigurationToApply": "No Adobe Launch Configuration To Apply", "AutoScanRequired": "Auto scan is required", "AdobeLaunchSettingsAppliedToAll": "Adobe Launch Settings Applied To All"}, "VendorManagement": {"VendorAddedSuccessfully": "<PERSON><PERSON><PERSON> added successfully", "CouldntAddVendor": "Couldn't add vendor", "VendorUpdatedSuccessfully": "Vendor updated successfully", "VendorDeletedSuccessfully": "<PERSON><PERSON><PERSON> deleted successfully", "FailedToUpdateVendor": "Failed to update vendor", "FailedToDeleteVendor": "Failed to delete vendor"}, "DataMapping": {"PiiAddedSuccessfully": "PII added successfully", "PiiUpdatedSuccessfully": "PII updated successfully", "PiiDeletedSuccessfully": "PII deleted successfully", "FailedToAddPii": "Failed to add PII", "FailedToUpdatePii": "Failed to update PII", "FailedToDeletePii": "Failed to delete PII", "PiiAlreadyExists": "PII already exists", "PIIAddedSuccessfully": "PII added successfully", "PIIEditedSuccessfully": "PII edited successfully", "PIIDeletedSuccessfully": "PII deleted successfully", "TaskTriggeredSuccessfully": "Task triggered successfully", "ServiceAddedSuccessfully": "Service added successfully", "FileProcessedSuccessfully": "File processed successfully"}, "AssessmentManagement": {"AssessmentCreatedSuccessfully": "Assessment created successfully", "AssessmentUpdatedSuccessfully": "Assessment updated successfully", "AssessmentDeletedSuccessfully": "Assessment deleted successfully", "FailedToCreateAssessment": "Failed to create assessment", "FailedToUpdateAssessment": "Failed to update assessment", "ErrorInCreatingQuestion": "Error in creating question", "FailedToDeleteAssessment": "Failed to delete assessment"}, "DSR": {"RequestCreatedSuccessfully": "Request created successfully", "RequestUpdatedSuccessfully": "Request updated successfully", "RequestDeletedSuccessfully": "Request deleted successfully", "FailedToCreateRequest": "Failed to create request", "FailedToUpdateRequest": "Failed to update request", "FailedToDeleteRequest": "Failed to delete request", "FormPublishedSuccessfully": "Form published successfully", "FailedToPublishForm": "Failed to publish form", "DocumentsAddedSuccessfully": "Documents added successfully!", "DocumentDeletedSuccessfully": "Document deleted successfully!", "FailedToDeleteDocument": "Failed to delete document. Please try again.", "FailedToDownloadFile": "Failed to download file. Please try again.", "SubjectIsRequired": "Subject is required", "EmailContentIsRequired": "Email content is required", "SubmittingForm": "Submitting form...", "FormSubmittedSuccessfully": "Form submitted successfully", "FailedToSubmitForm": "Failed to submit form", "UploadingLogo": "Uploading logo...", "LogoUploadedSuccessfully": "Logo uploaded successfully", "FailedToUploadLogo": "Failed to upload logo", "TemplateDeletedSuccessfully": "Template deleted successfully!", "DocumentUploadedSuccessfully": "Document uploaded successfully!", "FailedToUploadDocument": "Failed to upload document", "DocumentsDeletedSuccessfully": "Documents deleted successfully!", "RequestApprovedSuccessfully": "Request approved successfully!", "RequestRejectedSuccessfully": "Request rejected successfully!", "DeadlineExtendedSuccessfully": "The deadline has been successfully extended!", "AuditLogDownloading": "Downloading audit log...", "ProcessingEllipsis": "Processing in...", "UpdatingTask": "Updating task...", "TaskUpdatedSuccessfully": "Task updated successfully", "ErrorUpdatingTask": "Failed to update task", "ErrorLoadingData": "Error loading data", "MessageCannotBeEmpty": "Message cannot be empty.", "RequestUnderVerification": "Your request is under verification", "ErrorFetchingAssignees": "An error occurred while fetching assignees", "PleaseSelectAssignee": "Please select an assignee", "FailedToAssignUser": "Failed to assign user", "ErrorAssigningUser": "An error occurred while assigning user", "PleaseEnterEmail": "Please enter an email.", "VerificationCodeSent": "Verification code sent successfully!", "SomethingWrong": "Something Wrong!", "FailedToResendVerificationCode": "Failed to resend verification code.", "FailedToResendVerificationCodeTryAgain": "Failed to resend verification code. Please try again.", "InvalidOTPTryAgain": "Invalid OTP. Please try again.", "BusinessUnitUpdated": "Business unit updated successfully", "Success": "Success", "FailedToUpdateTask": "Failed to update task", "CannotDeleteLastStep": "Cannot delete the last workflow step", "WorkflowStepDeletedSuccessfully": "Workflow step deleted successfully", "FailedToDeleteWorkflowStep": "Failed to delete workflow step", "ErrorDeletingWorkflowStep": "An error occurred while deleting the workflow step", "StepTitleCannotBeEmpty": "Step title cannot be empty", "FailedToCompleteRequest": "Failed to complete request", "WorkflowIdMissing": "Workflow ID is missing", "Processing": "Processing...", "WorkflowUpdatedSuccessfully": "'Workflow updated successfully", "InvalidAPIRoute": "Invalid API route.", "FailedToUpdateWorkflow": "Failed to update workflow. Please try again later.", "TitleCannotBeEmpty": "Title cannot be empty", "ControlUpdatedSuccessfully": "Control updated successfully", "FailedToUpdateControl": "Failed to update control", "TitleRequired": "Title is required", "FailedToAddQuestion": "Failed To Add Question", "QuestionAddedSuccessfully": "Question Added Successfully", "FailedToAddWorkflow": "Failed To Add Workflow", "WorkflowAddedSuccessfully": "Workflow Added Successfully", "AddingWorkflow": "Adding Workflow", "DuplicateWorkflow": "Duplicate Workflow", "PleaseEnterWorkflowType": "Please Enter Workflow Type", "WorkflowCreatedSuccessfully": "Workflow Created Successfully", "FailedToCreateWorkflow": "Failed To Create Workflow", "ErrorCreatingWorkflow": "Error Creating Workflow", "SubmittedSuccessfully": "Submitted Successfully", "ProcessingRequest": "Processing Request", "FiltersAppliedSuccessfully": "Filters Applied Successfully", "AddingTask": "Adding Task", "TaskAddedSuccessfully": "Task Added Successfully", "FailedToAddTask": "Failed To Add Task", "TaskAutomationUpdatedSuccessfully": "Task automation updated successfully", "AutomationRemovedSuccessfully": "Automation removed successfully", "ListIdEmpty": "List id empty", "WorkflowStepRenamedSuccessfully": "Workflow step renamed successfully", "QuestionOrderUpdatedSuccessfully": "Question order updated successfully", "QuestionRemoved": "Question removed", "QuestionDeletedSuccessfully": "Question deleted successfully", "UserUnauthaorized": "User unauthorized"}, "PolicyManagement": {"CollaboratorAddedSuccessfully": "Collaborator added successfully", "AllChangesSaved": "All changes saved", "PolicyCreatedSuccessfully": "Policy created successfully"}, "BreachManagement": {"BreachAddedSuccessfully": "Breach added successfully", "BreachEvaluationUpdatedSuccessfully": "Breach evaluation updated successfully", "BreachResolutionDoneSuccessfully": "Breach resolution done successfully", "ActionAddedSuccessfully": "Action added successfully"}, "PrivacyOps": {"RiskCreatedSuccessfully": "Risk created successfully", "RiskUpdatedSuccessfully": "Risk updated successfully", "RiskDeletedSuccessfully": "Risk deleted successfully", "FailedToCreateRisk": "Failed to create risk", "FailedToUpdateRisk": "Failed to update risk", "FailedToDeleteRisk": "Failed to delete risk", "RegulationAddedSuccessfully": "Regulation added successfully", "FailedToAddRegulation": "Failed to add regulation"}, "Onboarding": {"ProcessCompletedSuccessfully": "Process completed successfully!", "SendingOnboardingDataFailed": "Sending Onboarding data failed!", "OnboardingSuccessful": "Onboarding completed successfully", "OnboardingFailed": "Onboarding failed"}, "Profile": {"PasswordUpdateFailed": "Password update failed!", "FailedToSaveChanges": "Failed to save changes."}, "VRM": {"AccurateInformationRequired": "Accurate Information is required.", "CommentRequired": "Comment is required.", "RiskScoreRequired": "Risk Score is required.", "CouldntSaveReview": "Couldn't save review", "ReviewSavedSuccessfully": "Review saved successfully", "AssessmentSubmittedSuccessfully": "Assessment submitted successfully!", "PleaseAnswerAllQuestions": "Please answer all questions!", "AssessmentReviewedSuccessfully": "Assessment reviewed successfully!", "PleaseReviewAllQuestions": "Please review all questions!", "ProcessingIn": "Processing in...", "CouldntStartAssessment": "Couldn't start assessment", "CouldntAddVendor": "Couldn't add vendor", "CouldntUpdateVendor": "Couldn't update vendor", "VendorAddedSuccessfully": "<PERSON><PERSON><PERSON> added successfully", "VendorUpdatedSuccessfully": "Vendor updated successfully", "PlanSavedSuccessfully": "Plan saved successfully", "CouldntSaveMitigationPlan": "Couldn't save mitigation plan.", "DataFetchedSuccessfully": "Data Fetched Successfully", "AnErrorOccurred": "An error occurred", "DeletedSuccessfully": "Deleted successfully", "UploadedSuccessfully": "Uploaded successfully", "DownloadLinkSent": "The download link has been sent to your email address. Please check your inbox", "Success": "Success", "NewVendor": "+ New vendor", "UserAssignedSuccessfully": "User assigned successfully!", "AssigningUserFailed": "Assigning User failed!", "MitigationPlanSubmittedSuccessfully": "Mitigation Plan submitted successfully.", "QuestionUpdatedSuccessfully": "Question updated successfully.", "QuestionDeletedSuccessfully": "Question has been deleted successfully", "Error": "Error", "ErrorExclamation": "Error!", "UploadValidFile": "Upload valid file", "ControlAndDescriptionRequired": "Control and description are required.", "UploadFilesOrEnterURL": "Upload File(s) or Enter URL", "EnterValidURL": "Enter Valid URL", "FailedToOpenWindow": "Failed to open the window. It might have been blocked by the browser.", "EnterTemplateName": "Enter Template name", "PleaseAttachDocumentOrURL": "Please attach document or url!", "InternalAssessment": "Internal-Assessment", "ExternalAssessment": "External-Assessment"}, "ROPA": {"RopaSubmittedSuccessfully": "ROPA has been submitted successfully", "PleaseAnswerAllQuestions": "Please answer all questions before submit.", "ProgressBarError": "Progress bar error!", "TentativeDateUpdatedSuccessfully": "Tentative Date Updated Successfully", "RecurrenceDateUpdatedSuccessfully": "Recurrence Date Updated Successfully"}, "FileUpload": {"FileUploadedSuccessfully": "File uploaded successfully", "FileUploadFailed": "File upload failed", "InvalidFileType": "Invalid file type", "FileSizeTooLarge": "File size is too large", "PleaseSelectFile": "Please select a file", "UploadInProgress": "Upload in progress...", "MaxFileSizeExceeded": "Maximum file size exceeded", "UploadingFile": "Uploading file...", "FailedToUploadFile": "Failed to upload file", "FailedToDownloadSampleFile": "Failed to download sample file", "ImportedSuccessfully": "Imported successfully", "FailedToImportFile": "Failed to import file", "ErrorDeletingFile": "Error deleting file", "PleaseSelectAtLeastOneDocument": "Please select at least one document to upload", "FailedToUploadDocuments": "Failed to upload documents"}, "DataValidation": {"InvalidDataFormat": "Invalid data format", "DataValidationFailed": "Data validation failed", "RequiredDataMissing": "Required data is missing", "InvalidDateFormat": "Invalid date format", "InvalidNumberFormat": "Invalid number format", "ValueOutOfRange": "Value is out of range", "DuplicateEntryFound": "Duplicate entry found"}, "ErrorBoundary": {"SomethingWentWrong": "Something went wrong", "ErrorCaughtByErrorBoundary": "Error caught by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorDetails": "Error details", "UnexpectedError": "An unexpected error occurred", "PageCrashed": "This page has crashed", "RefreshPage": "Refresh Page", "ReportIssue": "Report Issue"}, "Unauthorized": {"Whoops": "Whoops!", "RoadBlockAhead": "Road Block Ahead", "AccessDenied": "Access denied", "InsufficientPermissions": "You don't have sufficient permissions", "ContactAdministrator": "Please contact your administrator", "LoginRequired": "Login required to access this page"}, "HttpClient": {"NoAccessTokenAvailable": "No access token available for request", "RefreshTokenExpired": "Refresh token expired", "TokenRefreshFailed": "Token refresh failed", "ForbiddenError": "403 Forbidden error", "RequestFailed": "Request failed", "ResponseError": "Response error"}, "UniversalConsentManagement": {"downloadFailed": "Download failed", "invalidFileFormat": "Invalid file format. Please upload a csv file", "failedToUploadFile": "Failed To Upload File", "uploadValidFile": "Upload valid file", "processing": "Processing...", "uploadedSuccessfully": "Uploaded successfully.", "uploadFailed": "Upload failed: {error}", "unknownError": "Unknown error", "PleaseEnterAllFields": "Please Enter All Fields", "errorWithMessage": "Error: {error}", "unexpectedErrorOccurred": "An unexpected error occurred. Please try again.", "processingPurposeUpdatedSuccessfully": "Processing purpose updated successfully", "couldntUpdateProcessingPurpose": "Couldn't update processing purpose", "fillAllRequiredFields": "Fill all required fields.", "nameRequired": "Name is required.", "descriptionRequired": "Description is required.", "pleaseSelectConsentPurpose": "Please select a consent purpose", "pleaseSelectSource": "Please select a source.", "titleDescriptionRequired": "Please enter a title and description for your form", "formDescriptionRequired": "Please enter a description for your form", "formTitleRequired": "Please enter a title for your form", "addedSuccessfully": "added successfully", "couldntCreate": "Couldn't create", "fillRequiredFields": "Fill required fields.", "unexpectedError": "An unexpected error occurred", "processingIn": "Processing in...", "categoryIsRequired": "Category is required", "dataRetentionIsRequired": "Data retention is required", "lawfulBasisIsRequired": "Lawful basis is required", "purposeIsRequired": "Purpose is required", "typeIsRequired": "Type is required", "complianceOfficerIsRequired": "Compliance officer is required", "dataSubjectIsRequired": "Data subject is required", "sourceIsRequired": "Source is required", "dataImporterIsRequired": "Data importer is required", "dataExporterIsRequired": "Data exporter is required", "enterName": "Enter Name", "enterDescription": "Enter Description", "pleaseEnterAllFields": "Please enter all the fields", "consentPurposeUpdatedSuccessfully": "Consent purpose updated successfully", "consentPurposeDeletedSuccessfully": "Consent purpose deleted successfully", "processingPurposeDeletedSuccessfully": "Processing purpose deleted successfully", "piiLabelUpdatedSuccessfully": "Pii label updated successfully", "piiLabelDeletedSuccessfully": "Pii label deleted successfully", "recordUpdatedSuccessfully": "Record updated successfully", "privacyNoticeSavedSuccessfully": "Privacy notice saved successfully", "templateDownloadedSuccessfully": "Template downloaded successfully", "templateActivatedSuccessfully": "Template activated successfully", "templateInactivatedSuccessfully": "Template inactivated successfully", "couldntUpdateConsentPurpose": "Couldn't update consent purpose", "pleaseEnterTitle": "Please enter the title", "noPrivacyNoticePresent": "No privacy notice present, Please select one or create a new one.", "couldntUpdatePiiLabel": "Couldn't update pii label", "couldntUpdateRecordData": "Couldn't update record data", "copiedToClipboard": "Copied to clipboard!", "failedToCopy": "Failed to copy. Please try again.", "urlCopiedToClipboard": "URL copied to clipboard!", "failedToUpdateStatus": "Failed to update status. Please try again.", "entityAndTitleRequired": "Entity and title are required", "editorContentEmpty": "Editor content is empty", "noContentToSave": "No content to save", "anErrorOccurred": "An error occurred", "pleaseCompleteStep": "Please complete the step before proceeding.", "formTranslated": "Form Translated", "TranslatedDataFetched": "Translated data fetched", "pleaseSelectEntityAndDepartment": "Please select both entity and department", "FailedToSavePrivacyNotice": "Failed to save privacy notice"}}, "CommonErrorMessages": {"Sending": "Sending", "AddingWorkflowNewStep": "Adding workflow's new step...", "UpdatingTaskAutomation": "Updating task automation...", "RemovingAutomation": "Removing automation...", "UpdatingTask": "Updating task...", "DownloadingAuditLog": "Downloading audit log...", "SendingConsentLink": "Sending Consent Link...", "Saving": "Saving...", "VerifyingEmail": "Verifying Email...", "ChooseAtLeastOneCollaborator": "choose at least one collaborator", "SpecialCharacterNotAllowed": "special character is not allowed", "PleaseEnterCorrectURL": "Please enter correct URL", "LoadingTranslations": "Loading translations...", "LoadingData": "Loading...", "ProcessCompleted": "Process completed successfully!", "OperationSuccessful": "Submitted successfully", "OperationFailed": "Failed", "DataSavedSuccessfully": "Data saved successfully", "DataUpdatedSuccessfully": "Data updated successfully", "DataDeletedSuccessfully": "Data deleted successfully", "FailedToSaveData": "Failed to save data", "FailedToUpdateData": "Failed to update data", "FailedToDeleteData": "Failed to delete data", "ProcessingIn": "Processing in...", "Processing": "Processing", "ProcessingEllipsis": "Processing...", "FailedToFetchData": "Failed to fetch data.", "AnUnexpectedErrorOccurred": "An unexpected error occurred.", "VendorAddedSuccessfully": "<PERSON><PERSON><PERSON> added successfully", "CouldntStartAssessment": "Couldn't start assessment", "CouldntDownloadAssessment": "Couldn't download assessment", "CouldntAddVendor": "Couldn't add vendor", "FormSubmittedSuccessfully": "Form submitted successfully", "UserAssignedSuccessfully": "User assigned successfully!", "AssigningUserFailed": "Assigning User failed!", "AssessmentCreatedSuccessfully": "Assessment Created Successfully", "FailedToCreateAssessment": "Failed to create assessment", "QuestionDeletedSuccessfully": "Question has been deleted successfully", "Error": "Error", "ErrorDot": "Error.", "SendingOnboardingDataFailed": "Sending Onboarding data failed!", "PleaseAgreeToAllRequiredConsents": "Please agree to all required consents before submitting.", "ErrorSubmittingForm": "Error submitting form", "FailedToFetch": "Failed to fetch", "MappingDeletedSuccessfully": "Mapping deleted successfully", "DeleteFailed": "Delete failed", "EntityIdRequiredForDeletion": "Entity ID is required for deletion", "TextFieldAdded": "Text Field Added", "PleaseFilTheInput": "Please fill the input", "CouldntUpdateVendor": "Couldn't update vendor", "VendorUpdatedSuccessfully": "Vendor updated successfully", "SavedSuccessfully": "Saved successfully!", "AnswerSavedSuccessfully": "Answer saved successfully", "CouldntSaveAnswers": "Couldn't save answers", "ProgressBarError": "Progress bar error!", "ReviewSavedSuccessfully": "Review saved successfully", "CouldntSaveReviews": "Couldn't save reviews", "Success": "Success!", "SomethingWentWrong": "Something went wrong", "AssessmentReviewedSuccessfully": "Assessment reviewed successfully!", "PleaseReviewAllQuestions": "Please review all questions!", "AnErrorOccurred": "An error occurred", "ConfirmDelete": "Are you sure you want to delete this item?", "ConfirmUpdate": "Are you sure you want to update this item?", "ConfirmSave": "Are you sure you want to save changes?", "UnsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "InvalidInput": "Invalid input provided", "RequiredFieldsMissing": "Please fill in all required fields", "FileUploadSuccessful": "File uploaded successfully", "FileUploadFailed": "File upload failed", "InvalidFileFormat": "Invalid file format", "FileSizeExceeded": "File size exceeds the maximum limit", "ConnectionLost": "Connection lost. Please check your internet connection.", "TimeoutError": "Request timeout. Please try again.", "PermissionDenied": "You don't have permission to perform this action", "SessionTimeout": "Your session has timed out. Please login again.", "ServerError": "Server error occurred. Please try again later.", "BadRequest": "Bad request. Please check your input.", "NotFound": "The requested resource was not found.", "Conflict": "A conflict occurred. The resource may have been modified.", "TooManyRequests": "Too many requests. Please try again later.", "ServiceUnavailable": "Service is temporarily unavailable.", "MaintenanceMode": "System is under maintenance. Please try again later.", "InvalidToken": "Invalid or expired token.", "EmailAlreadyExists": "Email address already exists.", "WeakPassword": "Password is too weak. Please choose a stronger password.", "PasswordMismatch": "Passwords do not match.", "AccountLocked": "Account has been locked. Please contact support.", "AccountNotVerified": "Account is not verified. Please check your email.", "InvalidCredentials": "Invalid username or password.", "EmailNotFound": "Email address not found.", "UserNotFound": "User not found.", "AccessExpired": "Access has expired. Please renew your subscription.", "QuotaExceeded": "<PERSON><PERSON><PERSON> exceeded. Please upgrade your plan.", "FeatureNotAvailable": "This feature is not available in your current plan.", "InvalidConfiguration": "Invalid configuration detected.", "DatabaseError": "Database error occurred.", "ExternalServiceError": "External service error occurred.", "ValidationError": "n occurred.", "AuthenticationRequired": "Authentication is required.", "AuthorizationFailed": "Authorization failed.", "ResourceLocked": "Resource is currently locked by another user.", "ConcurrentModification": "Resource was modified by another user.", "OperationNotAllowed": "This operation is not allowed.", "InvalidState": "Invalid state for this operation.", "DependencyError": "Cannot complete operation due to dependencies.", "IntegrityConstraintViolation": "Data integrity constraint violation.", "DuplicateKey": "Duplicate key error.", "ForeignKeyConstraint": "Foreign key constraint violation.", "CheckConstraintViolation": "Check constraint violation.", "DataTruncation": "Data truncation error.", "NumericOverflow": "Numeric overflow error.", "DateTimeError": "Date/time format error.", "EncodingError": "Character encoding error.", "CompressionError": "Data compression error.", "EncryptionError": "Data encryption error.", "DecryptionError": "Data decryption error.", "HashingError": "Data hashing error.", "SignatureError": "Digital signature error.", "CertificateError": "Certificate validation error.", "SSLError": "SSL/TLS connection error.", "ProxyError": "Proxy server error.", "DNSError": "DNS resolution error.", "NetworkTimeout": "Network timeout error.", "ConnectionRefused": "Connection refused by server.", "HostUnreachable": "Host unreachable.", "PortClosed": "Port is closed or blocked.", "FirewallBlocked": "Request blocked by firewall.", "RateLimited": "Request rate limited.", "BandwidthExceeded": "Bandwidth limit exceeded.", "StorageQuotaExceeded": "Storage quota exceeded.", "MemoryLimitExceeded": "Memory limit exceeded.", "CPULimitExceeded": "CPU limit exceeded.", "ExecutionTimeout": "Execution timeout.", "ResourceExhausted": "System resources exhausted.", "ServiceDegraded": "Service is running in degraded mode.", "PartialFailure": "Operation completed with partial failures.", "RetryLimitExceeded": "Retry limit exceeded.", "CircuitBreakerOpen": "Circuit breaker is open.", "LoadBalancerError": "Load balancer error.", "CacheError": "Cache operation error.", "QueueFull": "Message queue is full.", "MessageExpired": "Message has expired.", "SerializationError": "Data serialization error.", "DeserializationError": "Data deserialization error.", "VersionMismatch": "Version mismatch error.", "ProtocolError": "Protocol error.", "FormatError": "Data format error.", "ParsingError": "Data parsing error.", "TransformationError": "Data transformation error.", "MappingError": "Data mapping error.", "ConversionError": "Data conversion error.", "CalculationError": "Calculation error.", "RoundingError": "Rounding error.", "PrecisionLoss": "Precision loss error.", "OverflowError": "Overflow error.", "UnderflowError": "Underflow error.", "DivisionByZero": "Division by zero error.", "InvalidOperation": "Invalid mathematical operation.", "DomainError": "Mathematical domain error.", "RangeError": "Mathematical range error.", "PleaseSelectFromList": "Please Select from the List", "ThisFieldIsRequired": "This field is required", "PleaseEnterValidEmail": "Please enter a valid email address", "InvalidFileFormatPDF": "Invalid file format. Please upload a PDF, DOC, or DOCX file", "PleaseSelectOption": "Please select an option", "FieldRequired": "This field is required", "InvalidEmail": "Please enter a valid email address"}, "AuditLog": {"RequestStatusChangedToAcknowledgement": "Request Status changed to Acknowledgement", "RequestStatusChangedToDataGathering": "Request Status changed to Data Gathering"}, "ToastMessages": {"Authentication": {"OTPVerifiedSuccessfully": "OTP verified successfully!", "OTPSentSuccessfully": "OTP sent successfully", "OTPResentSuccessfully": "OTP resent successfully", "VerificationCodeSentSuccessfully": "Verification code sent successfully", "VerificationCodeResentSuccessfully": "Verification code resent successfully", "PasswordUpdatedSuccessfully": "Password updated successfully", "CodeSentSuccessfully": "Code sent successfully", "FailedToSendOTP": "Failed to send <PERSON><PERSON>. Please try again."}, "Forms": {"FormSubmittedSuccessfully": "Form submitted successfully", "FormTranslatedSuccessfully": "Form translated successfully", "DataSavedSuccessfully": "Data saved successfully", "DataUpdatedSuccessfully": "Data updated successfully", "OperationSuccessful": "Operation successful", "ProcessCompletedSuccessfully": "Process completed successfully", "SubmittedSuccessfully": "Submitted successfully", "AllChangesSaved": "All changes saved", "SavedSuccessfully": "Saved successfully"}, "Files": {"FileUploadedSuccessfully": "File uploaded successfully", "FileImportedSuccessfully": "File imported successfully", "FileProcessedSuccessfully": "File processed successfully", "DocumentUploadedSuccessfully": "Document uploaded successfully", "DocumentDeletedSuccessfully": "Document deleted successfully", "DocumentsAddedSuccessfully": "Documents added successfully", "DocumentsUploadedSuccessfully": "Documents uploaded successfully", "DocumentsDeletedSuccessfully": "Documents deleted successfully", "UploadedSuccessfully": "Uploaded successfully", "TemplateDownloadedSuccessfully": "Template downloaded successfully"}, "Assessment": {"AssessmentCreatedSuccessfully": "Assessment created successfully", "AssessmentSubmittedSuccessfully": "Assessment submitted successfully", "AssessmentReviewedSuccessfully": "Assessment reviewed successfully", "AnswerSavedSuccessfully": "Answer saved successfully", "ReviewSavedSuccessfully": "Review saved successfully", "PleaseAnswerAllQuestions": "Please answer all questions!", "ProgressBarError": "Progress bar error!", "ErrorInUpdatingTheQuestion": "Error in updating the question", "PleaseReviewAllQuestions": "Please review all questions!", "FailedToLoadCategoryTypes": "Failed to load category types"}, "ROPA": {"ROPASubmittedSuccessfully": "ROPA submitted successfully", "ROPAStartedSuccessfully": "ROPA started successfully", "ROPADataUpdatedSuccessfully": "ROPA data updated successfully", "TentativeDateUpdatedSuccessfully": "Tentative date updated successfully"}, "Users": {"UserAssignedSuccessfully": "User assigned successfully", "UserAddedSuccessfully": "User added successfully", "UserUpdatedSuccessfully": "User updated successfully", "PasswordResetEmailSentSuccessfully": "Password reset email sent successfully"}, "Vendors": {"VendorAddedSuccessfully": "<PERSON><PERSON><PERSON> added successfully", "VendorUpdatedSuccessfully": "Vendor updated successfully"}, "UCM": {"ProcessingPurposeUpdatedSuccessfully": "Processing purpose updated successfully"}, "Cookies": {"CookieUpdatedSuccessfully": "<PERSON><PERSON> updated successfully", "TranslationSavedSuccessfully": "Translation saved successfully", "PolicyAddedSuccessfully": "Policy added successfully", "PolicyUpdatedSuccessfully": "Policy updated successfully"}, "Consent": {"ConsentPurposeUpdatedSuccessfully": "Consent purpose updated successfully", "ConsentPurposeDeletedSuccessfully": "Consent purpose deleted successfully", "ProcessingPurposeAddedSuccessfully": "Processing purpose added successfully", "ProcessingPurposeUpdatedSuccessfully": "Processing purpose updated successfully", "ProcessingPurposeDeletedSuccessfully": "Processing purpose deleted successfully"}, "Workflow": {"WorkflowCreatedSuccessfully": "Workflow created successfully", "WorkflowUpdatedSuccessfully": "Workflow updated successfully", "WorkflowAddedSuccessfully": "Workflow added successfully", "TaskAddedSuccessfully": "Task added successfully", "TaskUpdatedSuccessfully": "Task updated successfully", "TaskAutomationUpdatedSuccessfully": "Task automation updated successfully", "AutomationRemovedSuccessfully": "Automation removed successfully", "StepAddedSuccessfully": "Step added successfully", "WorkflowStepDeletedSuccessfully": "Workflow step deleted successfully", "WorkflowStepRenamedSuccessfully": "Workflow step renamed successfully"}, "PII": {"PIIAddedSuccessfully": "PII added successfully", "PIIEditedSuccessfully": "PII edited successfully", "PIIUpdatedSuccessfully": "PII updated successfully", "PIIDeletedSuccessfully": "PII deleted successfully", "PIIsUpdatedSuccessfully": "PIIs updated successfully"}, "DataCatalogue": {"ServiceAddedSuccessfully": "Service added successfully", "TaskTriggeredSuccessfully": "Task triggered successfully", "CredentialsSavedSuccessfully": "Credentials saved successfully", "FailedToSaveCredentials": "Failed to save credentials. Please check your input and try again."}, "CookieConsent": {"CategorySavedSuccessfully": "Category saved successfully", "ServiceSavedSuccessfully": "Service saved successfully", "CookieSavedSuccessfully": "<PERSON><PERSON> saved successfully", "DomainDetailsUpdatedSuccessfully": "Domain details updated successfully", "CookieCreatedSuccessfully": "<PERSON><PERSON> created successfully"}, "General": {"CopiedToClipboard": "Copied to clipboard!", "URLCopiedToClipboard": "URL copied to clipboard!", "ScanStartedSuccessfully": "<PERSON>an started successfully", "ErrorReportSentSuccessfully": "Error report sent successfully", "DashboardDeletedSuccessfully": "Dashboard deleted successfully", "DashboardUpdatedSuccessfully": "Dashboard updated successfully", "TicketCreatedSuccessfully": "Ticket created successfully", "BusinessUnitUpdatedSuccessfully": "Business unit updated successfully", "DepartmentUpdatedSuccessfully": "Department updated successfully", "ProcessUpdatedSuccessfully": "Process updated successfully", "QuestionAddedSuccessfully": "Question added successfully", "QuestionUpdatedSuccessfully": "Question updated successfully", "QuestionDeletedSuccessfully": "Question deleted successfully", "TextFieldAdded": "Text field added", "DeletedSuccessfully": "Deleted successfully", "Success": "Success!", "RequestApprovedSuccessfully": "Request approved successfully", "RequestRejectedSuccessfully": "Request rejected successfully", "RequestArchivedSuccessfully": "Request archived successfully", "RequestUnarchivedSuccessfully": "Request unarchived successfully", "RequestCompletedSuccessfully": "Request completed successfully", "MailTemplateSentSuccessfully": "Mail template sent successfully", "TemplateDeletedSuccessfully": "Template deleted successfully", "FiltersAppliedSuccessfully": "Filters applied successfully", "RulesAppliedSuccessfully": "Rules applied successfully", "OptionRemovedSuccessfully": "Option removed successfully", "LogoUploadedSuccessfully": "Logo uploaded successfully", "URLCopiedSuccessfully": "URL copied successfully", "FormCreatedSuccessfully": "Form created successfully", "QuestionOrderUpdatedSuccessfully": "Question order updated successfully", "QuestionRemovedSuccessfully": "Question removed successfully", "FormSavedSuccessfully": "Form saved successfully", "FormVersionCreatedSuccessfully": "Form version created successfully", "FailedToFetchControls": "Failed to fetch controls", "RejectMessageCannotBeEmpty": "Reject message cannot be empty", "FailedToSendMailTemplate": "Failed to send mail template", "DataSubjectEmailNotAvailable": "Data subject email not available", "FailedToFetchReviewers": "Failed to fetch reviewers", "AutoScanRequired": "Auto scan is required", "FailedToUpdateCookie": "Failed to update cookie", "FailedToSaveRetentionRule": "Failed to save retention rule", "UploadFilesOrEnterURL": "Upload file(s) or enter URL", "EnterValidURL": "Enter valid URL", "UploadValidFile": "Upload valid file", "EnterTemplateName": "Enter template name", "DuplicateStepsChooseDifferentName": "Duplicate steps. Please choose a different name", "StepAddedSuccessfully": "Step added successfully", "FailedToAddWorkflowStep": "Failed to add workflow's step", "ErrorWhileAddingWorkflow": "Error while adding workflow", "TentativeDateUpdatedSuccessfully": "Tentative date updated successfully", "CouldntUpdateTentativeDate": "Couldn't update tentative date", "PublicURLGenerated": "Public URL generated", "DeadlineExtendedSuccessfully": "Deadline extended successfully", "BusinessUnitUpdated": "Business unit updated", "ActionAddedSuccessfully": "Action added successfully", "MitigationPlanSubmittedSuccessfully": "Mitigation plan submitted successfully", "PlanSavedSuccessfully": "Plan saved successfully", "RegulationAddedSuccessfully": "Regulation added successfully", "RiskCreatedSuccessfully": "Risk created successfully", "ImportedSuccessfully": "Imported successfully", "RecordUpdatedSuccessfully": "Record updated successfully", "PrivacyNoticeSavedSuccessfully": "Privacy notice saved successfully", "ProcessingCategoryUpdatedSuccessfully": "Processing category updated successfully", "ProcessingCategoryDeletedSuccessfully": "Processing category deleted successfully", "PIILabelUpdatedSuccessfully": "PII label updated successfully", "PIILabelDeletedSuccessfully": "PII label deleted successfully", "TemplateActivatedSuccessfully": "Template activated successfully", "TemplateInactivatedSuccessfully": "Template inactivated successfully", "TranslatedDataFetched": "Translated data fetched", "FormTranslated": "Form translated", "GTMSettingsAppliedToAll": "GTM settings applied to all", "PIIAnalysisCompletedSuccessfully": "PII analysis completed successfully", "RetentionRuleSavedSuccessfully": "Retention rule saved successfully", "MappingDeletedSuccessfully": "Mapping deleted successfully", "BreachEvaluationUpdatedSuccessfully": "Breach evaluation updated successfully", "BreachAddedSuccessfully": "Breach added successfully", "BreachResolutionDoneSuccessfully": "Breach resolution done successfully", "PreventiveActionAddedSuccessfully": "Preventive action added successfully", "NotificationSavedSuccessfully": "Notification saved successfully", "CustomerAddedSuccessfully": "Customer added successfully", "FailedToUpdateCustomer": "Failed to update customer", "CustomerUpdatedSuccessfully": "Customer updated successfully", "ResourceAddedSuccessfully": "Resource added successfully", "CollaboratorAddedSuccessfully": "Collaborator added successfully", "PreferencesChangedSuccessfully": "Preferences changed successfully", "AddedSuccessfully": "{{type}} added successfully", "AssessmentStartedSuccessfully": "Assessment Started Successfully", "FailedToSaveForm": "Failed to save form"}}}