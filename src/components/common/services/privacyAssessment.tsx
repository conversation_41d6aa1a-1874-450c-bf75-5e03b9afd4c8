import axios from 'axios';
import Cookies from 'js-cookie';
import httpClient from '../../../api/httpClient';
import { api_key_work as api_key } from '../../../utils/helperData';
import {
  ASSESSMENT_COUNT,
  ASSESSMENT_DATA,
  ASSESSMENT_DETAILS,
  DOC_COUNT,
  DOC_DATA,
  ROPA_COUNT,
  ROPA_DATA,
  ROPA_DETAILS,
  VENDOR_DETAILS,
} from '../api';

export const fetchRopaList = async (search: string) => {
  let url = ROPA_DATA;
  if (search !== '') {
    url += `?search=${search}`;
  }
  try {
    const response = await httpClient.get(url);
    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};

export const fetchRopaStats = async () => {
  try {
    const response = await httpClient.get(ROPA_COUNT);
    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};

export const fetchRopaDetails = async (id: number) => {
  try {
    const response = await httpClient.get(`${ROPA_DETAILS}/${id}`);
    return response;
  } catch (error) {
    throw new Error('Failed to fetch ROPA details.');
  }
};

export const fetchVendorDetails = async (id: number) => {
  try {
    const response = await httpClient.get(`${VENDOR_DETAILS}/${id}`);
    return response;
  } catch (error) {
    throw new Error('Failed to fetch Vendor details.');
  }
};

export const fetchAssessmentDetails = async (id: number) => {
  try {
    const response = await httpClient.get(`${ASSESSMENT_DETAILS}/${id}`);
    return response;
  } catch (error) {
    throw new Error('Failed to fetch Assessment details.');
  }
};

export const fetchAssessmentList = async (search: string) => {
  const access_token = Cookies.get('access_token');
  let url = ASSESSMENT_DATA;
  if (search !== '') {
    url += `?search=${search}`;
  }
  try {
    const response = await axios.get(`${url}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });

    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};
export const fetchAssessmentStats = async () => {
  const access_token = Cookies.get('access_token');
  try {
    const response = await axios.get(`${ASSESSMENT_COUNT}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });

    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};

export const fetchDocumentList = async (search: string) => {
  const access_token = Cookies.get('access_token');
  let url = DOC_DATA;
  if (search !== '') {
    url += `?search=${search}`;
  }
  try {
    const response = await axios.get(`${url}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });

    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};

export const fetchDocumentStats = async () => {
  const access_token = Cookies.get('access_token');
  try {
    const response = await axios.get(`${DOC_COUNT}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });

    return response;
  } catch (error) {
    throw new Error('Failed to fetch data.');
  }
};
