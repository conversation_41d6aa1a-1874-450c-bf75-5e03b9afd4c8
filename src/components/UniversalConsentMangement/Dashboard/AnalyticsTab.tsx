import { AlertTriangle, FileText, Globe, MapPin, Network, TrendingUp } from 'lucide-react';
import React from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Progress } from '../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import { Badge } from '../../../@/components/ui/badge';
import { CardDescription } from '../../../@/components/ui/card';
import { ChartCardLoader } from '../../common/LoadingUI';
import { MultiColorProgress } from '../../common/MulticolorProgress';
import { AnalyticsTabProps } from './DashboardV2';

const AnalyticsTab: React.FC<AnalyticsTabProps> = ({
  common_metrics,
  collection_template_metrics,
  analytics_metrics,
  consent_funnel_analysis_metrics,
  multi_channel_performance_metrics,
  geographical_insight_metrics,
  collectionBuilderDetailedDashboardData,
  isLoadingCollectionBuilderDashboardData,
}) => {
  return (
    <div className="mb-6 space-y-8">
      <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="p-3">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Consent Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {analytics_metrics?.consent_rate?.current_month_value ||
                analytics_metrics?.consent_rate?.current_month_value === 0
                  ? `${analytics_metrics?.consent_rate?.current_month_value}%`
                  : '-'}
              </div>
              <p
                className={`text-xs ${analytics_metrics?.consent_rate?.percentage_change_from_last_month >= 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                {analytics_metrics?.consent_rate?.percentage_change_from_last_month !== null
                  ? `${analytics_metrics?.consent_rate?.percentage_change_from_last_month}% from last month`
                  : ''}
              </p>
            </CardContent>
          </Card>
        )}
        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-48" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="p-3">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Opt-out Rate</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {analytics_metrics?.opt_out_rate?.current_month_value ||
                analytics_metrics?.opt_out_rate?.current_month_value === 0
                  ? `${analytics_metrics?.opt_out_rate?.current_month_value}%`
                  : '-'}
              </div>
              <p
                className={`text-xs ${analytics_metrics?.opt_out_rate?.percentage_change_from_last_month >= 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                {analytics_metrics?.opt_out_rate?.percentage_change_from_last_month !== null
                  ? `${analytics_metrics?.opt_out_rate?.percentage_change_from_last_month}% from last month`
                  : ''}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Consent Funnel Analytics */}
        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-72" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="px-2 py-4">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <TrendingUp className="h-6 w-6 text-primary" />
                Consent Funnel Analysis
              </CardTitle>
              <CardDescription>
                Track user journey through consent collection process
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Form Views</span>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">
                        {consent_funnel_analysis_metrics?.consent_form_view_count !== null
                          ? consent_funnel_analysis_metrics?.consent_form_view_count
                          : '-'}
                      </span>
                    </div>
                  </div>
                  <Progress
                    value={
                      (consent_funnel_analysis_metrics?.consent_form_view_count /
                        (consent_funnel_analysis_metrics?.consent_form_view_count +
                          consent_funnel_analysis_metrics?.consent_form_submission_count)) *
                      100
                    }
                    className="h-3"
                  />{' '}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Form Submission Count</span>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">
                        {consent_funnel_analysis_metrics?.consent_form_submission_count !== null
                          ? consent_funnel_analysis_metrics?.consent_form_submission_count
                          : '-'}
                      </span>
                    </div>
                  </div>
                  <Progress
                    value={
                      (consent_funnel_analysis_metrics?.consent_form_submission_count /
                        (consent_funnel_analysis_metrics?.consent_form_view_count +
                          consent_funnel_analysis_metrics?.consent_form_submission_count)) *
                      100
                    }
                    className="h-3"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Preference Center Views</span>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">
                        {consent_funnel_analysis_metrics?.preference_center_view_count !== null
                          ? consent_funnel_analysis_metrics?.preference_center_view_count
                          : '-'}
                      </span>
                    </div>
                  </div>
                  <Progress
                    value={
                      (consent_funnel_analysis_metrics?.preference_center_view_count /
                        (consent_funnel_analysis_metrics?.preference_center_view_count +
                          consent_funnel_analysis_metrics?.preference_center_submission_count)) *
                      100
                    }
                    className="h-3"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Preference Center Submission Count</span>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">
                        {consent_funnel_analysis_metrics?.preference_center_submission_count !==
                        null
                          ? consent_funnel_analysis_metrics?.preference_center_submission_count
                          : '-'}
                      </span>
                    </div>
                  </div>
                  <Progress
                    value={
                      (consent_funnel_analysis_metrics?.preference_center_submission_count /
                        (consent_funnel_analysis_metrics?.preference_center_view_count +
                          consent_funnel_analysis_metrics?.preference_center_submission_count)) *
                      100
                    }
                    className="h-3"
                  />
                </div>
              </div>

              <div className="mt-6 border-t pt-6">
                <div className="grid grid-cols-1">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {consent_funnel_analysis_metrics?.drop_off_rate
                        ? `${consent_funnel_analysis_metrics?.drop_off_rate}%`
                        : '-'}
                    </div>
                    <div className="text-sm text-muted-foreground">Drop-off Rate</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Channel Performance */}
        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-72" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="px-2 py-4">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <Network className="h-6 w-6 text-primary" />
                Multi-Channel Performance
              </CardTitle>
              <CardDescription>
                Consent collection performance across different channels
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {multi_channel_performance_metrics?.length > 0 ? (
                <div className="custom-scrollbar h-[370px] space-y-6 overflow-y-auto pr-1">
                  <div className="grid grid-cols-1 gap-4">
                    {multi_channel_performance_metrics?.map((metric, index) => (
                      <div className="rounded-lg border p-4">
                        <div className="mb-3 flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Globe className="h-5 w-5 text-blue-600" />
                            <span className="font-medium">{metric.channel_name}</span>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <div className="font-semibold">{metric.total_submissions}</div>
                            <div className="text-muted-foreground">Submissions</div>
                          </div>
                          <div>
                            <div className="font-semibold text-green-600">
                              {metric.consent_rate}%
                            </div>
                            <div className="text-muted-foreground">Consent Rate</div>
                          </div>
                          <div>
                            <div className="font-semibold">{metric.opt_out_rate}%</div>
                            <div className="text-muted-foreground">Opt-Out Rate</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="flex h-[370px] flex-col items-center justify-center p-6 text-center text-gray-500">
                  <h3 className="text-lg font-medium text-gray-700">No Data Available</h3>

                  <p className="mt-1 max-w-xs text-sm text-gray-500">
                    No records to show as no consent has collected so far
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Advanced Analytics */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-72" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="px-2 py-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-5 w-5 text-primary" />
                Geographic Insights
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {geographical_insight_metrics?.length > 0 ? (
                geographical_insight_metrics?.map((metric) => (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                        <span className="text-sm">{metric.continent}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{metric.total_contribution}%</div>
                        <div className="text-xs text-muted-foreground">Contribution</div>
                      </div>
                    </div>
                    <MultiColorProgress
                      segments={[
                        {
                          color: 'bg-blue-600',
                          value: metric?.consent_rate,
                          label: 'Consent Rate',
                        },
                        {
                          color: 'bg-blue-300',
                          value: metric?.drop_out_rate,
                          label: 'Drop-off Rate',
                        },
                      ]}
                    />{' '}
                  </div>
                ))
              ) : (
                <div className="flex h-[370px] flex-col items-center justify-center p-6 text-center text-gray-500">
                  <h3 className="text-lg font-medium text-gray-700">No Data Available</h3>

                  <p className="mt-1 max-w-xs text-sm text-gray-500">
                    No geographic data to display yet.
                  </p>
                </div>
              )}
              {}
            </CardContent>
          </Card>
        )}

        {isLoadingCollectionBuilderDashboardData ? (
          <div className="flex items-center justify-center">
            <ChartCardLoader title="Loading..." height="h-72" chartType="bar" showLegend={true} />
          </div>
        ) : (
          <Card className="px-2 py-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="h-5 w-5 text-primary" />
                Template Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {collectionBuilderDetailedDashboardData?.length > 0 ? (
                <div className="custom-scrollbar h-[370px] space-y-6 overflow-y-auto pr-1">
                  {collectionBuilderDetailedDashboardData?.map((template: any) => (
                    <div className="space-y-4">
                      <div className="rounded-lg border p-3">
                        <div className="mb-4 flex items-center justify-between">
                          <span className="text-sm font-semibold">{template?.template_name}</span>
                          {template.consent_rate > 90 && (
                            <Badge className="bg-green-100 text-green-800">Excellent</Badge>
                          )}
                          {template.consent_rate > 80 && template.consent_rate <= 90 && (
                            <Badge className="bg-white text-black outline outline-1 outline-gray-300">
                              Good
                            </Badge>
                          )}
                          {template.consent_rate > 0 && template.consent_rate <= 70 && (
                            <Badge className="bg-gray-200 text-black">Needs Review</Badge>
                          )}{' '}
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div>
                            <div className="font-semibold">
                              {template?.consent_rate ? `${template?.consent_rate}%` : '-'}
                            </div>
                            <div className="mt-2 text-muted-foreground">Consent Rate</div>
                          </div>
                          <div>
                            <div className="font-semibold">{template?.submission ?? '-'}</div>
                            <div className="mt-2 text-muted-foreground">Submissions</div>
                          </div>
                          <div>
                            <Badge
                              className={`h-4 ${template?.active_status ? 'bg-black text-white' : 'bg-gray-200 text-black'}`}
                            >
                              {template?.active_status ? 'Active' : 'Inactive'}
                            </Badge>
                            <div className="ml-2 mt-2 text-muted-foreground">Status</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex h-[370px] flex-col items-center justify-center p-6 text-center text-gray-500">
                  <h3 className="text-lg font-medium text-gray-700">No Data Available</h3>

                  <p className="mt-1 max-w-xs text-sm text-gray-500">
                    No template performance data to display.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AnalyticsTab;
